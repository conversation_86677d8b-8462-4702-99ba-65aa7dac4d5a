package com.zpay.admin;

import com.zpay.admin.service.StatService;
import generator.service.CardActionLogService;
import generator.service.UserCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class AdminApplicationTests {

    @Autowired
    CardActionLogService cardActionLogService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    StatService statService;


    //    @Test
    void stat() {
//        FundInOutDto data = statService.fundInOut();
//        Console.error(data);
    }
}
