package com.zpay.admin.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.zpay.admin.job.dto.*;
import com.zpay.admin.response.JsonResult;
import com.zpay.admin.service.StatService;
import generator.domain.BusinessDayInOutLog;
import generator.domain.PlatformDayInOutLog;
import generator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static generator.domain.table.BusinessDayInOutLogTableDef.BUSINESS_DAY_IN_OUT_LOG;
import static generator.domain.table.PlatformDayInOutLogTableDef.PLATFORM_DAY_IN_OUT_LOG;

/**
 * <AUTHOR>
 * @updateTime 2024/8/13 17:24
 */
@RestController
@RequestMapping("stat")
@Slf4j
public class Stat {

    @Autowired
    CardActionLogService cardActionLogService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    StatService statService;

    @GetMapping("dashboard")
    public JsonResult<Object> card() {
        StatCardFundDto cardFund = cardActionLogService.statPlatformCardToday();
        StatCardNumDto cardNum = userCardService.statCardPlatformHistory();
        FundInOutDto fund = statService.fundInOut();
        TvlDto tvl = statService.tvl();
        HashMap<String, Object> data = new HashMap<>(4);
        data.put("cardFund", cardFund);
        data.put("cardNum", cardNum);
        data.put("fund", fund);
        data.put("tvl", tvl);
        return JsonResult.ok(data);
    }

    @Autowired
    BusinessDayInOutLogService businessDayInOutLogService;
    @Autowired
    PlatformDayInOutLogService platformDayInOutLogService;
    @Autowired
    CoinInOutService coinInOutService;

    @PostMapping("first-in-out")
    public JsonResult<Object> test(@RequestBody JSONObject body) {
        if (!"Tamia!23".equals(body.get("secret"))) {
            return JsonResult.ko();
        }

        HashMap<String, HashMap<Integer, StatInOutDto>> listCoin = coinInOutService.statOrderByDate();

        if (!listCoin.isEmpty()) {
            for (Map.Entry<String, HashMap<Integer, StatInOutDto>> bObj : listCoin.entrySet()) {
                String statDate = bObj.getKey();
                StatInOutDto platformStatInOutDto = new StatInOutDto();

                for (Map.Entry<Integer, StatInOutDto> entry : bObj.getValue().entrySet()) {
                    Integer businessId = entry.getKey();
                    StatInOutDto dto = entry.getValue();
                    BusinessDayInOutLog businessDayInOutLog = businessDayInOutLogService.queryChain()
                            .from(BUSINESS_DAY_IN_OUT_LOG)
                            .where(BUSINESS_DAY_IN_OUT_LOG.BUSINESS_ID.eq(businessId))
                            .where(BUSINESS_DAY_IN_OUT_LOG.STAT_DATE.eq(statDate))
                            .one();
                    if (businessDayInOutLog == null) {
                        businessDayInOutLog = new BusinessDayInOutLog();
                    }
                    BeanUtils.copyProperties(dto, businessDayInOutLog);
                    businessDayInOutLog.setStatDate(DateUtil.parse(statDate));
                    businessDayInOutLog.setBusinessId(businessId);
                    businessDayInOutLogService.saveOrUpdate(businessDayInOutLog);
                    log.warn("商户冲提流水写入成功 businessId: {}", businessId);

                    platformStatInOutDto.setIn(platformStatInOutDto.getIn().add(dto.getIn()));
                    platformStatInOutDto.setInFee(platformStatInOutDto.getInFee().add(dto.getInFee()));
                    platformStatInOutDto.setOut(platformStatInOutDto.getOut().add(dto.getOut()));
                    platformStatInOutDto.setOutFee(platformStatInOutDto.getOutFee().add(dto.getOutFee()));
                }

                PlatformDayInOutLog platformDayInOutLog = platformDayInOutLogService.queryChain()
                        .from(PLATFORM_DAY_IN_OUT_LOG)
                        .where(PLATFORM_DAY_IN_OUT_LOG.STAT_DATE.eq(statDate))
                        .one();
                if (platformDayInOutLog == null) {
                    platformDayInOutLog = new PlatformDayInOutLog();
                }
                BeanUtils.copyProperties(platformStatInOutDto, platformDayInOutLog);
                platformDayInOutLog.setStatDate(DateUtil.parse(statDate));
                platformDayInOutLog.setTvl(BigDecimal.ZERO);
                platformDayInOutLogService.saveOrUpdate(platformDayInOutLog);
                log.warn("平台冲提流水写入成功");
            }
        }
        return JsonResult.ok(listCoin);
    }
}
