package com.zpay.admin.job.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/8/7 15:33
 */
@Data
public class StatCardFundDto {

    private Integer openCardNum = 0;
    private BigDecimal openCardFeeCost = BigDecimal.ZERO;
    private BigDecimal openCardFeePlatform = BigDecimal.ZERO;
    private BigDecimal openCardFeeBusiness = BigDecimal.ZERO;
    private BigDecimal openCardFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal openCardFeeBusinessEarn = BigDecimal.ZERO;

    private Integer rechargeNum = 0;
    private BigDecimal rechargeFeeCost = BigDecimal.ZERO;
    private BigDecimal rechargeFeePlatform = BigDecimal.ZERO;
    private BigDecimal rechargeFeeBusiness = BigDecimal.ZERO;
    private BigDecimal rechargeFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal rechargeFeeBusinessEarn = BigDecimal.ZERO;

    private Integer cancelCardNum = 0;
    private BigDecimal cancelCardFeeCost = BigDecimal.ZERO;
    private BigDecimal cancelCardFeePlatform = BigDecimal.ZERO;
    private BigDecimal cancelCardFeeBusiness = BigDecimal.ZERO;
    private BigDecimal cancelCardFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal cancelCardFeeBusinessEarn = BigDecimal.ZERO;

    private Integer rechargeCardNum = 0;
    private BigDecimal rechargeCardFeeCost = BigDecimal.ZERO;
    private BigDecimal rechargeCardFeePlatform = BigDecimal.ZERO;
    private BigDecimal rechargeCardFeeBusiness = BigDecimal.ZERO;
    private BigDecimal rechargeCardFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal rechargeCardFeeBusinessEarn = BigDecimal.ZERO;

    private Integer shareCardNum = 0;
    private BigDecimal shareCardFeeCost = BigDecimal.ZERO;
    private BigDecimal shareCardFeePlatform = BigDecimal.ZERO;
    private BigDecimal shareCardFeeBusiness = BigDecimal.ZERO;
    private BigDecimal shareCardFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal shareCardFeeBusinessEarn = BigDecimal.ZERO;

    private Integer physicalCardNum = 0;
    private BigDecimal physicalCardFeeCost = BigDecimal.ZERO;
    private BigDecimal physicalCardFeePlatform = BigDecimal.ZERO;
    private BigDecimal physicalCardFeeBusiness = BigDecimal.ZERO;
    private BigDecimal physicalCardFeePlatformEarn = BigDecimal.ZERO;
    private BigDecimal physicalCardFeeBusinessEarn = BigDecimal.ZERO;
    
    private BigDecimal platformDayDisburse = BigDecimal.ZERO;
    private BigDecimal businessDayDisburse = BigDecimal.ZERO;

    private BigDecimal platformDayEarn = BigDecimal.ZERO;
    private BigDecimal businessDayEarn = BigDecimal.ZERO;

    private BigDecimal platformHistoryDisburse = BigDecimal.ZERO;
    private BigDecimal businessHistoryDisburse = BigDecimal.ZERO;

    private BigDecimal platformHistoryEarn = BigDecimal.ZERO;
    private BigDecimal businessHistoryEarn = BigDecimal.ZERO;

}
