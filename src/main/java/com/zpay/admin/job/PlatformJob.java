package com.zpay.admin.job;

import cn.hutool.core.date.DateUtil;
import com.zpay.admin.job.dto.*;
import com.zpay.admin.service.StatService;
import generator.domain.*;
import generator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.job.handler.EruptJobHandler;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static generator.domain.table.BusinessDayInOutLogTableDef.BUSINESS_DAY_IN_OUT_LOG;
import static generator.domain.table.BusinessDayModuleLogTableDef.BUSINESS_DAY_MODULE_LOG;
import static generator.domain.table.PlatformDayCardLogTableDef.PLATFORM_DAY_CARD_LOG;
import static generator.domain.table.PlatformDayDisburseLogTableDef.PLATFORM_DAY_DISBURSE_LOG;
import static generator.domain.table.PlatformDayEarnLogTableDef.PLATFORM_DAY_EARN_LOG;
import static generator.domain.table.PlatformDayInOutLogTableDef.PLATFORM_DAY_IN_OUT_LOG;
import static generator.domain.table.PlatformDayMoneyLogTableDef.PLATFORM_DAY_MONEY_LOG;
import static generator.domain.table.UserTableDef.USER;

/**
 * <AUTHOR>
 * @updateTime 2024/8/7 11:42
 */
@Service
@Slf4j
public class PlatformJob implements EruptJobHandler {

    @Autowired
    CardActionLogService cardActionLogService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    StatService statService;

    @Autowired
    PlatformDayCardLogService platformDayCardLogService;
    @Autowired
    PlatformDayDisburseLogService platformDayDisburseLogService;
    @Autowired
    PlatformDayEarnLogService platformDayEarnLogService;
    @Autowired
    PlatformDayMoneyLogService platformDayMoneyLogService;

    @Autowired
    UserService userService;
    @Autowired
    BusinessDayModuleLogService businessDayModuleLogService;
    @Autowired
    CoinInOutService coinInOutService;

    @Autowired
    BusinessDayInOutLogService businessDayInOutLogService;
    @Autowired
    PlatformDayInOutLogService platformDayInOutLogService;


    @Override
    public String exec(String code, String param) {

        log.warn("定时任务开始执行，参数：{}", param);
        Date statDate = DateUtil.yesterday();
        String statDateStr = DateUtil.formatDate(statDate);
        log.warn("统计日期：{}", statDateStr);

        StatCardFundDto card = cardActionLogService.statPlatformCard(statDateStr);
        StatCardNumDto cardNum = userCardService.statCardPlatformHistory();
        FundInOutDto fund = statService.fundInOut();
        TvlDto tvl = statService.tvl();

        PlatformDayCardLog platformDayCardLog = platformDayCardLogService.queryChain()
                .from(PLATFORM_DAY_CARD_LOG)
                .where(PLATFORM_DAY_CARD_LOG.STAT_DATE.between(DateUtil.beginOfDay(statDate), DateUtil.endOfDay(statDate)))
                .one();
        if (platformDayCardLog == null) {
            platformDayCardLog = new PlatformDayCardLog();
        }
        platformDayCardLog.setOpenCardNum(card.getOpenCardNum());
        platformDayCardLog.setCancelCardNum(card.getCancelCardNum());
        platformDayCardLog.setHaveCardNum(cardNum.getActiveCardNum());
        platformDayCardLog.setStatDate(statDate);
        platformDayCardLog.setCreateTime(new Date());
        platformDayCardLogService.saveOrUpdate(platformDayCardLog);
        log.warn("平台卡片日志写入成功");

        PlatformDayDisburseLog platformDayDisburseLog = platformDayDisburseLogService.queryChain()
                .from(PLATFORM_DAY_DISBURSE_LOG)
                .where(PLATFORM_DAY_DISBURSE_LOG.STAT_DATE.between(DateUtil.beginOfDay(statDate), DateUtil.endOfDay(statDate)))
                .one();
        if (platformDayDisburseLog == null) {
            platformDayDisburseLog = new PlatformDayDisburseLog();
        }
        platformDayDisburseLog.setOpenCardFee(card.getOpenCardFeeCost());
        platformDayDisburseLog.setRechargeFee(card.getRechargeFeeCost());
        platformDayDisburseLog.setCancelCardFee(card.getCancelCardFeeCost());
        platformDayDisburseLog.setDayDisburse(card.getPlatformDayDisburse());
        platformDayDisburseLog.setHistoryDisburse(card.getPlatformHistoryDisburse());
        platformDayDisburseLog.setStatDate(statDate);
        platformDayDisburseLog.setCreateTime(new Date());
        platformDayDisburseLogService.saveOrUpdate(platformDayDisburseLog);
        log.warn("平台支出日志写入成功");

        PlatformDayEarnLog platformDayEarnLog = platformDayEarnLogService.queryChain()
                .from(PLATFORM_DAY_EARN_LOG)
                .where(PLATFORM_DAY_EARN_LOG.STAT_DATE.between(DateUtil.beginOfDay(statDate), DateUtil.endOfDay(statDate)))
                .one();
        if (platformDayEarnLog == null) {
            platformDayEarnLog = new PlatformDayEarnLog();
        }
        platformDayEarnLog.setOpenCardFeeEarn(card.getOpenCardFeePlatformEarn());
        platformDayEarnLog.setOpenCardFeeTotal(card.getOpenCardFeeBusiness());
        platformDayEarnLog.setRechargeFeeEarn(card.getRechargeFeePlatformEarn());
        platformDayEarnLog.setRechargeFeeTotal(card.getRechargeFeeBusiness());
        platformDayEarnLog.setCancelCardFeeEarn(card.getCancelCardFeePlatformEarn());
        platformDayEarnLog.setCancelCardFeeTotal(card.getCancelCardFeeBusiness());
        platformDayEarnLog.setDayEarn(card.getPlatformDayEarn());
        platformDayEarnLog.setHistoryEarn(card.getPlatformHistoryEarn());
        platformDayEarnLog.setStatDate(statDate);
        platformDayEarnLog.setCreateTime(new Date());
        platformDayEarnLogService.saveOrUpdate(platformDayEarnLog);
        log.warn("平台收入日志写入成功");

        PlatformDayMoneyLog platformDayMoneyLog = platformDayMoneyLogService.queryChain()
                .from(PLATFORM_DAY_MONEY_LOG)
                .where(PLATFORM_DAY_MONEY_LOG.STAT_DATE.between(DateUtil.beginOfDay(statDate), DateUtil.endOfDay(statDate)))
                .one();
        if (platformDayMoneyLog == null) {
            platformDayMoneyLog = new PlatformDayMoneyLog();
        }
        platformDayMoneyLog.setTotalRecharge(fund.getTodayDeposit());
        platformDayMoneyLog.setTotalWithdraw(fund.getTodayWithdraw());
        platformDayMoneyLog.setTotalEarn(card.getPlatformDayEarn());
        platformDayMoneyLog.setTotalBalance(tvl.getMainTvl());
        platformDayMoneyLog.setStatDate(statDate);
        platformDayMoneyLog.setCreateTime(new Date());
        platformDayMoneyLogService.saveOrUpdate(platformDayMoneyLog);
        log.warn("平台资金日志写入成功");

        StatInOutDto platformStatInOutDto = new StatInOutDto();
        HashMap<Integer, StatInOutDto> listCoin = coinInOutService.stat(statDateStr);
        if (!listCoin.isEmpty()) {
            for (Map.Entry<Integer, StatInOutDto> entry : listCoin.entrySet()) {
                Integer businessId = entry.getKey();
                StatInOutDto dto = entry.getValue();
                BusinessDayInOutLog businessDayInOutLog = businessDayInOutLogService.queryChain()
                        .from(BUSINESS_DAY_IN_OUT_LOG)
                        .where(BUSINESS_DAY_IN_OUT_LOG.BUSINESS_ID.eq(businessId))
                        .where(BUSINESS_DAY_IN_OUT_LOG.STAT_DATE.eq(statDateStr))
                        .one();
                if (businessDayInOutLog == null) {
                    businessDayInOutLog = new BusinessDayInOutLog();
                }
                BeanUtils.copyProperties(dto, businessDayInOutLog);
                businessDayInOutLog.setStatDate(statDate);
                businessDayInOutLog.setBusinessId(businessId);
                businessDayInOutLogService.saveOrUpdate(businessDayInOutLog);
                log.warn("商户冲提流水写入成功 businessId: {}", businessId);

                platformStatInOutDto.setIn(platformStatInOutDto.getIn().add(dto.getIn()));
                platformStatInOutDto.setInFee(platformStatInOutDto.getInFee().add(dto.getInFee()));
                platformStatInOutDto.setOut(platformStatInOutDto.getOut().add(dto.getOut()));
                platformStatInOutDto.setOutFee(platformStatInOutDto.getOutFee().add(dto.getOutFee()));
            }

            PlatformDayInOutLog platformDayInOutLog = platformDayInOutLogService.queryChain()
                    .from(PLATFORM_DAY_IN_OUT_LOG)
                    .where(PLATFORM_DAY_IN_OUT_LOG.STAT_DATE.eq(statDateStr))
                    .one();
            if (platformDayInOutLog == null) {
                platformDayInOutLog = new PlatformDayInOutLog();
            }
            BeanUtils.copyProperties(platformStatInOutDto, platformDayInOutLog);
            platformDayInOutLog.setStatDate(statDate);
            platformDayInOutLog.setTvl(tvl.getMainTvl());
            platformDayInOutLogService.saveOrUpdate(platformDayInOutLog);
            log.warn("平台冲提流水写入成功");
        }

        List<User> userList = userService.queryChain()
                .select(USER.BUSINESS_ID)
                .from(USER)
                .where(USER.BUSINESS_STATUS.eq(1))
                .list();
        log.warn("开始记录商户钱包统计日志");
        for (User user : userList) {
            log.warn("记录钱包统计日志，商户ID: {}", user.getBusinessId());
            Integer businessId = user.getBusinessId();
            HashMap<String, StatWalletDto> statWalletList = statService.statWallet(statDateStr, businessId);
            StatCardFundDto statCardFundDto = cardActionLogService.statCard(statDateStr, businessId);

            for (Map.Entry<String, StatWalletDto> statWallet : statWalletList.entrySet()) {
                String walletType = statWallet.getKey();
                StatWalletDto statWalletDto = statWallet.getValue();
                BusinessDayModuleLog moduleLog = businessDayModuleLogService.queryChain()
                        .from(BUSINESS_DAY_MODULE_LOG)
                        .where(BUSINESS_DAY_MODULE_LOG.BUSINESS_ID.eq(user.getBusinessId()))
                        .where(BUSINESS_DAY_MODULE_LOG.STAT_DATE.between(DateUtil.beginOfDay(statDate), DateUtil.endOfDay(statDate)))
                        .where(BUSINESS_DAY_MODULE_LOG.WALLET_TYPE.eq(walletType))
                        .one();
                if (moduleLog == null) {
                    moduleLog = new BusinessDayModuleLog();
                }
                BeanUtils.copyProperties(statWalletDto, moduleLog);
                moduleLog.setWalletType(walletType);
                moduleLog.setStatDate(statDate);
                moduleLog.setBusinessId(user.getBusinessId().toString());
                switch (walletType) {
                    case "share":
                        moduleLog.setConsume(statCardFundDto.getShareCardFeeBusiness());
                        moduleLog.setDisburse(statCardFundDto.getShareCardFeePlatform());
                        moduleLog.setBusinessEarn(statCardFundDto.getShareCardFeeBusinessEarn());
                        moduleLog.setPlatformEarn(statCardFundDto.getShareCardFeePlatformEarn());
                        break;
                    case "store":
                        moduleLog.setConsume(statCardFundDto.getRechargeCardFeeBusiness());
                        moduleLog.setDisburse(statCardFundDto.getRechargeCardFeePlatform());
                        moduleLog.setBusinessEarn(statCardFundDto.getRechargeCardFeeBusinessEarn());
                        moduleLog.setPlatformEarn(statCardFundDto.getRechargeCardFeePlatformEarn());
                        break;
                    case "physical":
                        moduleLog.setConsume(statCardFundDto.getPhysicalCardFeeBusiness());
                        moduleLog.setDisburse(statCardFundDto.getPhysicalCardFeePlatform());
                        moduleLog.setBusinessEarn(statCardFundDto.getPhysicalCardFeeBusinessEarn());
                        moduleLog.setPlatformEarn(statCardFundDto.getPhysicalCardFeePlatformEarn());
                        break;
                    default:
                        break;
                }
                businessDayModuleLogService.saveOrUpdate(moduleLog);
            }
        }

        log.warn("任务执行完成");
        return null;
    }
}
