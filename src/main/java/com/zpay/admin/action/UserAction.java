package com.zpay.admin.action;

import com.zpay.admin.dao.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @updateTime 2023/12/13 18:10
 */
@Service
public class UserAction {

    @Autowired
    private UserRepository userRepo;

    public Map<String, Object> putBusinessInfo(Integer uid, Map<String, Object> map) {

        Optional<com.zpay.admin.model.User> user = userRepo.findById(uid.longValue());

        map.put("businessId", user.get().getBusinessId());
        map.put("businessName", user.get().getBusinessName());
        map.put("businessNo", user.get().getBusinessNo());
        return map;
    }
}
