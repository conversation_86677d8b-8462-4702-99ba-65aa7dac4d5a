package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2024/7/26 11:51
 */
@Erupt(
        name = "tb_payment_pay_config",
        power = @Power()
)
@Table(name = "tb_payment_pay_config")   //数据库表名
@Entity
public class PaymentPayConfig extends BaseModel {

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "二字码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "二字码",
                    search = @Search
            )
    )
    private String twoLetterCode;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "国家",
                    desc = "国家/地区",
                    width = "120"
            ),
            edit = @Edit(
                    title = "国家",
                    desc = "国家/地区",
                    search = @Search
            )
    )
    private String countryRegion;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "产品类型",
                    width = "120"
            ),
            edit = @Edit(
                    title = "产品类型"
            )
    )
    private String productType;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "支付产品",
                    width = "120"
            ),
            edit = @Edit(
                    title = "支付产品"
            )
    )
    private String paymentProduct;

    //    @Lob
//    @EruptField(
//            views = @View(
//                    title = "当地市场地位",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "当地市场地位"
//            )
//    )
    private String localMarketPosition;

    @Column(length = 10)
    @EruptField(
            views = @View(
                    title = "交易币种",
                    width = "120"
            ),
            edit = @Edit(
                    title = "交易币种"
            )
    )
    private String transactionCurrency;

    @Column(length = 10)
    @EruptField(
            views = @View(
                    title = "结算币种",
                    width = "120"
            ),
            edit = @Edit(
                    title = "结算币种"
            )
    )
    private String settlementCurrency;

    @Column(precision = 10, scale = 2)
    @EruptField(
            views = @View(
                    title = "单笔手续费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "单笔手续费"
            )
    )
    private Float totalFee;

    @Column(precision = 5, scale = 2)
    @EruptField(
            views = @View(
                    title = "手续费率",
                    width = "120"
            ),
            edit = @Edit(
                    title = "手续费率"
            )
    )
    private Float feeRate;

    @EruptField(
            views = @View(
                    title = "结算周期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "结算周期"
            )
    )
    private Integer settlementCycle;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "fx",
                    width = "120"
            ),
            edit = @Edit(
                    title = "fx"
            )
    )
    private String fx;

    @Column(length = 50)
    @EruptField(
            views = @View(
                    title = "渠道",
                    width = "120"
            ),
            edit = @Edit(
                    title = "渠道"
            )
    )
    private String channel;

    //    @EruptField(
//            views = @View(
//                    title = "create_time",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "create_time",
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE)
//            )
//    )
    private Date createTime;

    //    @EruptField(
//            views = @View(
//                    title = "update_time",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "update_time",
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE)
//            )
//    )
    private Date updateTime;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "状态",
                    width = "120"
            ),
            edit = @Edit(
                    title = "状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "1", label = "启用"),
                            @VL(value = "2", label = "禁用"),
                    })
            )
    )
    private String status;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "国家代码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "国家代码"
            )
    )
    private String englishNameCountry;


}

