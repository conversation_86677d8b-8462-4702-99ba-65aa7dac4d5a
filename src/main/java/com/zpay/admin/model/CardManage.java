package com.zpay.admin.model;

import com.zpay.admin.handler.CardManageDataProxy;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Erupt(name = "卡片类型", dataProxy = CardManageDataProxy.class)
@DynamicUpdate
@Table(name = "tb_user_card_manage")
@Entity
@Getter
@Setter
public class CardManage extends MetaModel {
    
    private String cardSource;

    @EruptField(
            views = @View(
                    title = "卡片来源", width = "120"
            ),
            edit = @Edit(
                    title = "卡片来源",
                    search = @Search(),
                    readonly = @Readonly(add = false),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"SELECT id, CONCAT(source, '-->', product_name) as name FROM tb_user_card_category_config"}
                    ),
                    notNull = true
            )
    )
    private Integer cardCategory;

    @EruptField(
            views = @View(
                    title = "卡片名称",
                    width = "100"
            ),
            edit = @Edit(
                    title = "卡片名称",
                    notNull = true,
                    search = @Search
            )
    )
    private String cardName;

    @EruptField(
            views = @View(
                    title = "卡归属", width = "80"
            ),
            edit = @Edit(
                    title = "卡归属", type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "Master", value = "Master"),
                                    @VL(label = "VISA", value = "VISA"),
                                    @VL(label = "Discover", value = "Discover"),
                                    @VL(label = "银联", value = "UnionPay"),
                            }
                    ),
                    notNull = true,
                    search = @Search
            )
    )
    private String cardBelongTo;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "开卡费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "开卡费",
                    notNull = true
            )
    )
    private Float cardOpenFee;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "划转手续费%",
                    width = "60"
            ),
            edit = @Edit(
                    title = "划转手续费%",
                    notNull = true
            )
    )
    private Float cardChargeRate;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "销卡费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "销卡费",
                    notNull = true
            )
    )
    private Float cardCancelFee;

    private String cardLei;

    @EruptField(
            views = @View(
                    title = "卡模式",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡模式",
                    readonly = @Readonly(add = false),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "充值卡", value = "recharge"),
                                    @VL(label = "共享卡", value = "share"),
                                    @VL(label = "实体卡", value = "physical")
                            }
                    ),
                    notNull = true,
                    search = @Search
            )
    )
    private String cardModel;

    @EruptField(
            edit = @Edit(
                    title = "卡段状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "启用", value = "1"),
                                    @VL(label = "禁用", value = "0"),
                            }
                    ),
                    notNull = true,
                    search = @Search
            )
    )
    private String cardStatus;

    @EruptField(
            views = @View(
                    title = "应用场景",
                    width = "140"
            ),
            edit = @Edit(
                    title = "应用场景",
                    desc = "多个值请用英文 , 分割",
                    type = EditType.TEXTAREA
            ),
            sort = 99999
    )
    private String cardChangjing;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "发卡国家",
                    width = "80"
            ),
            edit = @Edit(
                    title = "发卡国家"
            )
    )
    private String cardSourceCountry;

    @Column(length = 255, unique = true)
    @EruptField(
            views = @View(
                    title = "卡段编号",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡段编号"
            )
    )
    private String cardDuan;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "卡月费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡月费"
            )
    )
    private Float cardMonthFee;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "卡单次消费上限",
//                    desc = "卡单次消费上限（USD）",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡单次消费上限"
//                    desc = "卡单次消费上限（USD）"
            )
    )
    private Float cardDanciXiaofeiMax;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "卡月消费上限",
//                    desc = "卡月消费上限（USD）",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡月消费上限"
//                    desc = "卡月消费上限（USD）"
            )
    )
    private Float cardMonthXiaofeiMax;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "卡储值上限",
//                    desc = "卡储值上限（USD）",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡储值上限"
//                    desc = "卡储值上限（USD）"
            )
    )
    private Float cardChuzhiMax;

    @EruptField(
            edit = @Edit(
                    title = "卡背图",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE)
            )
    )
    private String cardImg;

}
