package com.zpay.admin.model;


import com.zpay.admin.handler.CardApplyCheckDataProxy;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@DynamicUpdate
@DynamicInsert
@Erupt(
        name = "tb_card_apply_order",
        power = @Power(add = false, delete = false),
        orderBy = "CardApplyCheck.id desc",
        filter = @Filter("CardApplyCheck.status = 0"),
        dataProxy = CardApplyCheckDataProxy.class
)
@Table(name = "tb_card_apply_order")   //数据库表名
@Entity
@Data
public class CardApplyCheck extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly()
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;

    @Transient
    @EruptField(
            views = @View(
                    title = "持卡人中文名",
                    width = "80"
            )
    )
    private String chineseName;

    @Transient
    @EruptField(
            views = @View(
                    title = "持卡人英文名",
                    width = "80"
            )
    )
    private String englishName;

    @Transient
    @EruptField(
            views = @View(
                    title = "邮寄地址",
                    width = "80"
            )
    )
    private String address;

    @Transient
    @EruptField(
            views = @View(
                    title = "收件人",
                    width = "80"
            )
    )
    private String userName;

    @Transient
    @EruptField(
            views = @View(
                    title = "收件电话",
                    width = "80"
            )
    )
    private String phone;

    //    @Column(length = 25, nullable = false, unique = true)
//    @EruptField(
//            views = @View(
//                    title = "申请单号",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "申请单号",
//                    notNull = true,
//                    search = @Search,
//                    readonly = @Readonly()
//            )
//    )
    private String orderId;

    @Column(length = 100, nullable = false)
    @EruptField(
            views = @View(
                    title = "卡片ID",
                    width = "120",
                    show = false
            ),
            edit = @Edit(
                    title = "卡片ID",
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly()
            )
    )
    private String cardId;

    @Column(length = 100, nullable = false)
    @EruptField(
            views = @View(
                    title = "卡号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡号",
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly()
            )
    )
    private String cardNumber;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "操作类型",
                    width = "60"
            ),
            edit = @Edit(
                    title = "操作类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    //1 开卡 2 充值 3 冻结 4 解冻 5 挂失 6 解挂 7 换卡 8 销卡
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "1", label = "开卡"),
                            @VL(value = "2", label = "充值"),
                            @VL(value = "3", label = "冻结"),
                            @VL(value = "4", label = "解冻"),
                            @VL(value = "5", label = "挂失"),
                            @VL(value = "6", label = "解挂"),
                            @VL(value = "7", label = "换卡"),
                            @VL(value = "8", label = "销卡"),
                    }),
                    search = @Search,
                    readonly = @Readonly()
            )
    )
    private Integer action;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "金额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "金额",
                    notNull = true,
                    readonly = @Readonly()
            )
    )
    private Float amount;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "手续费",
                    notNull = true,
                    readonly = @Readonly()
            )
    )
    private Float fee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "处理状态",
//                    desc = "0 待处理 1 处理中 2 驳回请求 3 处理成功 4 处理失败",
                    width = "60"
            ),
            edit = @Edit(
                    title = "处理状态",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "0", label = "待处理", disable = true),
                            @VL(value = "1", label = "处理中", disable = true),
                            @VL(value = "2", label = "驳回请求"),
                            @VL(value = "3", label = "处理成功"),
                            @VL(value = "4", label = "处理失败"),
                            @VL(value = "5", label = "提交 FTP"),
                    }),
                    search = @Search
            )
    )
    private Integer status;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "备注",
                    width = "120"
            ),
            edit = @Edit(
                    title = "备注"
            )
    )
    private String note;

    //    @Column(length = 255, nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "渠道",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "渠道",
//                    notNull = true,
//                    readonly = @Readonly()
//            )
//    )
    private String channel;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "申请时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "申请时间",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    readonly = @Readonly()
            )
    )
    private Date createTime;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "更新时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "更新时间",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    readonly = @Readonly()
            )
    )
    private Date updateTime;


}
