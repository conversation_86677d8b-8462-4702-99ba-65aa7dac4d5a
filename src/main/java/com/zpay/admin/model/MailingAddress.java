package com.zpay.admin.model;


import com.zpay.admin.handler.MailingAddressDataProxy;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


@Erupt(
        name = "tb_mailing_address",
        orderBy = "MailingAddress.id desc",
        power = @Power(add = false, edit = false, delete = false),
        dataProxy = MailingAddressDataProxy.class
)
@Table(name = "tb_mailing_address")   //数据库表名
@Entity
@Data
public class MailingAddress extends BaseModel {

    //    @EruptField(
//            views = @View(
//                    title = "kyc",
//                    desc = "kyc id",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "kyc",
//                    desc = "kyc id",
//                    search = @Search
//            )
//    )
    private Integer holderId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    show = false,
                    title = "卡片ID",
                    width = "100"
            ),
            edit = @Edit(
                    title = "卡片ID"
            )
    )
    private String cardId;

    @Transient
    @EruptField(
            views = @View(
                    title = "卡号",
                    width = "100"
            ),
            edit = @Edit(
                    title = "卡号", show = false,
                    search = @Search(vague = true)
            )
    )
    private String cardNumber;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "区号",
                    width = "50"
            ),
            edit = @Edit(
                    title = "区号"
            )
    )
    private String nationCode;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "国家",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "国家"
//            )
//    )
    private String country;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "电话",
                    width = "100"
            ),
            edit = @Edit(
                    title = "电话"
            )
    )
    private String phone;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "收件人",
                    width = "100"
            ),
            edit = @Edit(
                    title = "收件人"
            )
    )
    private String userName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "邮编",
                    width = "60"
            ),
            edit = @Edit(
                    title = "邮编"
            )
    )
    private String postcode;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "详细地址",
                    width = "140"
            ),
            edit = @Edit(
                    title = "详细地址"
            )
    )
    private String address;

    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "120",
                    show = false
            ),
            edit = @Edit(
                    title = "创建时间", show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    private Date updateTime;


}

