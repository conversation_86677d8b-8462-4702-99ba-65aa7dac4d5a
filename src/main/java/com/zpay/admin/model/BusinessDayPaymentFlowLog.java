package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Erupt(
        name = "代付流水",
        power = @Power(add = false, edit = false, delete = false)
)
@Table(name = "tb_business_day_payment_flow_log")   //数据库表名
@Entity
public class BusinessDayPaymentFlowLog extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "今日支出",
                    width = "80"
            ),
            edit = @Edit(
                    title = "今日支出",
                    notNull = true
            )
    )
    private Integer dayExpend;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "今日收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "今日收益",
                    notNull = true
            )
    )
    private Integer dayEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "用户总到账金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "用户总到账金额",
                    notNull = true
            )
    )
    private Integer dayUserArrivalAmount;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "今日成功数",
                    width = "80"
            ),
            edit = @Edit(
                    title = "今日成功数",
                    notNull = true
            )
    )
    private Integer daySucceedSum;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "历史总支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "历史总支出",
                    notNull = true
            )
    )
    private Integer historyExpend;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "历史总收益",
                    width = "120"
            ),
            edit = @Edit(
                    title = "历史总收益",
                    notNull = true
            )
    )
    private Integer historyEarnings;

    private Date createTime;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            )
    )
    private Date statDate;


}
