package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2023/12/13 14:48
 */

@Erupt(
        name = "支出流水",
        power = @Power(export = true, add = false, edit = false, delete = false),
        orderBy = "DisburseLog.id desc"
)
@Table(name = "tb_platform_day_disburse_log")
@Entity
public class DisburseLog extends BaseModel {

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "总开卡费支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总开卡费支出"
            )
    )
    private Float openCardFee;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "充值手续费支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "充值手续费支出"
            )
    )
    private Float rechargeFee;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "用户销卡支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "用户销卡支出"
            )
    )
    private Float cancelCardFee;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日支出"
            )
    )
    private Float dayDisburse;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "历史总支出",
                    width = "120"
            ),
            edit = @Edit(
                    title = "历史总支出"
            )
    )
    private Float historyDisburse;

    private Date createTime;

    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    search = @Search(),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date statDate;

}

