package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2023/12/13 14:21
 */
@Erupt(
        name = "收入流水",
        power = @Power(export = true, add = false, edit = false, delete = false),
        orderBy = "EarnLog.id desc"
)
@Table(name = "tb_platform_day_earn_log")
@Entity
public class EarnLog extends BaseModel {
    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日开卡费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日开卡费"
            )
    )
    private Float openCardFeeTotal;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日开卡费收益",
                    width = "120"
            )
    )
    private Float openCardFeeEarn;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日充值手续费",
                    width = "120"
            )
    )
    private Float rechargeFeeTotal;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日充值手续费收益",
                    width = "120"
            )
    )
    private Float rechargeFeeEarn;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "总销卡费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总销卡费"
            )
    )
    private Float cancelCardFeeTotal;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "销卡费收益",
                    width = "120"
            ),
            edit = @Edit(
                    title = "销卡费收益"
            )
    )
    private Float cancelCardFeeEarn;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日总收益",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日总收益"
            )
    )
    private Float dayEarn;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "历史总收益",
                    width = "120"
            ),
            edit = @Edit(
                    title = "历史总收益"
            )
    )
    private Float historyEarn;

    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    type = EditType.DATE,
                    search = @Search,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date statDate;

    private Date createTime;


}

