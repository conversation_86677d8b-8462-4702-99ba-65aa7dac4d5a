package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Erupt(
        name = "卡片流水",
        power = @Power(export = true, add = false, edit = false, delete = false),
        orderBy = "CardLog.id desc"
)
@Table(name = "tb_platform_day_card_log")
@Entity
public class CardLog extends BaseModel {

    @EruptField(
            views = @View(
                    title = "当日开卡",
                    width = "120"
            ),
            edit = @Edit(
                    title = "当日开卡"
            )
    )
    private Integer openCardNum;

    @EruptField(
            views = @View(
                    title = "当日销卡",
                    width = "120"
            ),
            edit = @Edit(
                    title = "当日销卡"
            )
    )
    private Integer cancelCardNum;

    @EruptField(
            views = @View(
                    title = "总活跃卡",
                    desc = "当天平台共计有多少张已激活可使用的卡片",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总活跃卡",
                    desc = "当天平台共计有多少张已激活可使用的卡片"
            )
    )
    private Integer haveCardNum;

    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    search = @Search,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date statDate;
    private Date createTime;

}

