package com.zpay.admin.model;

/**
 * <AUTHOR>
 * @updateTime 2024/7/24 16:57
 */

import com.zpay.admin.handler.CardHolderCheckDataProxy;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


@Erupt(
        name = "tb_card_holder",
        power = @Power(delete = false),
        orderBy = "CardHolderCheck.status asc,id desc",
        filter = @Filter("CardHolderCheck.status = 0"),
        dataProxy = CardHolderCheckDataProxy.class
)
@Table(name = "tb_card_holder")   //数据库表名
@Entity
@Data
public class CardHolderCheck extends BaseModel {

    //    @EruptField(
//            views = @View(
//                    title = "所属商户",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "所属商户",
//                    search = @Search
//            )
//    )
    private Integer userId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "英文名字",
//                    desc = "英文名字/拼音",
                    width = "120"
            ),
            edit = @Edit(
                    title = "英文名字",
                    notNull = true,
                    search = @Search
            )
    )
    private String englishName;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "中文名字",
                    width = "120"
            ),
            edit = @Edit(
                    title = "中文名字",
                    notNull = true,
                    search = @Search
            )
    )
    private String chineseName;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "出生日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "出生日期",
                    notNull = true
            )
    )
    private String birthday;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "邮箱",
                    width = "120"
            ),
            edit = @Edit(
                    title = "邮箱",
                    notNull = true
            )
    )
    private String email;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "电话",
                    width = "120"
            ),
            edit = @Edit(
                    title = "电话",
                    notNull = true
            )
    )
    private String phone;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "证件类型",
                    width = "120"
            ),
            edit = @Edit(
                    title = "证件类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "00", label = "身份证"),
                            @VL(value = "02", label = "护照")
                    })
            )
    )
    private String documentsType;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "证件号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "证件号",
                    notNull = true
            )
    )
    private String idNumber;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "证件有效期开始日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "证件有效期开始日期",
                    notNull = true
            )
    )
    private String dateOfIssue;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "详细地址",
                    width = "120"
            ),
            edit = @Edit(
                    title = "详细地址",
                    notNull = true
            )
    )
    private String mailingAddress;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "证件有效期结束日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "证件有效期结束日期",
                    notNull = true
            )
    )
    private String dateOfExpiry;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "审核状态",
//                    desc = "\n\n 0未审核/ 1审核通过/ 2 已驳回\n\n",
                    width = "120"
            ),
            edit = @Edit(
                    title = "审核状态",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "0", label = "未审核", disable = true),
                            @VL(value = "3", label = "后台审核通过", disable = true),
                            @VL(value = "1", label = "审核通过"),
                            @VL(value = "2", label = "已驳回"),
                    })
            )
    )
    private Integer status;


    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "国籍",
                    width = "120"
            ),
            edit = @Edit(
                    title = "国籍"
            )
    )
    private String nationality;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "区号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "区号"
            )
    )
    private String nationCode;

    @EruptField(
            views = @View(
                    title = "性别",
//                    desc = "性别：M 男/F 女",
                    width = "120"
            ),
            edit = @Edit(
                    title = "性别",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "M", label = "男"),
                            @VL(value = "F", label = "女")
                    })
            )
    )
    private String sex;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "职业",
                    width = "120"
            ),
            edit = @Edit(
                    title = "职业"
            )
    )
    private String profession;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "职位",
                    width = "120"
            ),
            edit = @Edit(
                    title = "职位"
            )
    )
    private String posts;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "开户目的",
                    width = "120"
            ),
            edit = @Edit(
                    title = "开户目的"
            )
    )
    private String purpose;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "证件照图片",
                    width = "120"
            ),
            edit = @Edit(
                    title = "证件照图片",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE, maxLimit = 2)
            )
    )
    private String certificateImage;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "签名图片",
                    width = "120"
            ),
            edit = @Edit(
                    title = "签名图片",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE)
            )
    )
    private String signatureImage;


    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "创建时间",
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @EruptField(
            views = @View(
                    title = "更新时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "更新时间",
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date updateTime;


}
