package com.zpay.admin.model;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Erupt(
        name = "tb_platform_day_in_out_log",
        power = @Power(add = false, edit = false, delete = false),
        orderBy = "PlatformDayInOutLog.statDate asc"
)
@Table(name = "tb_platform_day_in_out_log")   //数据库表名
@Entity
public class PlatformDayInOutLog extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            )
    )
    private Date statDate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "总充值",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总充值",
                    notNull = true
            )
    )
    private Integer in;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "总提现",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总提现",
                    notNull = true
            )
    )
    private Integer out;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "总充值手续费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总充值手续费",
                    notNull = true
            )
    )
    private Integer inFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "总提现手续费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "总提现手续费",
                    notNull = true
            )
    )
    private Integer outFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台资金",
                    width = "120"
            ),
            edit = @Edit(
                    title = "平台资金",
                    notNull = true
            )
    )
    private Integer tvl;


}
