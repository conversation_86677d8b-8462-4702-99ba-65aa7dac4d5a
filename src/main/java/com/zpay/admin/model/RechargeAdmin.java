package com.zpay.admin.model;

/**
 * <AUTHOR>
 * @updateTime 2024/2/23 15:21
 */

import com.zpay.admin.handler.RechargeAdminDataProxy;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.MetaModelCreateVo;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

@Erupt(
        name = "recharge_admin",
        power = @Power(delete = false, edit = false),
        orderBy = "RechargeAdmin.createTime desc",
        dataProxy = RechargeAdminDataProxy.class
)
@Table(name = "tb_recharge_admin")
@Entity
@Data
public class RechargeAdmin extends MetaModelCreateVo {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = " 80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "钱包类型",
                    width = "100"
            ),
            edit = @Edit(
                    title = "钱包类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "main", label = "主账户钱包"),
                            @VL(value = "store", label = "储值卡钱包"),
                            @VL(value = "share", label = "共享卡钱包"),
                            @VL(value = "physical", label = "实体卡钱包"),
                            @VL(value = "payment", label = "代付钱包"),
                            @VL(value = "usdt", label = "USDT钱包"),
//                            @VL(value = "token", label = "代币钱包"),
                    }),
                    search = @Search
            )
    )
    private String walletType;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "充值金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "充值金额",
                    notNull = true
            )
    )
    private BigDecimal money;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "充值原因",
                    width = "120"
            ),
            edit = @Edit(
                    title = "充值原因",
                    notNull = true
            )
    )
    private String note;

}

