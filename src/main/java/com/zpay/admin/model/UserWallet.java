package com.zpay.admin.model;


import com.zpay.admin.handler.UserWalletDataProxy;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


@Erupt(
        name = "tb_user_wallet",
        power = @Power(delete = false, add = false, edit = false),
        dataProxy = UserWalletDataProxy.class
)
@Table(name = "tb_user_wallet")
@Entity
public class UserWallet extends BaseModel {

    private Integer userId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "主账户钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "主账户钱包",
                    notNull = true
            )
    )
    private Integer mainWallet;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "储值卡钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "储值卡钱包",
                    notNull = true
            )
    )
    private Integer storeWallet;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "共享卡钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "共享卡钱包",
                    notNull = true
            )
    )
    private Integer shareWallet;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "实体卡钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "实体卡钱包",
                    notNull = true
            )
    )
    private Integer physicalWallet;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "代付钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "代付钱包",
                    notNull = true
            )
    )
    private Integer paymentWallet;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "U本位钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "U本位钱包",
                    notNull = true
            )
    )
    private Float usdtWallet;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "代币钱包",
                    width = "120"
            ),
            edit = @Edit(
                    title = "代币钱包",
                    notNull = true
            )
    )
    private Float tokenWallet;


}

