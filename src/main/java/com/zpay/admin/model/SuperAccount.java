package com.zpay.admin.model;


import xyz.erupt.annotation.*;
import xyz.erupt.annotation.sub_erupt.*;
import xyz.erupt.annotation.sub_field.*;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.upms.model.base.HyperModel;
import xyz.erupt.jpa.model.BaseModel;

import java.util.*;
import java.util.Date;
import javax.persistence.*;

import org.hibernate.annotations.GenericGenerator;


/**
 * <AUTHOR>
 * @updateTime 2024/7/15 17:56
 */
@Erupt(
        name = "tb_super_account",
        power = @Power(edit = false)
)
@Table(name = "tb_super_account")   //数据库表名
@Entity
public class SuperAccount extends BaseModel {

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "账户",
                    width = "120"
            ),
            edit = @Edit(
                    title = "email",
                    notNull = true
            )
    )
    private String email;

    @Column(length = 32, nullable = false)
    @EruptField(
            views = @View(
                    title = "password",
                    width = "120"
            ),
            edit = @Edit(
                    title = "password",
                    notNull = true
            )
    )
    private String password;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "code",
                    width = "120"
            ),
            edit = @Edit(
                    title = "code",
                    notNull = true
            )
    )
    private String code;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "create_time",
                    width = "120"
            ),
            edit = @Edit(
                    title = "create_time",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search
            )
    )
    private Date createTime;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "expire_time",
                    width = "120"
            ),
            edit = @Edit(
                    title = "expire_time",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date expireTime;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "created_by",
                    width = "120"
            ),
            edit = @Edit(
                    title = "created_by",
                    notNull = true
            )
    )
    private String createdBy;


}

