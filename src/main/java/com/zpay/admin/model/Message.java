package com.zpay.admin.model;

import com.zpay.admin.handler.MessageDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.upms.helper.HyperModelCreatorVo;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Erupt(name = "公告列表", dataProxy = MessageDataProxy.class)
@Table(name = "tb_message")
@Entity
@Getter
@Setter
public class Message extends BaseModel {

    @EruptField(
            views = @View(title = "语言"),
            edit = @Edit(title = "语言", type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "中文", value = "1"),
                                    @VL(label = "英文", value = "2")
                            }
                    ))
    )
    private Integer lang;

    @EruptField(
            views = @View(title = "标题"),
            edit = @Edit(title = "标题", search = @Search())
    )
    private String title;

    @EruptField(
            views = @View(title = "内容", show = false),
            edit = @Edit(title = "内容", type = EditType.TEXTAREA)
    )
    private String content;

    @EruptField(
            views = @View(title = "显示状态"),
            edit = @Edit(title = "显示状态",
                    search = @Search(),
                    boolType = @BoolType(trueText = "显示", falseText = "隐藏")
            )
    )
    private Boolean status;


    @EruptField(
            views = @View(title = "排序"),
            edit = @Edit(title = "排序")
    )
    private Integer sort;

    @EruptField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @EruptField(
            views = @View(title = "更新时间", show = false),
            edit = @Edit(title = "创建时间",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date updateTime;
}
