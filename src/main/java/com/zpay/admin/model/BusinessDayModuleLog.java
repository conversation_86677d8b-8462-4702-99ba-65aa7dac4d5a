package com.zpay.admin.model;

/**
 * <AUTHOR>
 * @updateTime 2024/8/17 20:15
 */

import com.zpay.admin.handler.BusinessDayModuleLogDataProxy;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


@Erupt(
        name = "tb_business_day_module_log",
        power = @Power(add = false, edit = false, delete = false),
        orderBy = "BusinessDayModuleLog.statDate desc,BusinessDayModuleLog.id asc",
        dataProxy = BusinessDayModuleLogDataProxy.class

)
@Table(name = "tb_business_day_module_log")   //数据库表名
@Entity
public class BusinessDayModuleLog extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "80"
            )
    )
    private String businessName;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "100"
            ),
            edit = @Edit(
                    title = "统计日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            )
    )
    private Date statDate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "统计模块",
                    width = "80"
            ),
            edit = @Edit(
                    title = "统计模块",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            //'main','store','usdt','token','share','physical','payment'
                            @VL(value = "main", label = "主账户"),
                            @VL(value = "share", label = "共享卡钱包"),
                            @VL(value = "store", label = "储值卡钱包"),
                            @VL(value = "physical", label = "实体卡钱包"),
//                            @VL(value = "payment", label = "代付钱包")
                    }),
                    search = @Search
            )
    )
    private String walletType;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "划入",
                    width = "80"
            ),
            edit = @Edit(
                    title = "划入",
                    notNull = true
            )
    )
    private Integer transferIn;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "划出",
                    width = "80"
            ),
            edit = @Edit(
                    title = "划出",
                    notNull = true
            )
    )
    private Float transferOut;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "消费",
                    width = "80"
            ),
            edit = @Edit(
                    title = "消费",
                    notNull = true
            )
    )
    private Integer consume;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "支出",
                    width = "80"
            ),
            edit = @Edit(
                    title = "支出",
                    notNull = true
            )
    )
    private Integer disburse;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户收入",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户收入",
                    notNull = true
            )
    )
    private Integer businessEarn;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台收入",
                    width = "80"
            ),
            edit = @Edit(
                    title = "平台收入",
                    notNull = true
            )
    )
    private Integer platformEarn;


}
