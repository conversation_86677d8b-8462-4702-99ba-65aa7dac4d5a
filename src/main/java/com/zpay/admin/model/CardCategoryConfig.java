package com.zpay.admin.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * <AUTHOR>
 */
@Erupt(
        name = "卡片渠道",
        power = @Power(add = false, edit = false, delete = false)
)
@Table(name = "tb_user_card_category_config")
@Entity
@Getter
@Setter
public class CardCategoryConfig extends BaseModel {

    @EruptField(
            views = @View(
                    title = "上游渠道",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游渠道",
                    search = @Search
            )
    )
    private String source;

    @EruptField(
            views = @View(
                    title = "产品编码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "产品编码",
                    search = @Search
            )
    )
    private String productCode;

    @EruptField(
            views = @View(
                    title = "产品名称"
            ),
            edit = @Edit(
                    title = "产品名称",
                    search = @Search
            )
    )
    private String productName;

    @EruptField(
            views = @View(
                    title = "卡模式"
            ),
            edit = @Edit(
                    title = "卡模式"
            )
    )
    private String cardModel;

    @EruptField(
            views = @View(
                    title = "结算币种"
            ),
            edit = @Edit(
                    title = "支持的结算币种"
            )
    )
    private String cardCurrency;


    @EruptField(
            views = @View(
                    title = "卡组织"
            ),
            edit = @Edit(
                    title = "卡组织"
            )
    )
    private String cardScheme;

    @EruptField(
            views = @View(
                    title = "渠道标识",
                    width = "120"
            ),
            edit = @Edit(
                    title = "渠道标识",
                    notNull = true,
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "skyee", label = "skyee"),
                            @VL(value = "photon", label = "photon"),
                            @VL(value = "ZPhysical", label = "ZPhysical")
                    })
            )
    )
    private String channel;

}

