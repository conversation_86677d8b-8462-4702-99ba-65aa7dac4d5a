package com.zpay.admin.model;


import com.zpay.admin.handler.PhysicalCardDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2024/7/15 15:30
 */
@Erupt(
        name = "实体卡",
        power = @Power(delete = false),
        orderBy = "PhysicalCard.id desc",
        filter = @Filter("PhysicalCard.cardModel ='physical'"),
        dataProxy = PhysicalCardDataProxy.class
)
@Table(name = "tb_user_card")   //数据库表名
@Entity
@Getter
@Setter
public class PhysicalCard extends BaseModel {

    private Integer uid;


    private Integer uidChild;

    @Column(length = 255, unique = true)
    @EruptField(
            views = @View(
                    title = "卡片ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡片ID",
                    notNull = true,
                    search = @Search(vague = true),
                    show = false
            )
    )
    private String cardId;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "卡号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String cardNumber;

    //    @Column(precision = 10, scale = 2, nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "可用金额",
//                    width = "80"
//            ),
//            edit = @Edit(
//                    title = "可用金额",
//                    notNull = true
//            )
//    )
    private Float amount;

    private Float virtualAmt;

    private String productCode;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "卡模式",
//                    width = "80"
//            ),
//            edit = @Edit(
//                    title = "卡模式",
//                    search = @Search,
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(
//                            vl = {
//                                    @VL(label = "充值卡", value = "recharge"),
//                                    @VL(label = "共享卡", value = "share"),
//                                    @VL(label = "实体卡", value = "physical")
//                            }
//                    )
//            )
//    )
    private String cardModel;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "开卡币种",
//                    width = "60"
//            ),
//            edit = @Edit(
//                    title = "开卡币种"
//            )
//    )
    private String cardCurrency;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "单笔限额",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "单笔限额"
//            )
//    )
    private String cardAmt;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "卡片总限额",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "卡片总限额"
//            )
//    )
    private String cardTotalAmt;


    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "激活码",
                    width = "80"
            ),
            edit = @Edit(
                    title = "激活码",
                    notNull = true
            )
    )
    private String orderNo;


    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "有效期",
//                    width = "80"
//            ),
//            edit = @Edit(
//                    title = "有效期",
//                    notNull = true
//            )
//    )
    private String cardExpirationMmyy;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "CVV",
//                    width = "50"
//            ),
//            edit = @Edit(
//                    title = "CVV",
//                    notNull = true
//            )
//    )
    private String cardCvv;


    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡片状态",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡片状态",
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "活跃", value = "Active"),
                                    @VL(label = "锁定", value = "Blocked"),
                                    @VL(label = "过期", value = "Expired"),
                                    @VL(label = "注销", value = "Cancel")
                            }
                    ),
                    show = false
            )
    )
    private String cardStatus;


    private Date createTime;


    private Date updateTime;

    private Date applyTime;

    private Integer status;

    private Date openCardTime;


    private Date openCardDate;

    private Date cancelCardTime;

    private Date cancelCardDate;

    private String channel;

    private Integer cardManageId;

    private String cardNickname;

    private Integer holderId;

    @EruptField(
            views = @View(
                    title = "卡组织",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡组织",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(label = "Master", value = "Master"),
                            @VL(label = "VISA", value = "VISA"),
                            @VL(label = "银联", value = "UnionPay")
                    })
            )
    )
    private String cardScheme;
}

