package com.zpay.admin.model;


import com.zpay.admin.dialog.CardActionFromDialog;
import com.zpay.admin.dialog.CardActionFromHandler;
import com.zpay.admin.handler.UserCardDataProxy;
import lombok.Getter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2024/7/15 15:30
 */
@Erupt(
        name = "tb_user_card",
        power = @Power(export = true, add = false, edit = false),
        orderBy = "UserCard.openCardTime desc",
        rowOperation = {
                @RowOperation(
                        title = "卡片操作",
                        code = "SINGLE",
                        icon = "fa fa-ioxhost",
                        mode = RowOperation.Mode.SINGLE,
                        eruptClass = CardActionFromDialog.class,
                        operationHandler = CardActionFromHandler.class
                ),
        },
        dataProxy = UserCardDataProxy.class
)
@Table(name = "tb_user_card")   //数据库表名
@Entity
@Getter
public class UserCard extends BaseModel {

    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    search = @Search
            )
    )
    private String uid;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "80"
            )
    )
    private String businessName;


    private Integer uidChild;

    @Column(length = 255, unique = true)
    @EruptField(
            views = @View(
                    title = "卡片ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡片ID",
                    search = @Search(vague = true)
            )
    )
    private String cardId;

    @Column(length = 100)
    @EruptField(
            views = @View(
                    title = "卡号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡号",
                    search = @Search(vague = true)
            )
    )
    private String cardNumber;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "可用金额",
                    width = "80"
            ),
            edit = @Edit(
                    title = "可用金额",
                    notNull = true
            )
    )
    private Float amount;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "账面金额",
                    width = "80"
            ),
            edit = @Edit(
                    title = "账面金额",
                    notNull = true
            )
    )
    private Float virtualAmt;

    private String productCode;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡模式",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡模式",
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "充值卡", value = "recharge"),
                                    @VL(label = "共享卡", value = "share"),
                                    @VL(label = "实体卡", value = "physical")
                            }
                    )
            )
    )
    private String cardModel;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "开卡币种",
                    width = "60"
            ),
            edit = @Edit(
                    title = "开卡币种"
            )
    )
    private String cardCurrency;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "单笔限额",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "单笔限额"
//            )
//    )
    private String cardAmt;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "卡片总限额",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "卡片总限额"
//            )
//    )
    private String cardTotalAmt;

    private String orderNo;


    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "有效期",
                    width = "80"
            ),
            edit = @Edit(
                    title = "有效期"
            )
    )
    private String cardExpirationMmyy;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "CVV",
                    width = "50"
            ),
            edit = @Edit(
                    title = "CVV"
            )
    )
    private String cardCvv;


    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡片状态",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡片状态",
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "活跃", value = "Active"),
                                    @VL(label = "锁定", value = "Blocked"),
                                    @VL(label = "过期", value = "Expired"),
                                    @VL(label = "注销", value = "Cancel")
                            }
                    )
            )
    )
    private String cardStatus;


    private Date createTime;


    private Date updateTime;

    private Date applyTime;

    private Integer status;

    @EruptField(
            views = @View(
                    title = "开卡时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "开卡时间",
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date openCardTime;


    private Date openCardDate;

    @EruptField(
            views = @View(
                    title = "销卡时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "销卡时间",
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date cancelCardTime;

    private Date cancelCardDate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "上游渠道",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游渠道",
                    search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "skyee", label = "天易"),
                            @VL(value = "photon", label = "光子易"),
                            @VL(value = "photon2", label = "光子易2"),
                            @VL(value = "ZPhysical", label = "ZNET手动实体卡"),
                            @VL(value = "GSalary", label = "GSalary")
                    })
            )
    )
    private String channel;

    private Integer cardManageId;

    @Column(length = 25)
    @EruptField(
            views = @View(
                    title = "卡片昵称",
                    width = "100"
            ),
            edit = @Edit(
                    title = "卡片昵称"
            )
    )
    private String cardNickname;

    @EruptField(
            views = @View(
                    title = "持卡人ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "持卡人ID"
            )
    )
    private Integer holderId;

    @EruptField(
            views = @View(
                    title = "卡组织",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡组织",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(label = "Master", value = "Master"),
                            @VL(label = "VISA", value = "VISA"),
                            @VL(label = "银联", value = "UnionPay"),
                            @VL(label = "发现卡", value = "Discover"),
                    })
            )
    )
    private String cardScheme;
}

