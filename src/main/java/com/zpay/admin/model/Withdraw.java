package com.zpay.admin.model;

import com.zpay.admin.handler.WithdrawDataProxy;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Erupt(
        name = "提现列表",
        filter = @Filter("Withdraw.transType = 2 and Withdraw.status = 0"),
        power = @Power(add = false, delete = false),
        dataProxy = WithdrawDataProxy.class
)
@Table(name = "tb_coin_in_out")
@Entity
@Getter
@Setter
@DynamicUpdate
@DynamicInsert
public class Withdraw extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;

    //    @Column(nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "交易类型",
//                    width = "60"
//            ),
//            edit = @Edit(
//                    title = "交易类型",
//                    notNull = true,
//                    type = EditType.CHOICE,
//                    readonly = @Readonly,
//                    choiceType = @ChoiceType(vl = {
//                            @VL(value = "1", label = "充值"),
//                            @VL(value = "2", label = "提现"),
//                    }),
//                    search = @Search
//            )
//    )
    private Integer transType;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "币类型",
                    width = "60"
            ),
            edit = @Edit(
                    title = "币类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "1", label = "ERC20"),
                            @VL(value = "56", label = "BEP20"),
                            @VL(value = "137", label = "MATIC"),
                            @VL(value = "195", label = "TRC20"),
                            @VL(value = "200", label = "Solana")
                    }),
                    search = @Search
            )
    )
    private Integer coinType;

    private String fromAddress;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "到账地址",
                    width = "120"
            ),
            edit = @Edit(
                    title = "到账地址",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            )
    )
    private String toAddress;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易Hash",
                    width = "120"
            ),
            edit = @Edit(
                    title = "交易Hash",
                    readonly = @Readonly
            )
    )
    private String hash;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "金额",
                    readonly = @Readonly,
                    notNull = true
            )
    )
    private BigDecimal amount;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "手续费",
                    width = "120"
            ),
            edit = @Edit(
                    title = "手续费",
                    readonly = @Readonly,
                    notNull = true
            )
    )
    private BigDecimal fee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "状态",
                    width = "120"
            ),
            edit = @Edit(
                    title = "状态",
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "0", label = "待审核", disable = true),
                            @VL(value = "1", label = "驳回"),
                            @VL(value = "2", label = "通过"),
                            @VL(value = "9", label = "成功", disable = true),
                    })
            )
    )
    private Integer status;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "创建时间",
                    notNull = true,
                    type = EditType.DATE,
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "更新时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "更新时间",
                    notNull = true,
                    type = EditType.DATE,
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date updateTime;

}
