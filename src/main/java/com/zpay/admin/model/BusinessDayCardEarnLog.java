package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Erupt(
        name = "商户卡片收益统计",
        power = @Power(add = false, edit = false, delete = false)
)
@Table(name = "tb_business_day_card_earn_log")   //数据库表名
@Entity
public class BusinessDayCardEarnLog extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "开卡费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "开卡费",
                    notNull = true
            )
    )
    private Integer dayOpenCardFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "开卡费收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "开卡费收益",
                    notNull = true
            )
    )
    private Integer dayOpenCardEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "充值手续费",
                    width = "80"
            ),
            edit = @Edit(
                    title = "今日手续费",
                    notNull = true
            )
    )
    private Integer dayRechargeChargeFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "充值手续费收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "充值手续费收益",
                    notNull = true
            )
    )
    private Integer dayRechargeChargeCardEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "销卡费",
                    width = "80"
            ),
            edit = @Edit(
                    title = "销卡费",
                    notNull = true
            )
    )
    private Integer cancelCardFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "销卡费收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "销卡费收益",
                    notNull = true
            )
    )
    private Integer cancelCardEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "今日总收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "今日总收益",
                    notNull = true
            )
    )
    private Integer dayEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "历史总收益",
                    width = "80"
            ),
            edit = @Edit(
                    title = "历史总收益",
                    notNull = true
            )
    )
    private Integer historyEarnings;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "销卡余额返回",
                    width = "80"
            ),
            edit = @Edit(
                    title = "销卡余额返回",
                    notNull = true
            )
    )
    private Integer cardCancellationBalanceReturn;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "钱包类型",
                    width = "80"
            ),
            edit = @Edit(
                    title = "钱包类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "充值", value = "recharge"),
                                    @VL(label = "共享", value = "share"),
                                    @VL(label = "实体", value = "physical")
                            }
                    ),
                    search = @Search
            )
    )
    private String cardModel;

    private Date createTime;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "100"
            ),
            edit = @Edit(
                    title = "统计日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    search = @Search(vague = true)
            )
    )
    private Date statDate;


}
