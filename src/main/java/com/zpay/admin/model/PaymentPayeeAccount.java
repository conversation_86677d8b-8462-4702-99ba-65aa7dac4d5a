package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Erupt(
        name = "tb_payment_payee_account",
        power = @Power()
)
@Table(name = "tb_payment_payee_account")   //数据库表名
@Entity
public class PaymentPayeeAccount extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true
            )
    )
    private String businessId;

    @Column(length = 25)
    @EruptField(
            views = @View(
                    title = "到账方式",
                    width = "120"
            ),
            edit = @Edit(
                    title = "到账方式"
            )
    )
    private String paymentMethod;

    @Column(length = 25)
    @EruptField(
            views = @View(
                    title = "账户编码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "账户编码"
            )
    )
    private String accountNo;

    //    @Column(length = 25)
//    @EruptField(
//            views = @View(
//                    title = "账户注册URL",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "账户注册URL"
//            )
//    )
    private String registerUrl;

    @Column(length = 25)
    @EruptField(
            views = @View(
                    title = "上游状态",
                    width = "60"
            ),
            edit = @Edit(
                    title = "上游状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "PENDING", label = "等待处理"),
                            @VL(value = "ACTIVE", label = "正常"),
                            @VL(value = "INACTIVE", label = "停用"),
                    }),
                    search = @Search
            )
    )
    private String status;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "上游收款人ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游收款人ID"
            )
    )
    private String payeeId;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "上游账户ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游账户ID",
                    notNull = true
            )
    )
    private String accountId;

    //    @EruptField(
//            views = @View(
//                    title = "create_time",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "create_time",
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE)
//            )
//    )
    private Date createTime;


}
