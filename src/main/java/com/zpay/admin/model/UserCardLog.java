package com.zpay.admin.model;


import com.zpay.admin.handler.UserCardLogDataProxy;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


@Erupt(
        name = "tb_user_card_log",
        power = @Power(),
        orderBy = "UserCardLog.createTime desc",
        dataProxy = UserCardLogDataProxy.class
)
@Table(name = "tb_user_card_log")   //数据库表名
@Entity
public class UserCardLog extends BaseModel {

    private Integer childUserId;

    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "80"
            )
    )
    private String businessName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "通知类型",
                    width = "80"
            ),
            edit = @Edit(
                    title = "通知类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "ZNET-CARD", label = "ZNET"),
                            @VL(value = "cardPay", label = "卡交易"),
                    }),
                    search = @Search
            )
    )
    private String noticeType;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易ID",
                    width = "140"
            ),
            edit = @Edit(
                    title = "交易ID",
                    search = @Search
            )
    )
    private String orderId;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "预授权时间",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "预授权时间"
//            )
//    )
    private String authTime;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易币种",
                    width = "60"
            ),
            edit = @Edit(
                    title = "交易币种"
            )
    )
    private String transAmountCurrency;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "交易金额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "交易金额"
            )
    )
    private Float transAmount;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "授权币种",
                    width = "60"
            ),
            edit = @Edit(
                    title = "授权币种"
            )
    )
    private String authAmountCurrency;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "授权金额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "授权金额"
            )
    )
    private Float authAmount;

    //    @Column(precision = 36, scale = 2)
//    @EruptField(
//            views = @View(
//                    title = "结算金额",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "结算金额"
//            )
//    )
    private Float settledAmount;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡ID",
                    search = @Search
            )
    )
    private String cardId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡号",
                    search = @Search(vague = true)
            )
    )
    private String maskCardNumber;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "产品编码",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "产品编码"
//            )
//    )
    private String productCode;

    //    @Column(length = 255)
//    @EruptField(
//            views = @View(
//                    title = "产品名称",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "产品名称"
//            )
//    )
    private String productName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡模式",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡模式",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "recharge", label = "充值卡"),
                            @VL(value = "share", label = "共享卡"),
                            @VL(value = "physical", label = "实体卡")
                    }),
                    search = @Search
            )
    )
    private String cardModel;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "卡别名",
                    width = "120"
            ),
            edit = @Edit(
                    title = "卡别名"
            )
    )
    private String cardAlias;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户名称"
            )
    )
    private String merchantName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户国家代码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户国家代码"
            )
    )
    private String merchantCountryCode;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户所在城市",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户所在城市"
            )
    )
    private String merchantCity;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户所在州或区",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户所在州或区"
            )
    )
    private String merchantState;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户邮编",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户邮编"
            )
    )
    private String merchantZipCode;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "商户描述",
                    width = "120"
            ),
            edit = @Edit(
                    title = "商户描述"
            )
    )
    private String merchantDesc;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易状态",
                    width = "60"
            ),
            edit = @Edit(
                    title = "交易状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "AuthSuccessed", label = "预授权成功"),
                            @VL(value = "AuthFailure", label = "预授权失败"),
                            @VL(value = "Settled", label = "已结算")
                    }),
                    search = @Search

            )
    )
    private String status;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "资金方向",
                    width = "120"
            ),
            edit = @Edit(
                    title = "资金方向",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "Income", label = "收入"),
                            @VL(value = "Expenditure", label = "支出"),
                            @VL(value = "Nochange", label = "不变化")
                    })
            )
    )
    private String fundsDirection;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易类型",
                    width = "120"
            ),
            edit = @Edit(
                    title = "交易类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "Auth", label = "消费"),
                            @VL(value = "Verification", label = "验证"),
                            @VL(value = "Refund", label = "退款"),
                            @VL(value = "RefundTransFee", label = "退款手续费"),
                            @VL(value = "Void", label = "撤销退款"),
                            @VL(value = "CorrectiveAuth", label = "纠正授权"),
                            @VL(value = "CorrectiveRefund", label = "校正退款"),
                            @VL(value = "CorrectiveRefundVoid", label = "校正退款取消"),
                            @VL(value = "DiscardRechargeReturn", label = "销卡余额返回"),
                            @VL(value = "ServiceFee", label = "服务费"),
                            @VL(value = "TransFee", label = "交易手续费"),
                            @VL(value = "RefundReversal", label = "退款撤销"),
                            @VL(value = "OpenCardFee", label = "开卡费"),
                            @VL(value = "CardRecharge", label = "卡片充值"),
                            @VL(value = "RechargeFee", label = "充值手续费"),
                            @VL(value = "CancelCardFee", label = "销卡费"),
                            @VL(value = "FeeReturn", label = "手续费退回"),
                            @VL(value = "CardWithdraw", label = "卡片提出"),
                    }),
                    search = @Search
            )
    )
    private String transactionType;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "失败原因",
                    width = "120"
            ),
            edit = @Edit(
                    title = "失败原因"
            )
    )
    private String failureReason;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易备注",
                    width = "120"
            ),
            edit = @Edit(
                    title = "交易备注"
            )
    )
    private String note;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "可用额度",
                    width = "60"
            ),
            edit = @Edit(
                    title = "可用额度"
            )
    )
    private Float availableCredit;

    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "创建时间",
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(
                            vague = true
                    )
            )
    )
    private Date createTime;


    private Date createDate;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "渠道",
                    width = "120",
                    show = false
            ),
            edit = @Edit(
                    title = "渠道",
                    type = EditType.CHOICE,
                    show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "ZPhysical", label = "ZPhysical"),
                            @VL(value = "photon", label = "光之易"),
                            @VL(value = "photon2", label = "光之易2"),
                            @VL(value = "GSalary", label = "GSalary"),
                            @VL(value = "asinx", label = "asinx"),
                            @VL(value = "asinxPhysical", label = "asinx实体卡"),
                    })
            )
    )
    @Transient
    private String channel;
//
//    @EruptField(
//            views = @View(
//                    title = "开始时间",
//                    width = "60",
//                    show = false
//            ),
//            edit = @Edit(
//                    title = "开始时间",
//                    show = false,
//                    search = @Search(),
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE_TIME)
//            )
//    )
//    @Transient
//    private String startTime;
//
//    @EruptField(
//            views = @View(
//                    title = "结束时间",
//                    width = "60",
//                    show = false
//            ),
//            edit = @Edit(
//                    title = "结束时间",
//                    show = false,
//                    search = @Search,
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE_TIME)
//            )
//    )
//    @Transient
//    private String endTime;


}
