package com.zpay.admin.model;


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2023-12-13 14:00:00
 */
@Erupt(
        name = "资金流水",
        power = @Power(export = true, add = false, edit = false, delete = false),
        orderBy = "FundLog.id desc"
)
@Table(name = "tb_platform_day_money_log")
@Entity
public class FundLog extends BaseModel {

    @EruptField(
            views = @View(
                    title = "今日充值",
                    desc = "所有商户当日在平台执行充值操作的金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日充值",
                    desc = "所有商户当日在平台执行充值操作的金额"
            )
    )
    private Float totalRecharge;

    @EruptField(
            views = @View(
                    title = "今日提现",
                    desc = "所有商户当日在平台执行提现操作的金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日提现",
                    desc = "所有商户当日在平台执行提现操作的金额"
            )
    )
    private Float totalWithdraw;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "今日盈利",
                    desc = "平台从商户处赚取的所有利润总和，包括开卡费以及手续费。",
                    width = "120"
            ),
            edit = @Edit(
                    title = "今日盈利",
                    desc = "平台从商户处赚取的所有利润总和，包括开卡费以及手续费。"
            )
    )
    private Float totalEarn;

    @Column(precision = 36, scale = 2)
    @EruptField(
            views = @View(
                    title = "平台TVT",
                    desc = "平台总的资金沉淀量，包括平台所有的商户充值进来尚未消费掉的资金。",
                    width = "120"
            ),
            edit = @Edit(
                    title = "平台TVT",
                    desc = "平台总的资金沉淀量，包括平台所有的商户充值进来尚未消费掉的资金。"
            )
    )
    private Float totalBalance;

    @EruptField(
            views = @View(
                    title = "统计日期",
                    width = "120"
            ),
            edit = @Edit(
                    title = "统计日期",
                    search = @Search,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date statDate;

    private Date createTime;


}
