package com.zpay.admin.model;


import com.zpay.admin.handler.WalletLogDataProxy;
import org.hibernate.annotations.GenericGenerator;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 * @updateTime 2024/7/15 11:41
 */
@Erupt(
        name = "tb_user_wallet_log",
        power = @Power(delete = false, edit = false, add = false),
        orderBy = "WalletLog.id DESC",
        dataProxy = WalletLogDataProxy.class
)
@Table(name = "tb_user_wallet_log")   //数据库表名
@Entity
public class WalletLog {

    @Id
    @GeneratedValue(generator = "generator")
    @GenericGenerator(name = "generator", strategy = "native")
    @Column(nullable = false)
    @EruptField()
    private Long id;

    private Integer userId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户 ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户 ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "80"
            )
    )
    private String businessName;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "操作类型",
                    width = "120"
            ),
            edit = @Edit(
                    // 31开卡费 32划转充值 33充值手续费 34销卡返回 35 销卡手续费
                    title = "操作类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "1", label = "划转进"),
                            @VL(value = "2", label = "划转出"),

                            @VL(value = "31", label = "开卡费"),
                            @VL(value = "32", label = "划转充值"),
                            @VL(value = "33", label = "充值手续费"),
                            @VL(value = "34", label = "销卡返回"),
                            @VL(value = "35", label = "销卡手续费"),
                            @VL(value = "36", label = "卡片提出"),
                            @VL(value = "41", label = "开卡费收入"),
                            @VL(value = "43", label = "充值手续费收入"),
                            @VL(value = "45", label = "销卡费收入"),

                            @VL(value = "51", label = "消费减少"),
                            @VL(value = "52", label = "消费退款"),

                            @VL(value = "62", label = "代付手续费"),
                            @VL(value = "63", label = "代付金额"),
                            @VL(value = "64", label = "代付收益"),

                            @VL(value = "91", label = "后台充值"),
                            @VL(value = "92", label = "后台扣减"),
                            @VL(value = "93", label = "提现驳回"),
                    }),
                    search = @Search
            )
    )
    private Integer action;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "钱包类型",
                    width = "120"
            ),
            edit = @Edit(
                    title = "钱包类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    //'main','store','usdt','token','share','physical','payment'
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "main", label = "主账户钱包"),
                            @VL(value = "store", label = "储值卡钱包"),
                            @VL(value = "share", label = "共享卡钱包"),
                            @VL(value = "physical", label = "实体卡钱包"),
                            @VL(value = "payment", label = "代付钱包"),
                            @VL(value = "usdt", label = "USDT钱包"),
//                            @VL(value = "token", label = "代币钱包"),
                    }),
                    search = @Search
            )
    )
    private String walletType;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "变动前金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "变动前金额",
                    notNull = true
            )
    )
    private Integer changeBefore;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "变动金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "变动金额",
                    notNull = true
            )
    )
    private Float changeMoney;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "变动后金额",
                    width = "120"
            ),
            edit = @Edit(
                    title = "变动后金额",
                    notNull = true
            )
    )
    private Integer changeAfter;

    @Column(length = 1000, nullable = false)
    @EruptField(
            views = @View(
                    title = "备注",
                    width = "120"
            ),
            edit = @Edit(
                    title = "备注",
                    notNull = true
            )
    )
    private String note;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "关联",
                    desc = "关联 ID",
                    width = "120",
                    show = false
            ),
            edit = @Edit(
                    title = "关联",
                    desc = "关联 ID",
                    notNull = true
            )
    )
    private Integer targetId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "操作时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "操作时间",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @Column(length = 25)
    @EruptField(
            views = @View(
                    title = "流水号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "流水号"
            )
    )
    private String orderId;


}

