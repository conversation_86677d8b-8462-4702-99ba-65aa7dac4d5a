package com.zpay.admin.model;


import com.zpay.admin.handler.PaymentUserConfigDataProxy;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;


@Erupt(
        name = "tb_payment_user_config",
        power = @Power(),
        dataProxy = PaymentUserConfigDataProxy.class
)
@Table(name = "tb_payment_user_config")   //数据库表名
@Entity
@Data
public class PaymentUserConfig extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "渠道",
                    width = "80"
            ),
            edit = @Edit(
                    title = "渠道",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"SELECT id,code as name FROM tb_payment_channel where state = 1"}
                    ),
                    search = @Search
            )
    )
    private Integer channelId;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "120"
            )
    )
    private String businessName;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台单笔手续费",
                    width = "80"
            ),
            edit = @Edit(
                    title = "平台单笔手续费",
                    notNull = true
            )
    )
    private BigDecimal platformFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台手续费率%",
                    width = "80"
            ),
            edit = @Edit(
                    title = "平台手续费率%",
                    notNull = true
            )
    )
    private BigDecimal platformFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户单笔手续费",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户单笔手续费",
                    readonly = @Readonly
            )
    )
    private BigDecimal businessFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户费率%",
                    width = "80"
            ),
            edit = @Edit(
                    title = "商户费率%",
                    readonly = @Readonly
            )
    )
    private BigDecimal businessFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "是否启用",
                    width = "80"
            ),
            edit = @Edit(
                    title = "是否启用",
                    notNull = true,
                    type = EditType.BOOLEAN
            )
    )
    private Boolean status;


}
