package com.zpay.admin.model;


import com.zpay.admin.handler.UserDataProxy;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Filter;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Erupt(name = "商户列表",
        dataProxy = UserDataProxy.class,
        filter = @Filter("deleteTime IS NULL")
)
@Table(name = "tb_user")
@SQLDelete(sql = "update tb_user set delete_time = now() where id = ?")
@Entity
@Getter
@Setter
@DynamicUpdate
public class User extends BaseModel {

    @EruptField(
            views = @View(title = "邮箱"),
            edit = @Edit(title = "邮箱",
                    notNull = true,
                    inputType = @InputType(
                            type = "email",
                            regex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                    ),
                    search = @Search(vague = true)
            )
    )
    private String email;

    @EruptField(
            views = @View(title = "手机号", show = false),
            edit = @Edit(title = "手机号", show = false)
    )
    private String mobile;

    @EruptField()
    private String salt;

    @EruptField(
            views = @View(title = "密码", show = false),
            edit = @Edit(title = "登录密码", inputType = @InputType(type = "password"))
    )
    private String password;

    @Column(name = "pay_password")
    @EruptField(
            views = @View(title = "支付密码", show = false),
            edit = @Edit(title = "支付密码", show = false, inputType = @InputType(type = "password"))
    )
    private String payPassword;


//    @EruptField(
//            views = @View(title = "余额"),
//            edit = @Edit(title = "余额", numberType = @NumberType(min = 0))
//    )
//    private Float money;

    @EruptField(
            views = @View(title = "用户状态", width = "80"),
            edit = @Edit(
                    title = "用户状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "正常", value = "1"),
                                    @VL(label = "冻结", value = "2")
                            }
                    )
            )
    )
    private Integer status;

//    @EruptField(
//            views = @View(title = "用户状态"),
//            edit = @Edit(title = "用户状态", boolType = @BoolType(trueText = "正常", falseText = "冻结"))
//    )
//    private Boolean status;

    @EruptField(
            views = @View(title = "商户ID"),
            edit = @Edit(title = "商户ID",
                    search = @Search(),
                    show = false, readonly = @Readonly()
            )
    )
    private String businessId;

    @EruptField(
            views = @View(title = "商户名称"),
            edit = @Edit(title = "商户名称", notNull = true)
    )
    private String businessName;

    @EruptField(
            views = @View(title = "商户状态", width = "80"),
            edit = @Edit(title = "商户状态", type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "开启", value = "1"),
                                    @VL(label = "关闭", value = "2")
                            }
                    ))
    )
    private Integer businessStatus;

    @EruptField(
            views = @View(title = "充值手续费"),
            edit = @Edit(title = "充值手续费", numberType = @NumberType(min = 0))
    )
    private Float rechargeRate;

    @EruptField(
            views = @View(title = "提现手续费"),
            edit = @Edit(title = "提现手续费", numberType = @NumberType(min = 0))
    )
    private Float withdrawRate;

    @EruptField(
            views = @View(
                    title = "最后登录时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "最后登录时间",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date lastLoginTime;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "最后登录IP",
                    width = "120"
            ),
            edit = @Edit(
                    title = "最后登录IP",
                    show = false
            )
    )
    private String lastLoginIp;


    @EruptField(
            views = @View(title = "商户客服"),
            edit = @Edit(title = "商户客服",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"SELECT id,name FROM tb_customer_service_info"}
                    )
            )
    )
    private String businessNo;


    @Transient
    @EruptField(
            views = @View(title = "开通模块"),
            edit = @Edit(
                    title = "开通模块",
                    type = EditType.TAGS,
                    tagsType = @TagsType(
                            tags = {"card", "payment", "flow"}
                    )
            )
    )
    private String enableModule;

    @EruptField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @EruptField(
            views = @View(title = "更新时间", show = false),
            edit = @Edit(title = "创建时间",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date updateTime;

    private Date deleteTime;

}
