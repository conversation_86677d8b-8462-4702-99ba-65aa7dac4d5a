package com.zpay.admin.model;


import com.zpay.admin.handler.CardChoiceTrigger;
import com.zpay.admin.handler.CardFeeDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.MetaModel;
import xyz.erupt.toolkit.handler.SqlChoiceFetchHandler;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Erupt(
        name = "卡片费率配置",
        power = @Power(),
        dataProxy = CardFeeDataProxy.class
)
@Table(name = "tb_user_card_fee_config")
@Entity
@Getter
@Setter
public class CardFeeConfig extends MetaModel {


    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "50"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = false)
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "100"
            )
    )
    private String businessName;


    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "卡片类型",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡片类型",
                    search = @Search,
                    notNull = true,
                    readonly = @Readonly(add = false),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"SELECT id, CONCAT(CASE WHEN card_model = 'share' THEN '共享卡' WHEN card_model = 'recharge' THEN '充值卡' WHEN card_model = 'physical' THEN '实体卡' END, ' - ', card_name) name FROM tb_user_card_manage"},
                            trigger = CardChoiceTrigger.class
                    )
            )
    )
    private Integer cardManageId;

    @EruptField(
            views = @View(
                    title = "卡段",
                    width = "30",
                    show = false
            )
    )
    private String CardBin;


    @Column(nullable = false)
    @EruptField(
            views = @View(title = "可用状态", width = "30"),
            edit = @Edit(
                    title = "可用状态",
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "启用", value = "1"),
                                    @VL(label = "禁用", value = "2")
                            }
                    )
            )
    )
    private Integer isAble;

    @EruptField(
            views = @View(title = "成本开卡费", width = "50"),
            edit = @Edit(
                    title = "成本开卡费",
                    readonly = @Readonly()
            )
    )
    private Float costOpenCardFee;

    @EruptField(
            views = @View(title = "成本充值费%", width = "50"),
            edit = @Edit(
                    title = "成本充值费%",
                    readonly = @Readonly()
            )
    )
    private Float costChargeRate;

    @EruptField(
            views = @View(title = "成本销卡费", width = "50"),
            edit = @Edit(
                    title = "成本销卡费",
                    readonly = @Readonly()
            )
    )
    private Float costCancelCardFee;

    @Column(precision = 5, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "平台开卡费",
                    width = "50"
            ),
            edit = @Edit(
                    title = "平台开卡费",
                    notNull = true
            )
    )
    private Float platformOpenCardFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台充值费%",
                    width = "50"
            ),
            edit = @Edit(
                    title = "平台充值费%",
                    notNull = true
            )
    )
    private Float platformChargeRate;

    @Column(precision = 5, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "平台销卡费",
                    width = "50"
            ),
            edit = @Edit(
                    title = "平台销卡费",
                    notNull = true
            )
    )
    private Float platformCancelCardFee;

    @Column(precision = 5, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "商户开卡费",
                    width = "50"
            )
    )
    private Float businessOpenCardFee;

    @Column(precision = 5, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "商户充值费%",
                    width = "50"
            )
    )
    private Float businessChargeRate;


    @Column(precision = 5, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "商户销卡费",
                    width = "50"
            )
    )
    private Float businessCancelCardFee;

    @EruptField(
            views = @View(
                    title = "可开卡数",
                    width = "60"
            ),
            edit = @Edit(
                    title = "可开卡数",
                    notNull = true
            )
    )
    private Integer numberOfCardsThatCanBeOpened;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "首充金额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "首充金额",
                    notNull = true
            )
    )
    private Integer firstRecharge;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "卡月费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡月费",
                    notNull = true
            )
    )
    private BigDecimal cardMonthFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "卡单次限额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡单次限额",
                    notNull = true
            )
    )
    private Float cardSingleLimit;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "卡月限额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡月限额",
                    notNull = true
            )
    )
    private Float cardMonthLimit;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "卡总限额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "卡总限额",
                    notNull = true
            )
    )
    private Float cardTotalLimit;


    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "授权手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "授权手续费",
                    desc = "-1表示不收取,直接使用上游数据",
                    notNull = true
            )
    )
    private Float preAuthFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "跨境手续费率%",
                    width = "60"
            ),
            edit = @Edit(
                    title = "跨境手续费率%",
                    desc = "-1表示不收取,直接使用上游数据",
                    notNull = true
            )
    )
    private Float crossBroadFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "交易手续费率%",
                    width = "60"
            ),
            edit = @Edit(
                    title = "交易手续费率%",
                    desc = "-1表示不收取,直接使用上游数据",
                    notNull = true
            )
    )
    private Float transactionFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "退款手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "退款手续费",
                    desc = "-1表示不收取,直接使用上游数据",
                    notNull = true
            )
    )
    private Float refundFee;

}

