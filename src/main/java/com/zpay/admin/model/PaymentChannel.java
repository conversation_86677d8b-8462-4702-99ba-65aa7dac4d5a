package com.zpay.admin.model;

/**
 * <AUTHOR>
 * @updateTime 2024/10/9 11:37
 */


import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


@Erupt(
        name = "渠道表",
        power = @Power(delete = false)
)
@Table(name = "tb_payment_channel")   //数据库表名
@Entity
public class PaymentChannel extends BaseModel {

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "渠道",
                    width = "120"
            ),
            edit = @Edit(
                    title = "渠道",
                    search = @Search
            )
    )
    private String code;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "国家二字码",
                    width = "120"
            ),
            edit = @Edit(
                    title = "国家二字码",
                    search = @Search
            )
    )
    private String country;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "币种",
                    width = "120"
            ),
            edit = @Edit(
                    title = "币种",
                    search = @Search
            )
    )
    private String currency;

    @EruptField(
            views = @View(
                    title = "启用状态",
                    width = "120"
            ),
            edit = @Edit(
                    title = "启用状态",
                    type = EditType.BOOLEAN,
                    search = @Search
            )
    )
    private Boolean state;


}
