package com.zpay.admin.model;


import com.zpay.admin.handler.PaymentPayOrderDataProxy;
import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


@Erupt(
        name = "tb_payment_pay_order",
        power = @Power(add = false, edit = false, delete = false),
        orderBy = "PaymentPayOrder.id desc",
        dataProxy = PaymentPayOrderDataProxy.class
)
@Table(name = "tb_payment_pay_order")   //数据库表名
@Entity
@Data
public class PaymentPayOrder extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "80"
            )
    )
    private String businessName;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "订单号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "订单号",
                    notNull = true,
                    search = @Search
            )
    )
    private String orderId;

    //    @Column(nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "收款人id",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "收款人id",
//                    notNull = true,
//                    search = @Search
//            )
//    )
    private Integer payeeId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "付款方式",
                    width = "60"
            ),
            edit = @Edit(
                    title = "付款方式",
                    search = @Search
            )
    )
    private String paymentMethod;

    //    @Column(precision = 25, scale = 10, nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "上游付款汇率",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "上游付款汇率",
//                    notNull = true,
//                    search = @Search
//            )
//    )
    private Float paymentRate;

    @Column(length = 50, nullable = false)
    @EruptField(
            views = @View(
                    title = "到账币种",
                    width = "60"
            ),
            edit = @Edit(
                    title = "到账币种",
                    notNull = true
            )
    )
    private String currencyReceived;

    @Column(precision = 25, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "到账金额",
                    width = "60"
            ),
            edit = @Edit(
                    title = "到账金额",
                    notNull = true
            )
    )
    private Float amountReceived;

    @Column(precision = 25, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "付款金额U",
                    width = "60"
            ),
            edit = @Edit(
                    title = "付款金额U",
                    notNull = true
            )
    )
    private Float paymentAmount;

    @Column(precision = 25, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "上游手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "上游手续费",
                    notNull = true
            )
    )
    private Float costFee;

    @Column(precision = 25, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "上游总收费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "上游总收费",
                    notNull = true
            )
    )
    private Float costTotalMoney;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台单笔手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "平台单笔手续费",
                    notNull = true
            )
    )
    private Integer platformFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台手续费率",
                    width = "60"
            ),
            edit = @Edit(
                    title = "平台手续费率",
                    notNull = true
            )
    )
    private Integer platformFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台总手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "平台总手续费",
                    notNull = true
            )
    )
    private Integer platformFeeTotal;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户手续费",
                    notNull = true
            )
    )
    private Integer businessFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户费率",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户费率",
                    notNull = true
            )
    )
    private Integer businessFeeRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户总手续费",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户总手续费",
                    notNull = true
            )
    )
    private Integer businessFeeTotal;

    //    @Column(length = 30)
//    @EruptField(
//            views = @View(
//                    title = "付款人",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "付款人"
//            )
//    )
    private String payer;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "付款用途",
                    width = "120"
            ),
            edit = @Edit(
                    title = "付款用途"
            )
    )
    private String paymentPurpose;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "交易状态",
                    width = "120"
            ),
            edit = @Edit(
                    title = "交易状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "CREATED", label = "已创建"),
                            @VL(value = "PAID", label = "已支付"),
                            @VL(value = "REVIEWING", label = "渠道审核中"),
                            @VL(value = "PENDING", label = "渠道处理中"),
                            @VL(value = "REJECTED", label = "渠道已拒绝"),
                            @VL(value = "COMPLETED", label = "已完成"),
                            @VL(value = "FAILED", label = "付款失败"),
                            @VL(value = "CANCELLED", label = "已取消"),
                    }),
                    search = @Search
            )
    )
    private String status;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "失败原因",
                    width = "120"
            ),
            edit = @Edit(
                    title = "失败原因"
            )
    )
    private String failureReason;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "创建时间",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;

    @EruptField(
            views = @View(
                    title = "付款时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "付款时间",
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date paymentTime;

    //    @Column(nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "锁汇过期时间",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "锁汇过期时间",
//                    notNull = true,
//                    type = EditType.DATE,
//                    dateType = @DateType(type = DateType.Type.DATE)
//            )
//    )
    private Date completionTime;

    //    @Column(length = 255, nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "锁汇id",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "锁汇id",
//                    notNull = true
//            )
//    )
    private String quoteId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "上游订单号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游订单号"
            )
    )
    private String upstreamOrderId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "备注",
                    width = "120"
            ),
            edit = @Edit(
                    title = "备注"
            )
    )
    private String remark;

    //    @Column(precision = 20, scale = 10)
//    @EruptField(
//            views = @View(
//                    title = "cost_payment_rate",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "cost_payment_rate"
//            )
//    )
    private Float costPaymentRate;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "付款账户",
                    width = "120"
            ),
            edit = @Edit(
                    title = "付款账户"
            )
    )
    private String paymentAccount;


}
