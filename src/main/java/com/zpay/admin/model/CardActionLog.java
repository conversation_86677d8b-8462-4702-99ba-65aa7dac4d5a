package com.zpay.admin.model;


import com.zpay.admin.handler.CardActionLogDataProxy;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Erupt(
        name = "卡片操作日志",
        power = @Power(export = true, add = false, edit = false, delete = false),
        orderBy = "CardActionLog.id desc",
        dataProxy = CardActionLogDataProxy.class

)
@Table(name = "tb_card_action_log")
@Entity
public class CardActionLog extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "操作类型",
                    width = "30"
            ),
            edit = @Edit(
                    title = "操作类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "1", label = "开卡"),
                            @VL(value = "2", label = "充值"),
                            @VL(value = "3", label = "销卡"),
                    }),
                    search = @Search
            )
    )
    private Integer type;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "40"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Transient
    @EruptField(
            views = @View(
                    title = "商户名称",
                    width = "50"
            )
    )
    private String businessName;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "卡片Id",
                    width = "80"
            ),
            edit = @Edit(
                    title = "卡片Id",
                    notNull = true,
                    search = @Search
            )
    )
    private String cardId;


    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "记录金额",
                    width = "40"
            ),
            edit = @Edit(
                    title = "记录金额",
                    notNull = true
            )
    )
    private Float money;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "成本费",
                    width = "40"
            ),
            edit = @Edit(
                    title = "成本费",
                    notNull = true
            )
    )
    private Float costFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "成本费率%",
                    width = "40"
            ),
            edit = @Edit(
                    title = "成本费率%",
                    notNull = true
            )
    )
    private Float costRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台费用",
                    width = "40"
            ),
            edit = @Edit(
                    title = "平台费用",
                    notNull = true
            )
    )
    private Float platformFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台费率%",
                    width = "40"
            ),
            edit = @Edit(
                    title = "平台费率",
                    notNull = true
            )
    )
    private Float platformRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户费用",
                    width = "40"
            ),
            edit = @Edit(
                    title = "商户费用",
                    notNull = true
            )
    )
    private Float businessFee;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户费率%",
                    width = "40"
            ),
            edit = @Edit(
                    title = "商户费率",
                    notNull = true
            )
    )
    private Float businessRate;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "平台收益",
                    width = "40",
                    show = false
            ),
            edit = @Edit(
                    title = "平台收益",
                    notNull = true
            )
    )
    private Float platformEarn;

    @Column(precision = 10, scale = 2, nullable = false)
    @EruptField(
            views = @View(
                    title = "商户收益",
                    width = "40",
                    show = false
            ),
            edit = @Edit(
                    title = "商户收益",
                    notNull = true
            )
    )
    private Float businessEarn;

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "创建时间",
                    width = "80"
            ),
            edit = @Edit(
                    title = "创建时间",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(vague = true)
            )
    )
    private Date createTime;

    private Date createDate;

}

