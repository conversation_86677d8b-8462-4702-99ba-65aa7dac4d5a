package com.zpay.admin.model;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Erupt(
        name = "tb_payment_pay_payee",
        power = @Power()
)
@Table(name = "tb_payment_pay_payee")   //数据库表名
@Entity
public class PaymentPayPayee extends BaseModel {

    @Column(nullable = false)
    @EruptField(
            views = @View(
                    title = "商户ID",
                    width = "60"
            ),
            edit = @Edit(
                    title = "商户ID",
                    notNull = true,
                    search = @Search
            )
    )
    private String businessId;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "上游ID",
                    width = "120"
            ),
            edit = @Edit(
                    title = "上游ID"
            )
    )
    private String payeeId;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "类型",
                    width = "60"
            ),
            edit = @Edit(
                    title = "类型",
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "INDIVIDUAL", label = "个人"),
                            @VL(value = "ENTERPRISE", label = "企业"),
                    })
            )
    )
    private String subjectType;

    @Column(length = 25, nullable = false)
    @EruptField(
            views = @View(
                    title = "国家",
                    width = "60"
            ),
            edit = @Edit(
                    title = "国家",
                    notNull = true
            )
    )
    private String country;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "收款币种",
                    width = "60"
            ),
            edit = @Edit(
                    title = "收款币种",
                    notNull = true
            )
    )
    private String currency;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "收款人名",
                    desc = "收款人名，类型为个人时必填",
                    width = "60"
            ),
            edit = @Edit(
                    title = "收款人名",
                    desc = "收款人名，类型为个人时必填"
            )
    )
    private String firstName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "收款人姓",
                    desc = "收款人姓，类型为个人时必填",
                    width = "60"
            ),
            edit = @Edit(
                    title = "收款人姓",
                    desc = "收款人姓，类型为个人时必填"
            )
    )
    private String lastName;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "公司名称",
                    desc = "公司名称，类型为公司时必填",
                    width = "120"
            ),
            edit = @Edit(
                    title = "公司名称",
                    desc = "公司名称，类型为公司时必填"
            )
    )
    private String accountHolder;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "区号",
                    width = "40"
            ),
            edit = @Edit(
                    title = "区号"
            )
    )
    private String nationCode;

    @Column(length = 255, nullable = false)
    @EruptField(
            views = @View(
                    title = "手机号",
                    width = "120"
            ),
            edit = @Edit(
                    title = "手机号",
                    notNull = true
            )
    )
    private String phone;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "地址",
                    width = "120"
            ),
            edit = @Edit(
                    title = "地址"
            )
    )
    private String address;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "付款用途",
                    width = "120"
            ),
            edit = @Edit(
                    title = "付款用途"
            )
    )
    private String paymentPurpose;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "二字码",
                    width = "60"
            ),
            edit = @Edit(
                    title = "二字码"
            )
    )
    private String twoLetterCode;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "国家码",
                    width = "60"
            ),
            edit = @Edit(
                    title = "国家码"
            )
    )
    private String englishNameCountry;

    //    @Column(nullable = false)
//    @EruptField(
//            views = @View(
//                    title = "付款渠道ID",
//                    width = "120"
//            ),
//            edit = @Edit(
//                    title = "付款渠道ID",
//                    notNull = true
//            )
//    )
    private Integer payConfigId;

    @EruptField(
            views = @View(
                    title = "添加时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "添加时间",
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date createTime;


}

