package com.zpay.admin.model;

import org.hibernate.annotations.GenericGenerator;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.DateType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Erupt(
        name = "tb_customer_service_info",
        power = @Power()
)
@Table(name = "tb_customer_service_info")   //数据库表名
@Entity
public class Customer {

    @Id
    @GeneratedValue(generator = "generator")
    @GenericGenerator(name = "generator", strategy = "native")
    @Column(nullable = false)
    @EruptField()
    private Long id;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "客服名称",
                    width = "120"
            ),
            edit = @Edit(
                    title = "客服名称",
                    search = @Search
            )
    )
    private String name;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "WhatsApp",
                    width = "120"
            ),
            edit = @Edit(
                    title = "WhatsApp"
//                    type = EditType.ATTACHMENT,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE)
            )
    )
    private String whatsApp;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "WeChat",
                    width = "120"
            ),
            edit = @Edit(
                    title = "WeChat"
//                    type = EditType.ATTACHMENT,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE)
            )
    )
    private String weChat;

    @Column(length = 255)
    @EruptField(
            views = @View(
                    title = "telegram",
                    width = "120"
            ),
            edit = @Edit(
                    title = "telegram"
//                    type = EditType.ATTACHMENT,
//                    attachmentType = @AttachmentType(type = AttachmentType.Type.IMAGE)
            )
    )
    private String telegram;

    @EruptField(
            views = @View(
                    title = "添加时间",
                    width = "120"
            ),
            edit = @Edit(
                    title = "添加时间",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date createTime;


}

