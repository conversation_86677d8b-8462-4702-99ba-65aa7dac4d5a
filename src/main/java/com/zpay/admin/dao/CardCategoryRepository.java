package com.zpay.admin.dao;

import com.zpay.admin.model.CardCategoryConfig;
import org.springframework.data.jpa.repository.JpaRepository;


/**
 * <AUTHOR>
 */
public interface CardCategoryRepository extends JpaRepository<CardCategoryConfig, Long> {

    /**
     * 根据来源和产品名称查询
     *
     * @param source      来源
     * @param productName 产品名称
     * @return CardCategoryConfig
     */
    CardCategoryConfig findBySourceAndProductName(String source, String productName);

}
