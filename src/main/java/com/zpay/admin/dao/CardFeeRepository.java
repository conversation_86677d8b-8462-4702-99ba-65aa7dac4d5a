package com.zpay.admin.dao;

import com.zpay.admin.model.CardFeeConfig;
import com.zpay.admin.model.CardManage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 */
@Transactional
public interface CardFeeRepository extends JpaRepository<CardFeeConfig, Long> {


    @Modifying
    @Query("UPDATE CardFeeConfig SET " +
            "costOpenCardFee = :#{#c.getCardOpenFee()}, " +
            "costChargeRate = :#{#c.getCardChargeRate()}, " +
            "costCancelCardFee = :#{#c.getCardCancelFee()} " +
            "WHERE cardManageId = :#{#c.getId().intValue()} ")
    int updateCardCostFee(CardManage c);


}
