package com.zpay.admin.dao;

import com.zpay.admin.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;


/**
 * <AUTHOR>
 */
public interface UserRepository extends JpaRepository<User, Long> {

    User findByBusinessId(Integer bid);
    
    @Query(value = "select * from tb_user  where business_id = ?1", nativeQuery = true)
    User findUserByBusinessId(Integer bid);


}
