package com.zpay.admin.dao;

import com.zpay.admin.model.Withdraw;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface UserMoneyRepository extends JpaRepository<Withdraw, Long> {
    @Query(value = "SELECT check_status FROM tb_user_money_log WHERE id = :id", nativeQuery = true)
    Integer findCheckStatusById(@Param("id") Long id);


    @Query(value = "SELECT money FROM tb_user_money_log WHERE business_id = :businessId AND order_id = :orderId AND sub_type =:subType", nativeQuery = true)
    BigDecimal findLogMoney(Integer businessId, String orderId, Integer subType);

}
