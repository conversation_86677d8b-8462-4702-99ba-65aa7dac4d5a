package com.zpay.admin.dao;

import com.zpay.admin.model.CardManage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.CrudRepository;

import javax.persistence.QueryHint;

/**
 * <AUTHOR>
 */
public interface CardManageRepository extends JpaRepository<CardManage, Long> {

    @QueryHints(value = {
            @QueryHint(name = "javax.persistence.readOnly", value = "true"), // 设定只读模式
            @QueryHint(name = "org.hibernate.readOnly", value = "true") // 设定只读模式（特定于Hibernate）
    })
    CardManage findByIdEquals(Long id);
}
