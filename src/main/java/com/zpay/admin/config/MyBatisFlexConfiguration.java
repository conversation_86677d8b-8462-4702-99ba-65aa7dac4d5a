package com.zpay.admin.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @updateTime 2024/8/1 09:37
 */

@Configuration
public class MyBatisFlexConfiguration implements MyBatisFlexCustomizer {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Override
    public void customize(FlexGlobalConfig flexGlobalConfig) {
        if (!"prod".equals(activeProfile)) {
            // 开启审计功能
            AuditManager.setAuditEnable(true);

            // 设置 SQL 审计收集器
            MessageCollector collector = new ConsoleMessageCollector();
            AuditManager.setMessageCollector(collector);
        }
    }
}