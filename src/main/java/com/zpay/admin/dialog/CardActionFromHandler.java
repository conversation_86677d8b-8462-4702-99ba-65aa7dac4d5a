package com.zpay.admin.dialog;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.zpay.admin.model.UserCard;
import com.zpay.admin.service.RpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;

import java.util.*;

/**
 * <AUTHOR>
 * @updateTime 2024/7/16 15:49
 */
@Component
public class CardActionFromHandler implements OperationHandler<UserCard, CardActionFromDialog> {

    private final static Set<String> NotActionStatus = new HashSet<>(List.of(
            "Cancel", "Expired"
    ));

    @Autowired
    RpcService rpcService;


    @Override
    public String exec(List<UserCard> data, CardActionFromDialog form, String[] param) {
        UserCard cardInfo = data.get(0);
        String cardStatus = cardInfo.getCardStatus();
        if (NotActionStatus.contains(cardStatus)) {
            return "msg.error('卡片已注销或过期')";
        }
        switch (form.getAction()) {
            case "freeze":
                if ("Blocked".equals(cardStatus)) {
                    return "msg.error('卡片已锁定')";
                }
                break;
            case "unfreeze":
                if ("Active".equals(cardStatus)) {
                    return "msg.error('卡片是活跃状态')";
                }
                break;
            case "cancel":
                break;
            default:
                return "msg.error('操作类型有误')";
        }
        JSONObject body = new JSONObject();
        body.putOnce("action", form.getAction());
        body.putOnce("cardId", cardInfo.getCardId());

        rpcService.sendReqNow("card", body);

        return null;
    }

//    @Override
//    public CardActionFromDialog eruptFormValue(List<UserCard> data, CardActionFromDialog form, String[] param) {
//        // 设置 from 初始值
//        return form;
//    }
}
