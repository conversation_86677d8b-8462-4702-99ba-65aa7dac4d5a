package com.zpay.admin.dialog;

import lombok.Data;
import lombok.EqualsAndHashCode;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

/**
 * <AUTHOR>
 * @updateTime 2024/7/16 15:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Erupt(name = "CardActionFromDialog")
public class CardActionFromDialog extends BaseModel {

    @EruptField(
            edit = @Edit(
                    title = "卡片操作",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            vl = {
                                    @VL(label = "冻结", value = "freeze"),
                                    @VL(label = "解冻", value = "unfreeze"),
                                    @VL(label = "销卡", value = "cancel")
                            }
                    )
            )
    )
    private String action;
}
