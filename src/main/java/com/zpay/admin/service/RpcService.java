package com.zpay.admin.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;

/**
 * <AUTHOR>
 * @updateTime 2024/7/16 18:00
 */
@Service
@Slf4j
public class RpcService {

    @Value("${znet.rpc}")
    private String rpcUrl;


    public JSONObject sendReqNow(String path, JSONObject body) {
        HttpResponse res;
        try {
            res = HttpRequest.post(rpcUrl + path)
                    .header("SECRET", "TAMIA")
                    .body(JSONUtil.toJsonStr(body))
                    .execute();
        } catch (Exception e) {
            log.error("请求出错 #RPC000 {}", e.getMessage(), e);
            throw new EruptApiErrorTip("请求出错 #RPC000", EruptApiModel.PromptWay.MESSAGE);
        }

        JSONObject resObj = JSONUtil.parseObj(res.body());
        if (!resObj.getInt("code").equals(200)) {
            log.error("请求出错 #RPC001 {}", resObj.get("msg"));
            throw new EruptApiErrorTip(resObj.getStr("msg"), EruptApiModel.PromptWay.MESSAGE);
        }
        return resObj.getJSONObject("data");
    }

}
