package com.zpay.admin.service;

import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.zpay.admin.job.dto.FundInOutDto;
import com.zpay.admin.job.dto.StatWalletDto;
import com.zpay.admin.job.dto.TvlDto;
import generator.domain.CoinInOut;
import generator.domain.UserWallet;
import generator.domain.UserWalletLog;
import generator.service.CoinInOutService;
import generator.service.UserWalletLogService;
import generator.service.UserWalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.linq.Linq;
import xyz.erupt.linq.util.Columns;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static generator.domain.table.CoinInOutTableDef.COIN_IN_OUT;
import static generator.domain.table.UserWalletLogTableDef.USER_WALLET_LOG;
import static generator.domain.table.UserWalletTableDef.USER_WALLET;

/**
 * <AUTHOR>
 * @updateTime 2024/8/13 18:05
 */
@Service
@Slf4j
public class StatService {

    @Autowired
    UserWalletService userWalletService;

    @Autowired
    CoinInOutService coinInOutService;

    @Autowired
    UserWalletLogService userWalletLogService;

    public TvlDto tvl() {
        List<UserWallet> list = userWalletService.queryChain()
                .select(USER_WALLET.ALL_COLUMNS)
                .from(USER_WALLET)
                .list();
        return Linq.from(list)
                .select(Columns.sum(UserWallet::getMainWallet, "mainTvl"))
                .select(Columns.sum(UserWallet::getShareWallet, "shareTvl"))
                .select(Columns.sum(UserWallet::getStoreWallet, "storeTvl"))
                .select(Columns.sum(UserWallet::getPhysicalWallet, "physicalTvl"))
                .select(Columns.sum(UserWallet::getPaymentWallet, "paymentTvl"))
                .select(Columns.sum(UserWallet::getUsdtWallet, "usdtTvl"))
                .writeOne(TvlDto.class);
    }

    public FundInOutDto fundInOut() {
        FundInOutDto fund = new FundInOutDto();
        List<CoinInOut> list = coinInOutService.queryChain()
                .select(COIN_IN_OUT.AMOUNT, COIN_IN_OUT.FEE, COIN_IN_OUT.TRANS_TYPE, COIN_IN_OUT.CREATE_TIME)
                .from(COIN_IN_OUT)
                .list();
        Date todayBegin = DateUtil.beginOfDay(DateUtil.date());

        BigDecimal todayDeposit = list.stream()
                .filter(item -> item.getCreateTime().after(todayBegin))
                .filter(item -> item.getTransType().equals(1))
                .map(CoinInOut::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setTodayDeposit(todayDeposit);

        BigDecimal todayWithdraw = list.stream()
                .filter(item -> item.getCreateTime().after(todayBegin))
                .filter(item -> item.getTransType().equals(2))
                .map(CoinInOut::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setTodayWithdraw(todayWithdraw);

        BigDecimal todayWithdrawFee = list.stream()
                .filter(item -> item.getCreateTime().after(todayBegin))
                .filter(item -> item.getTransType().equals(2))
                .map(CoinInOut::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setTodayWithdrawFee(todayWithdrawFee);

        BigDecimal historyDeposit = list.stream()
                .filter(item -> item.getTransType().equals(1))
                .map(CoinInOut::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setHistoryDeposit(historyDeposit);

        BigDecimal historyWithdraw = list.stream()
                .filter(item -> item.getTransType().equals(2))
                .map(CoinInOut::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setHistoryWithdraw(historyWithdraw);

        BigDecimal historyWithdrawFee = list.stream()
                .filter(item -> item.getTransType().equals(2))
                .map(CoinInOut::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fund.setHistoryWithdrawFee(historyWithdrawFee);

        return fund;
    }

    public HashMap<String, StatWalletDto> statWallet(String statDate, Integer businessId) {
        HashMap<String, StatWalletDto> statList = new HashMap<>();
        Date yesterday = DateUtil.parse(statDate);
        QueryWrapper query = QueryWrapper.create()
                .from(USER_WALLET_LOG)
                .select(USER_WALLET_LOG.ALL_COLUMNS)
                .where(USER_WALLET_LOG.BUSINESS_ID.eq(businessId))
                .where(USER_WALLET_LOG.CREATE_TIME.between(DateUtil.beginOfDay(yesterday), DateUtil.endOfDay(yesterday)));
        List<UserWalletLog> list = userWalletLogService.list(query);

//         * 操作类型:  1 划转进 2 划转出 3 交易扣减
//         * 31开卡费 32划转充值 33充值手续费 34销卡返回 35 销卡手续费
//         * 41开卡费收入 43充值手续费收入 45销卡费收入

        List<Integer> inActionList = List.of(1, 3, 4, 5, 6, 7, 8, 16, 22);
        List<Integer> outActionList = List.of(2, 9, 10, 11, 12, 13, 14, 15, 24, 19);

        {
            StatWalletDto mainDto = new StatWalletDto();
            BigDecimal transferIn = list.stream()
                    .filter(item -> "main".equals(item.getWalletType()))
                    .filter(item -> inActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            mainDto.setTransferIn(transferIn);
            BigDecimal transferOut = list.stream()
                    .filter(item -> "main".equals(item.getWalletType()))
                    .filter(item -> outActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            mainDto.setTransferOut(transferOut);
            statList.put("main", mainDto);
        }
        {
            StatWalletDto shareDto = new StatWalletDto();
            BigDecimal transferIn = list.stream()
                    .filter(item -> "share".equals(item.getWalletType()))
                    .filter(item -> inActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            shareDto.setTransferIn(transferIn);
            BigDecimal transferOut = list.stream()
                    .filter(item -> "share".equals(item.getWalletType()))
                    .filter(item -> outActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            shareDto.setTransferOut(transferOut);
            statList.put("share", shareDto);
        }
        {
            StatWalletDto rechargeDto = new StatWalletDto();
            BigDecimal transferIn = list.stream()
                    .filter(item -> "store".equals(item.getWalletType()))
                    .filter(item -> inActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            rechargeDto.setTransferIn(transferIn);
            BigDecimal transferOut = list.stream()
                    .filter(item -> "store".equals(item.getWalletType()))
                    .filter(item -> outActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            rechargeDto.setTransferOut(transferOut);
            statList.put("store", rechargeDto);
        }
        {
            StatWalletDto physicalDto = new StatWalletDto();
            BigDecimal transferIn = list.stream()
                    .filter(item -> "physical".equals(item.getWalletType()))
                    .filter(item -> inActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            physicalDto.setTransferIn(transferIn);
            BigDecimal transferOut = list.stream()
                    .filter(item -> "physical".equals(item.getWalletType()))
                    .filter(item -> outActionList.contains(item.getAction()))
                    .map(UserWalletLog::getChangeMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            physicalDto.setTransferOut(transferOut);
            statList.put("physical", physicalDto);
        }
        return statList;
    }
}
