package com.zpay.admin.handler;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.zpay.admin.dao.UserRepository;
import com.zpay.admin.model.User;
import generator.domain.UserDevConfig;
import generator.service.UserDevConfigService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.util.MD5Util;
import xyz.erupt.core.view.EruptApiModel;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserDataProxy implements DataProxy<User> {

    private final String ZNET_SECRET = "E@jtLW%eTCjy6rH0C2K^";

    @Autowired
    UserService userService;

    @Override
    public void addBehavior(User m) {
        m.setStatus(1);
        m.setBusinessStatus(1);
        m.setPassword("Asd123");
//        m.setMoney(0F);
        m.setWithdrawRate(1F);
        m.setRechargeRate(1F);
    }

    @Override
    public void beforeAdd(User u) {
        Integer bid = this.genBid();
        u.setBusinessId(bid.toString());
        String salt = this.genSalt();
        u.setSalt(salt);
        String pwd0 = u.getPassword();
        if (pwd0 == null) {
            pwd0 = "Asd123";
        } else {
            this.checkPwd(pwd0);
        }
        String pwd = MD5Util.digest(pwd0 + salt);
        u.setPassword(pwd);
        Date nowTime = new Date();
        u.setCreateTime(nowTime);
        u.setUpdateTime(nowTime);
    }

    @Override
    public void afterAdd(User u) {
        saveUserModule(u);
    }


    @Override
    public void editBehavior(User m) {
        m.setEnableModule(getUserModule(Integer.valueOf(m.getBusinessId())));
    }

    @Override
    public void beforeUpdate(User m) {
        String pwd0 = m.getPassword();
        if (pwd0.length() != 32) {
            this.checkPwd(pwd0);
            String pwd = MD5Util.digest(pwd0 + m.getSalt());
            m.setPassword(pwd);
        }
        m.setUpdateTime(new Date());

        saveUserModule(m);
    }


    @Override
    public String beforeFetch(List<Condition> conditions) {
        return null;
    }

    @Autowired
    UserDevConfigService userDevConfigService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            map.put("enableModule", getUserModule(Integer.valueOf((String) map.get("businessId"))));
        }
    }


    private String genSalt() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "").substring(0, 8);
    }

    @Autowired
    private UserRepository userRepo;

    private Integer genBid() {
        Random random = new Random();
        int min = 100000000;
        int max = 999999999;
        int bid = random.nextInt(max - min + 1) + min;
//        User user = userRepo.findByBusinessId(bid);
        generator.domain.User user = userService.getUserByBusinessId(bid);
        if (user != null) {
            this.genBid();
        }
        return bid;
    }

    private void checkPwd(String pwd) {
        int pwdLen = pwd.length();
        if (pwdLen < 6 || pwdLen > 12 || !pwd.matches("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*")) {
            throw new EruptApiErrorTip("密码不合规: 6-12位且包含大小写字母及数字", EruptApiModel.PromptWay.MESSAGE);
        }
    }

    private String getUserModule(Integer businessId) {
        String enableModule = "";
        UserDevConfig devConfig = userDevConfigService.getUserDevConfigByBusinessId(businessId);
        if (devConfig != null) {
            enableModule = JSONUtil.parseArray(devConfig.getEnableModule())
                    .stream()
                    .map(Object::toString)
                    .collect(Collectors.joining("|"));
        }
        return enableModule;
    }

    private void saveUserModule(User user) {
        String[] modList = user.getEnableModule().split("\\|");
        Integer businessId = Integer.valueOf(user.getBusinessId());
        UserDevConfig devConfig = userDevConfigService.getUserDevConfigByBusinessId(businessId);
        if (devConfig == null) {
            devConfig = new UserDevConfig();
            devConfig.setUserId(user.getId().intValue());
            devConfig.setBusinessId(user.getBusinessId());
            devConfig.setAppId(DigestUtil.md5Hex(user.getId() + ZNET_SECRET).toUpperCase());
            devConfig.setAppSecret(DigestUtil.md5Hex(user.getBusinessId() + ZNET_SECRET).toUpperCase());
        }
        devConfig.setEnableModule(JSONUtil.toJsonStr(modList));
        userDevConfigService.saveOrUpdate(devConfig);
    }
}
