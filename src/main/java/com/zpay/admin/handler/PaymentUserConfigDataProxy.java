package com.zpay.admin.handler;

import com.zpay.admin.model.PaymentUserConfig;
import generator.domain.User;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class PaymentUserConfigDataProxy implements DataProxy<PaymentUserConfig> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            int bid = Integer.parseInt((String) map.get("businessId"));
            if (bid == 0) {
                map.put("businessName", "注册用户模版");
            } else {
                User user = userService.getUserByBusinessId(bid);
                map.put("businessName", user.getBusinessName());
            }
        }
    }


    @Override
    public void addBehavior(PaymentUserConfig c) {
        c.setStatus(Boolean.TRUE);
    }

    @Override
    public void beforeAdd(PaymentUserConfig c) {
        int bid = Integer.parseInt(c.getBusinessId());
        if (bid != 0) {
            User user = userService.getUserByBusinessId(bid);
            if (user == null) {
                throw new EruptApiErrorTip("商户不存在");
            }
        }
        c.setBusinessFee(c.getPlatformFee());
        c.setBusinessFeeRate(c.getPlatformFeeRate());
    }

}
