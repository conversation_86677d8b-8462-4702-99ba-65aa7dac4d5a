package com.zpay.admin.handler;

import com.zpay.admin.model.CardActionLog;
import generator.domain.User;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @updateTime 2024/8/21 15:37
 */
@Component
public class CardActionLogDataProxy implements DataProxy<CardActionLog> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }

}
