package com.zpay.admin.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.AttachmentProxy;

import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Service
public class Img2bProxy implements AttachmentProxy {

    @Override
    public String upLoad(InputStream inputStream, String path) {
        return null;
    }

    @Override
    public String fileDomain() {
        return null;
    }
}
