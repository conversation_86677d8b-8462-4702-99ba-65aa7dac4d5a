package com.zpay.admin.handler;

import com.zpay.admin.model.CardApplyOrder;
import generator.domain.CardHolder;
import generator.domain.MailingAddress;
import generator.domain.User;
import generator.service.CardHolderService;
import generator.service.MailingAddressService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Collection;
import java.util.Map;

import static generator.domain.table.MailingAddressTableDef.MAILING_ADDRESS;

/**
 * <AUTHOR>
 * @updateTime 2024/8/21 15:37
 */
@Component
public class CardApplyOrderDataProxy implements DataProxy<CardApplyOrder> {

    @Autowired
    UserService userService;
    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    MailingAddressService mailingAddressService;


    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());

            map.put("address", "");
            map.put("chineseName", "");
            map.put("englishName", "");
            MailingAddress mailingAddress = mailingAddressService.queryChain()
                    .from(MAILING_ADDRESS)
                    .where(MAILING_ADDRESS.CARD_ID.eq(map.get("cardId")))
                    .one();
            if (mailingAddress != null) {
                if (map.get("action").equals(1)) {
                    map.put("address", mailingAddress.getAddress());
                    map.put("userName", mailingAddress.getUserName());
                    map.put("phone", mailingAddress.getNationCode() + mailingAddress.getPhone());
                }

                CardHolder cardHolder = cardHolderService.getById(mailingAddress.getHolderId());
                map.put("chineseName", cardHolder.getChineseName());
                map.put("englishName", cardHolder.getEnglishName());
            }

        }
    }

}
