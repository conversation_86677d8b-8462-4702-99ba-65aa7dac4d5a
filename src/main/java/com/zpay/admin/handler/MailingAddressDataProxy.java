package com.zpay.admin.handler;

import com.zpay.admin.model.MailingAddress;
import generator.domain.UserCard;
import generator.service.UserCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MailingAddressDataProxy implements DataProxy<MailingAddress> {

    @Autowired
    UserCardService userCardService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            UserCard card = userCardService.getOneByCardId((String) map.get("cardId"));
            if (card != null) {
                map.put("cardNumber", card.getCardNumber());
            } else {
                UserCard card2 = userCardService.getOneByOrderNo((String) map.get("cardId"));
                if (card2 != null) {
                    map.put("cardNumber", card2.getCardNumber());
                } else {
                    map.put("cardNumber", "卡片不存在");
                }
            }
        }
    }

    /**
     * 查询前动态注入条件
     *
     * @param conditions 前端已传递条件，如果某个条件可做 remove 等操作，通过返回值做额外条件处理
     * @return 自定义查询条件(HQL语句)，列占位符用字段名，可做 OR 拼接等操作，如：(xxx.id = 'a' or name = 'b')
     */
    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = null;
        if (conditions != null && !conditions.isEmpty()) {
            String cardNo = conditions.get(0).getValue().toString();
            List<UserCard> cards = userCardService.getCardIdByCardNo(cardNo);
            List<String> cardIds = new ArrayList<>();
            for (UserCard card : cards) {
                cardIds.add(card.getCardId());
                cardIds.add(card.getOrderNo());
            }
            String result = cardIds.stream().map(e -> "'" + e + "'").collect(Collectors.joining(","));
            sql = String.format("card_id in (%s)", result);
            conditions.remove(conditions.size() - 1);
        }
        return sql;
    }
}
