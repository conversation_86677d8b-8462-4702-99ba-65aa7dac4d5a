package com.zpay.admin.handler;

import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.zpay.admin.model.BusinessDayModuleLog;
import generator.domain.User;
import generator.service.BusinessDayModuleLogService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.model.Column;
import xyz.erupt.annotation.model.Row;
import xyz.erupt.annotation.query.Condition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @updateTime 2024/8/21 15:37
 */
@Component
public class BusinessDayModuleLogDataProxy implements DataProxy<BusinessDayModuleLog> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }


    @Autowired
    BusinessDayModuleLogService businessDayModuleLogService;


    @Override
    public List<Row> extraRow(List<Condition> conditions) {
        List<Row> rows = new ArrayList<>();
        List<Column> columns = new ArrayList<>();
        columns.add(Column.builder().value("汇总").build());
        columns.add(Column.builder().value("全部商户").build());
        columns.add(Column.builder().value("不限定日期").className("text-center").colspan(2).build());
        columns.add(Column.builder().value("全部模块").build());

        QueryWrapper queryWrapper = new QueryWrapper();
        for (Condition condition : conditions) {
            switch (condition.getKey()) {
                case "businessId":
                    queryWrapper.eq("business_id", condition.getValue());
                    columns.get(1).setValue((String) condition.getValue());
                    break;
                case "statDate":
                    ArrayList<?> list = (ArrayList<?>) condition.getValue();
                    queryWrapper.between("stat_date", list.get(0), list.get(1));
                    columns.get(2).setValue(DateUtil.formatDate(DateUtil.parse((String) list.get(0))) + " --> " + DateUtil.formatDate(DateUtil.parse((String) list.get(1))));
                    break;
                case "walletType":
                    queryWrapper.eq("wallet_type", condition.getValue());
                    columns.get(3).setValue((String) condition.getValue());
                    break;
            }
        }
        List<generator.domain.BusinessDayModuleLog> list = businessDayModuleLogService.list(queryWrapper);
        // 循环统计日志总和数据
//        BigDecimal totalTransferIn = BigDecimal.ZERO;
//        BigDecimal totalTransferOut = BigDecimal.ZERO;
//        BigDecimal totalConsume = BigDecimal.ZERO;
//        BigDecimal totalDisburse = BigDecimal.ZERO;
//        BigDecimal totalBusinessEarn = BigDecimal.ZERO;
//        BigDecimal totalPlatformEarn = BigDecimal.ZERO;
//
//        for (generator.domain.BusinessDayModuleLog log : list) {
//            totalTransferIn = totalTransferIn.add(log.getTransferIn());
//            totalTransferOut = totalTransferOut.add(log.getTransferOut());
//            totalConsume = totalConsume.add(log.getConsume());
//            totalDisburse = totalDisburse.add(log.getDisburse());
//            totalBusinessEarn = totalBusinessEarn.add(log.getBusinessEarn());
//            totalPlatformEarn = totalPlatformEarn.add(log.getPlatformEarn());
//        }
        // 循环统计日志总和数据
        BigDecimal totalTransferIn = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getTransferIn)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalTransferOut = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getTransferOut)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalConsume = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getConsume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalDisburse = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getDisburse)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalBusinessEarn = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getBusinessEarn)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalPlatformEarn = list.stream()
                .map(generator.domain.BusinessDayModuleLog::getPlatformEarn)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        columns.add(Column.builder().value(totalTransferIn.toString()).className("text-right").build());
        columns.add(Column.builder().value(totalTransferOut.toString()).className("text-right").build());
        columns.add(Column.builder().value(totalConsume.toString()).className("text-right").build());
        columns.add(Column.builder().value(totalDisburse.toString()).className("text-right").build());
        columns.add(Column.builder().value(totalBusinessEarn.toString()).className("text-right").build());
        columns.add(Column.builder().value(totalPlatformEarn.toString()).className("text-right").build());
//        columns.add(Column.builder().value(100 + "").colspan(6).className("text-red").build());
        rows.add(Row.builder().columns(columns).build());
        return rows;
    }

}
