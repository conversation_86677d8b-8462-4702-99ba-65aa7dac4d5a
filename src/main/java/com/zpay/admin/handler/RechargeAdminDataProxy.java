package com.zpay.admin.handler;

import com.zpay.admin.model.RechargeAdmin;
import generator.domain.UserWallet;
import generator.domain.UserWalletLog;
import generator.service.UserService;
import generator.service.UserWalletLogService;
import generator.service.UserWalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

import static generator.domain.table.UserWalletTableDef.USER_WALLET;


/**
 * <AUTHOR>
 */
@Component
public class RechargeAdminDataProxy implements DataProxy<RechargeAdmin> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            generator.domain.User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }


    @Override
    public void addBehavior(RechargeAdmin r) {
        r.setWalletType("main");
    }


    @Autowired
    UserWalletService userWalletService;
    @Autowired
    UserWalletLogService userWalletLogService;

    @Override
    public void beforeAdd(RechargeAdmin r) {
        if (r.getMoney().equals(BigDecimal.ZERO)) {
            throw new EruptApiErrorTip("金额为 0", EruptApiModel.PromptWay.MESSAGE);
        }
        UserWallet userWallet = userWalletService.queryChain()
                .from(USER_WALLET)
                .where(USER_WALLET.BUSINESS_ID.eq(r.getBusinessId()))
                .one();
        if (userWallet == null) {
            throw new EruptApiErrorTip("商户钱包不存在", EruptApiModel.PromptWay.MESSAGE);
        }

        BigDecimal before;
        BigDecimal after;
        switch (r.getWalletType()) {
            case "main":
                before = userWallet.getMainWallet();
                after = before.add(r.getMoney());
                userWallet.setMainWallet(after);
                break;
            case "store":
                before = userWallet.getStoreWallet();
                after = before.add(r.getMoney());
                userWallet.setStoreWallet(after);
                break;
            case "share":
                before = userWallet.getShareWallet();
                after = before.add(r.getMoney());
                userWallet.setShareWallet(after);
                break;
            case "physical":
                before = userWallet.getPhysicalWallet();
                after = before.add(r.getMoney());
                userWallet.setPhysicalWallet(after);
                break;
            case "usdt":
                before = userWallet.getUsdtWallet();
                after = before.add(r.getMoney());
                userWallet.setUsdtWallet(after);
                break;
            case "payment":
                before = userWallet.getPaymentWallet();
                after = before.add(r.getMoney());
                userWallet.setPaymentWallet(after);
                break;
            default:
                throw new EruptApiErrorTip("钱包类型错误", EruptApiModel.PromptWay.MESSAGE);

        }
        userWalletService.updateById(userWallet);

        Integer action = r.getMoney().signum() == 1 ? 91 : 92;
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userWallet.getUserId());
        userWalletLog.setBusinessId(userWallet.getBusinessId());
        userWalletLog.setAction(action);
        userWalletLog.setWalletType(r.getWalletType());
        userWalletLog.setChangeBefore(before);
        userWalletLog.setChangeMoney(r.getMoney());
        userWalletLog.setChangeAfter(after);
        userWalletLog.setNote(r.getNote());
        userWalletLog.setCreateTime(new Date());
        userWalletLogService.save(userWalletLog);
    }


}
