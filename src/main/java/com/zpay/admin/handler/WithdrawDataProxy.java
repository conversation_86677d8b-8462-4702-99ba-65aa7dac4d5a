package com.zpay.admin.handler;

import com.zpay.admin.action.UserAction;
import com.zpay.admin.dao.UserRepository;
import com.zpay.admin.model.Withdraw;
import com.zpay.admin.service.RpcService;
import generator.domain.UserWallet;
import generator.domain.UserWalletLog;
import generator.service.UserService;
import generator.service.UserWalletLogService;
import generator.service.UserWalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.module.MetaUserinfo;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.upms.service.EruptUserService;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static generator.domain.table.UserWalletTableDef.USER_WALLET;

/**
 * <AUTHOR>
 */
@Service
public class WithdrawDataProxy implements DataProxy<Withdraw> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            generator.domain.User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }

    @Autowired
    private UserAction user;

    @Autowired
    private EruptUserService eruptUserService;

    @Autowired
    private UserRepository userRepo;


    private final Lock lock = new ReentrantLock();

    @Autowired
    UserWalletService userWalletService;
    @Autowired
    UserWalletLogService userWalletLogService;
    @Autowired
    RpcService rpcService;

    @Override
    public void beforeUpdate(Withdraw w) {
        Integer checkStatusNow = w.getStatus();

        if (!lock.tryLock()) {
            throw new EruptApiErrorTip("不能重复提交", EruptApiModel.PromptWay.MESSAGE);
        }
        try {
            UserWallet userWallet = userWalletService.queryChain()
                    .from(USER_WALLET)
                    .where(USER_WALLET.BUSINESS_ID.eq(w.getBusinessId()))
                    .one();
            // 0 待审核 1 驳回  2 通过  9 成功
            switch (checkStatusNow) {
                case 1:
                    // 驳回
                    BigDecimal changeBefore = userWallet.getMainWallet();
                    BigDecimal changeMoney = w.getAmount().add(w.getFee());
                    BigDecimal changeAfter = changeBefore.add(changeMoney);

                    userWallet.setMainWallet(changeAfter);
                    userWalletService.updateById(userWallet);

                    UserWalletLog userWalletLog = new UserWalletLog();
                    userWalletLog.setUserId(userWallet.getUserId());
                    userWalletLog.setBusinessId(userWallet.getBusinessId());
                    userWalletLog.setAction(93);
                    userWalletLog.setWalletType("main");
                    userWalletLog.setChangeBefore(userWallet.getMainWallet());
                    userWalletLog.setChangeMoney(changeMoney);
                    userWalletLog.setChangeAfter(changeAfter);
                    userWalletLog.setCreateTime(new Date());
                    userWalletLog.setNote("提现驳回");
                    userWalletLogService.save(userWalletLog);

                    break;
                case 2:
                    // 通过
                    rpcService.sendReqNow("withdraw/" + w.getId(), null);
                    break;
                default:
                    break;
            }
            MetaUserinfo userinfo = eruptUserService.getSimpleUserInfo();
        } finally {
            lock.unlock();
        }

    }
}
