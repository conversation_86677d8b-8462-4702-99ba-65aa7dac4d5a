package com.zpay.admin.handler;

import com.zpay.admin.model.Message;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class MessageDataProxy implements DataProxy<Message> {

    @Override
    public void addBehavior(Message m) {
        m.setStatus(true);
        m.setLang(1);
    }

    @Override
    public void beforeAdd(Message m) {
        m.setCreateTime(new Date());
        m.setUpdateTime(new Date());
        if (m.getSort() == null) {
            m.setSort(0);
        }
    }

    @Override
    public void editBehavior(Message m) {
    }

    @Override
    public void beforeUpdate(Message m) {
        m.setUpdateTime(new Date());
    }
}
