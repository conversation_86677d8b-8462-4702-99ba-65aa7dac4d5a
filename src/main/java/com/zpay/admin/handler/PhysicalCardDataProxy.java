package com.zpay.admin.handler;

import cn.hutool.core.date.DateUtil;
import com.zpay.admin.model.PhysicalCard;
import generator.domain.UserCardManage;
import generator.service.UserCardManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class PhysicalCardDataProxy implements DataProxy<PhysicalCard> {

    @Autowired
    UserCardManageService userCardManageService;

    @Override
    public void addBehavior(PhysicalCard c) {
        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        int random = (int) (Math.random() * 100);
        c.setCardId("ZP0GS" + dateStr + random);
        c.setAmount((float) 0);
        c.setCardStatus("Active");
    }

    @Override
    public void beforeAdd(PhysicalCard c) {
        UserCardManage cardManage = userCardManageService.getCardManageByCardModelAndCardBin("physical", c.getCardScheme());
        c.setCardModel("physical");
        c.setChannel("ZPhysical");
        c.setCardManageId(cardManage.getId());
        c.setCardCurrency("USD");
        c.setAmount((float) 0);
        c.setVirtualAmt((float) 0);
        c.setCreateTime(new Date());
        c.setProductCode("ZP0GS");
        c.setCardStatus("Active");
        c.setCardId(genCardId());
        c.setStatus(0);
    }

    @Override
    public void editBehavior(PhysicalCard c) {
    }

    @Override
    public void beforeUpdate(PhysicalCard c) {
    }

    private String genCardId() {
        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        int random = (int) (Math.random() * 100);
        return "ZP0GS" + dateStr + random;
//        return IdUtil.fastSimpleUUID();
    }
}
