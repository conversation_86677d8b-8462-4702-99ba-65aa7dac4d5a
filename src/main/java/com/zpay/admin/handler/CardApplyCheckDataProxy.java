package com.zpay.admin.handler;

import cn.hutool.json.JSONObject;
import com.zpay.admin.model.CardApplyCheck;
import com.zpay.admin.service.RpcService;
import generator.domain.CardHolder;
import generator.domain.MailingAddress;
import generator.domain.User;
import generator.domain.UserCard;
import generator.service.CardHolderService;
import generator.service.MailingAddressService;
import generator.service.UserCardService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static generator.domain.table.MailingAddressTableDef.MAILING_ADDRESS;


/**
 * <AUTHOR>
 */
@Component
public class CardApplyCheckDataProxy implements DataProxy<CardApplyCheck> {

    @Autowired
    UserService userService;
    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    MailingAddressService mailingAddressService;


    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());

            map.put("address", "");
            map.put("chineseName", "");
            map.put("englishName", "");
            MailingAddress mailingAddress = mailingAddressService.queryChain()
                    .from(MAILING_ADDRESS)
                    .where(MAILING_ADDRESS.CARD_ID.eq(map.get("cardId")))
                    .one();
            if (mailingAddress != null) {
                if (map.get("action").equals(1)) {
                    map.put("address", mailingAddress.getAddress());
                    map.put("userName", mailingAddress.getUserName());
                    map.put("phone", mailingAddress.getNationCode() + mailingAddress.getPhone());
                }

                CardHolder cardHolder = cardHolderService.getById(mailingAddress.getHolderId());
                map.put("chineseName", cardHolder.getChineseName());
                map.put("englishName", cardHolder.getEnglishName());
            }

        }
    }

    @Override
    public void addBehavior(CardApplyCheck card) {
    }


    @Override
    public void beforeAdd(CardApplyCheck card) {

    }

    @Autowired
    UserCardService userCardService;
    @Autowired
    RpcService rpcService;

    @Override
    public void beforeUpdate(CardApplyCheck cardApply) {
        switch (cardApply.getAction()) {
            case 1:
                // 开卡
                doOpenCard(cardApply);
                break;
            case 8:
                // 销卡
                doCancelCard(cardApply);
                break;
            default:
                break;
        }
        if (cardApply.getNote() == null) {
            cardApply.setNote("");
        }
        cardApply.setUpdateTime(new Date());
    }


    @Override
    public void beforeDelete(CardApplyCheck card) {

    }


    private void doOpenCard(CardApplyCheck cardApply) {
        UserCard userCard = userCardService.getOneByCardId(cardApply.getCardId());
        CardHolder cardHolder = cardHolderService.getById(userCard.getHolderId());
        // 开卡
        switch (cardApply.getStatus()) {
            case 3:
                // 处理成功
                userCard.setStatus(1);
                userCardService.updateById(userCard);

                cardHolder.setStatus(1);
                cardHolderService.updateById(cardHolder);
                break;
            case 5:
                // 提交 FTP
                if (!List.of(1, 3).contains(cardHolder.getStatus())) {
                    throw new EruptApiErrorTip("请先审核持卡人 KYC 信息", EruptApiModel.PromptWay.MESSAGE);
                }
                JSONObject body = new JSONObject();
                body.putOnce("cardId", cardApply.getCardId());
                rpcService.sendReqNow("ftp", body);
                break;
        }
    }

    private void doCancelCard(CardApplyCheck cardApply) {
        UserCard userCard = userCardService.getOneByCardId(cardApply.getCardId());
        CardHolder cardHolder = cardHolderService.getById(userCard.getHolderId());
        switch (cardApply.getStatus()) {
            case 3:
                // 处理成功 目前先不做处理
                
                break;
            default:
                break;
        }
    }

}
