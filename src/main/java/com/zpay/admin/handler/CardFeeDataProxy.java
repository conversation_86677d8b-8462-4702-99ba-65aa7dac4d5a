package com.zpay.admin.handler;

import com.zpay.admin.dao.CardManageRepository;
import com.zpay.admin.model.CardFeeConfig;
import com.zpay.admin.model.CardManage;
import generator.domain.User;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Component
public class CardFeeDataProxy implements DataProxy<CardFeeConfig> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            int bid = Integer.parseInt((String) map.get("businessId"));
            if (bid == 0) {
                map.put("businessName", "注册用户模版");
            } else {
                User user = userService.getUserByBusinessId(bid);
                map.put("businessName", user.getBusinessName());
            }
        }
    }

    @Autowired
    private CardManageRepository cardManageRepo;

    @Resource
    private EruptDao eruptDao;

    @Override
    public void addBehavior(CardFeeConfig cf) {
        cf.setIsAble(1);
    }


    @Override
    public void beforeAdd(CardFeeConfig cf) {
        int bid = Integer.parseInt(cf.getBusinessId());
        if (bid != 0) {
            User user = userService.getUserByBusinessId(bid);
            if (user == null) {
                throw new EruptApiErrorTip("商户不存在");
            }
        }

        Optional<CardManage> card = this.checkFeeSet(cf);
        CardManage manage = card.get();

        cf.setCostOpenCardFee(manage.getCardOpenFee());
        cf.setCostChargeRate(manage.getCardChargeRate());
        cf.setCostCancelCardFee(manage.getCardCancelFee());
        cf.setCardBin(manage.getCardDuan());

        cf.setBusinessOpenCardFee(cf.getPlatformOpenCardFee());
        cf.setBusinessChargeRate(cf.getPlatformChargeRate());
        cf.setBusinessCancelCardFee(cf.getPlatformCancelCardFee());

        Float singleLimit = cf.getCardSingleLimit() != null ? cf.getCardSingleLimit() : manage.getCardDanciXiaofeiMax();
        Float monthLimit = cf.getCardMonthLimit() != null ? cf.getCardMonthLimit() : manage.getCardMonthXiaofeiMax();
        Float totalLimit = cf.getCardTotalLimit() != null ? cf.getCardTotalLimit() : manage.getCardChuzhiMax();

        cf.setCardSingleLimit(singleLimit);
        cf.setCardMonthLimit(monthLimit);
        cf.setCardTotalLimit(totalLimit);
    }

    @Override
    public void beforeUpdate(CardFeeConfig cf) {
        this.checkFeeSet(cf);
    }


    @Override
    public void beforeDelete(CardFeeConfig cf) {

    }


    private Optional<CardManage> checkFeeSet(CardFeeConfig cf) {
        Integer cmId = cf.getCardManageId();
        Optional<CardManage> card = cardManageRepo.findById(Long.valueOf(cmId));
        if (card.isEmpty()) {
            throw new EruptApiErrorTip("卡片不存在");
        }

        Float openCardFee = card.get().getCardOpenFee();
        Float rechargeRate = card.get().getCardChargeRate();
        Float cancelCardFee = card.get().getCardCancelFee();

        Float platformOpenCardFee = cf.getPlatformOpenCardFee();
        Float platformRechargeRate = cf.getPlatformChargeRate();
        Float platformCancelCardFee = cf.getPlatformCancelCardFee();

        if (platformOpenCardFee < openCardFee) {
            throw new EruptApiErrorTip("开卡费小于成本", EruptApiModel.PromptWay.MESSAGE);
        }
        if (platformRechargeRate < rechargeRate) {
            throw new EruptApiErrorTip("充值费率小于成本", EruptApiModel.PromptWay.MESSAGE);
        }
        if (platformCancelCardFee < cancelCardFee) {
            throw new EruptApiErrorTip("销卡费小于成本", EruptApiModel.PromptWay.MESSAGE);
        }

        boolean transFeeCheck = (cf.getPreAuthFee() == -1 &&
                cf.getCrossBroadFeeRate() == -1 &&
                cf.getTransactionFeeRate() == -1 &&
                cf.getRefundFee() == -1)
                || (cf.getPreAuthFee() != -1 &&
                cf.getCrossBroadFeeRate() != -1 &&
                cf.getTransactionFeeRate() != -1 &&
                cf.getRefundFee() != -1);
        if (!transFeeCheck) {
            throw new EruptApiErrorTip("交易费率设置不完整", EruptApiModel.PromptWay.MESSAGE);
        }

        return card;
    }


}
