package com.zpay.admin.handler;

import generator.domain.UserCardManage;
import generator.service.UserCardManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.ChoiceTrigger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @updateTime 2024/7/29 16:51
 */
@Component
public class CardChoiceTrigger implements ChoiceTrigger {

    @Autowired
    UserCardManageService userCardManageService;

    @Override
    public Map<String, Object> trigger(Object value, String[] params) {
        Integer cardManageId = Integer.parseInt(value.toString());
        UserCardManage cardManage = userCardManageService.getById(cardManageId);
        Map<String, Object> map = new HashMap<>(3);
        map.put("costOpenCardFee", cardManage.getCardOpenFee());
        map.put("costChargeRate", cardManage.getCardChargeRate());
        map.put("costCancelCardFee", cardManage.getCardCancelFee());
        return map;
    }
}
