package com.zpay.admin.handler;

import com.zpay.admin.dao.CardCategoryRepository;
import com.zpay.admin.dao.CardFeeRepository;
import com.zpay.admin.model.CardCategoryConfig;
import com.zpay.admin.model.CardManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;


/**
 * <AUTHOR>
 */
@Component
public class CardManageDataProxy implements DataProxy<CardManage> {

    @Autowired
    private CardCategoryRepository cardCategoryRepo;


    @Autowired
    private CardFeeRepository cardFeeRepo;

    @Override
    public void beforeAdd(CardManage c) {
        CardCategoryConfig cardCategory = cardCategoryRepo.findById(Long.valueOf(c.getCardCategory())).orElse(null);
        if (cardCategory == null) {
            throw new EruptApiErrorTip("卡片类型不存在");
        }

        c.setCardSource(cardCategory.getSource() + "---" + cardCategory.getProductName());
        if (c.getCardChangjing().split(",").length == 0) {
            throw new EruptApiErrorTip("场景值请用英文 , 分割");
        }
        if ("share".equals(c.getCardModel())) {
            if ("skyee".equals(cardCategory.getChannel())) {
                throw new EruptApiErrorTip(cardCategory.getSource() + "卡不支持设置为共享卡模式");
            }
        }
    }


    @Override
    public void beforeUpdate(CardManage c) {
        cardFeeRepo.updateCardCostFee(c);
    }

    @Override
    public void beforeDelete(CardManage c) {

    }


}
