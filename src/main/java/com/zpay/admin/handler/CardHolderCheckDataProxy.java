package com.zpay.admin.handler;

import com.zpay.admin.model.CardHolderCheck;
import com.zpay.admin.service.RpcService;
import generator.domain.User;
import generator.domain.UserCard;
import generator.service.CardApplyOrderService;
import generator.service.UserCardService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

import static generator.domain.table.CardApplyOrderTableDef.CARD_APPLY_ORDER;
import static generator.domain.table.UserCardTableDef.USER_CARD;


/**
 * <AUTHOR>
 */
@Component
public class CardHolderCheckDataProxy implements DataProxy<CardHolderCheck> {

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }

    @Override
    public void addBehavior(CardHolderCheck card) {
    }


    @Override
    public void beforeAdd(CardHolderCheck card) {

    }

    @Autowired
    RpcService rpcService;
    @Autowired
    CardApplyOrderService cardApplyOrderService;
    @Autowired
    UserCardService userCardService;

    @Override
    public void beforeUpdate(CardHolderCheck ch) {
        UserCard userCard = userCardService.queryChain()
                .from(USER_CARD)
                .where(USER_CARD.CARD_MODEL.eq("physical"))
                .where(USER_CARD.UID.eq(ch.getBusinessId()))
                .where(USER_CARD.HOLDER_ID.eq(ch.getId()))
                .orderBy(USER_CARD.OPEN_CARD_TIME, true)
                .limit(1)
                .one();
        if (userCard == null) {
            return;
        }
        switch (ch.getStatus()) {
            case 2:
                // 已驳回 , 同步执行申请单驳回
                cardApplyOrderService.updateChain()
                        .from(CARD_APPLY_ORDER)
                        .where(CARD_APPLY_ORDER.CARD_ID.eq(userCard.getCardId()))
                        .set(CARD_APPLY_ORDER.STATUS, 2)
                        .update();
                break;
            case 3:
                // 后台审核通过，执行 FTP 上传  取消执行此逻辑，改为到申请单处处理
//                JSONObject body = new JSONObject();
//                body.putOnce("holderId", ch.getId());
//                rpcService.sendReqNow("ftp", body);
                break;
            default:
                break;
        }
        ch.setUpdateTime(new Date());
    }


}
