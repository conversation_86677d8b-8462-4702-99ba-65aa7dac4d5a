package com.zpay.admin.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.zpay.admin.model.CardApplyOrder;
import generator.domain.User;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import generator.mapper.UserCardLogMapper;
import generator.service.UserCardService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.model.Column;
import xyz.erupt.annotation.model.Row;
import xyz.erupt.annotation.query.Condition;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/8/21 15:37
 */
@Component
public class UserCardLogDataProxy implements DataProxy<CardApplyOrder> {

    @Autowired
    UserService userService;
    @Autowired
    UserCardLogMapper userCardLogMapper;
    @Autowired
    UserCardService userCardService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String sql = null;
        if (conditions != null && !conditions.isEmpty()) {
            List<Integer> is = new ArrayList<>();
            for (int i = 0; i < conditions.size(); i++) {
                Condition condition = conditions.get(i);
                if ("channel".equals(condition.getKey())) {
                    List<UserCard> channel = userCardService.list(new QueryWrapper().eq("channel", condition.getValue()));
                    List<String> cardIds = channel.stream().map(c -> c.getCardId()).collect(Collectors.toList());
                    String result = cardIds.stream().map(e -> "'" + e + "'").collect(Collectors.joining(","));
                    sql = String.format("card_id in (%s)", result);
                    is.add(i);
                }
            }
            if (is != null && !is.isEmpty()) {
                for (Integer i : is) {
                    int in = (int) i;
                    conditions.remove(in);
                }
            }
        }
        return sql;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }

    @Override
    public List<Row> extraRow(List<Condition> conditions) {
        generator.domain.UserCardLog userCardLog = new generator.domain.UserCardLog();
        if (conditions != null && !conditions.isEmpty()) {
            for (Integer i = 0; i < conditions.size(); i++) {
                Condition condition = conditions.get(i);
                if ("businessId".equals(condition.getKey())) {
                    userCardLog.setBusinessId(condition.getValue().toString());
                } else if ("noticeType".equals(condition.getKey())) {
                    userCardLog.setNoticeType(condition.getValue().toString());
                } else if ("orderId".equals(condition.getKey())) {
                    userCardLog.setOrderId(condition.getValue().toString());
                } else if ("cardId".equals(condition.getKey())) {
                    userCardLog.setCardId(condition.getValue().toString());
                } else if ("maskCardNumber".equals(condition.getKey())) {
                    userCardLog.setMaskCardNumber(condition.getValue().toString());
                } else if ("cardModel".equals(condition.getKey())) {
                    userCardLog.setCardModel(condition.getValue().toString());
                } else if ("status".equals(condition.getKey())) {
                    userCardLog.setStatus(condition.getValue().toString());
                } else if ("transactionType".equals(condition.getKey())) {
                    userCardLog.setTransactionType(condition.getValue().toString());
                } else if ("createTime".equals(condition.getKey())) {
                    JSONArray a = JSONUtil.parseArray(condition.getValue());
                    userCardLog.setCreateTime(DateUtil.parseDate(a.get(0).toString()));
                    userCardLog.setCreateDate(DateUtil.parseDate(a.get(1).toString()));
                } else if ("channel".equals(condition.getKey())) {
                    userCardLog.setProductName(condition.getValue().toString());
                }
            }
        }
        Map<String, Double> percentages = calculateStatusPercentages(userCardLog);
        //行对象
        List<Row> rows = new ArrayList<>();
        //列对象
        List<Column> columns = new ArrayList<>();
        columns.add(Column.builder().value("统计").colspan(2).build());
        if (conditions != null && !conditions.isEmpty()) {
            for (Integer i = 0; i < conditions.size(); i++) {
                Condition condition = conditions.get(i);
                if ("businessId".equals(condition.getKey())) {
                    columns.add(Column.builder().value("商户id：" + condition.getValue()).colspan(2).className("text-red").build());
                }
            }
        }
        Double success = percentages.get("authSuccessed");
        columns.add(Column.builder().value("交易成功率：" + success + "%").colspan(2).className("text-red").build());
        columns.add(Column.builder().value("交易失败率：" + percentages.get("authFailure") + "%").colspan(2).className("text-red").build());
        rows.add(Row.builder().columns(columns).build());
        return rows;
    }

    // 计算状态百分比
    private Map<String, Double> calculateStatusPercentages(generator.domain.UserCardLog queryDto) {

        QueryWrapper queryWrapper = createQueryWrapper(queryDto);
        // 1. 查询数据库获取所有数据
        List<generator.domain.UserCardLog> allLogs = userCardLogMapper.selectListByQuery(queryWrapper);

        // 2. 统计状态数量
        long authFailureCount = 0;
        long authSuccessedCount = 0;
        long other = 0;
        long totalCount = allLogs.size();

        for (generator.domain.UserCardLog log : allLogs) {
            String status = log.getStatus();
            if (status == null) continue;
            switch (status) {
                case "Settled":
                    authSuccessedCount++;
                    break;
                case "AuthFailure":
                    authFailureCount++;
                    break;
                case "AuthSuccessed":
                    authSuccessedCount++;
                    break;
                default:
                    other++;
                    break;
            }
        }
        System.out.println("s:" + authFailureCount + "  e:" + authSuccessedCount);
        // 3. 计算占比
        Map<String, Double> percentages = new HashMap<>();
        percentages.put("authFailure", calculatePercentage(authFailureCount, totalCount));
        percentages.put("authSuccessed", calculatePercentage(authSuccessedCount, totalCount));
        percentages.put("other", calculatePercentage(other, totalCount));
        return percentages;
    }

    // 根据查询条件创建 QueryWrapper
    private QueryWrapper createQueryWrapper(generator.domain.UserCardLog queryDto) {

        QueryWrapper wrapper = new QueryWrapper();
        //        List<String> list = Arrays.asList("Auth", "Verification", "Refund", "TransFee", "RefundTransFee");
        wrapper.eq(UserCardLog::getTransactionType, "Auth");
        // 添加查询条件
        if (queryDto.getBusinessId() != null) {
            wrapper.like(UserCardLog::getBusinessId, queryDto.getBusinessId());
        }
        if (queryDto.getCardId() != null) {
            wrapper.like(UserCardLog::getCardId, queryDto.getCardId());
        }
        if (queryDto.getNoticeType() != null) {
            wrapper.eq(UserCardLog::getNoticeType, queryDto.getNoticeType());
        }
        if (queryDto.getOrderId() != null) {
            wrapper.like(UserCardLog::getOrderId, queryDto.getOrderId());
        }
        if (queryDto.getMaskCardNumber() != null) {
            wrapper.like(UserCardLog::getMaskCardNumber, queryDto.getMaskCardNumber());
        }
        if (queryDto.getCardModel() != null) {
            wrapper.eq(UserCardLog::getCardModel, queryDto.getCardModel());
        }
        if (queryDto.getStatus() != null) {
            wrapper.eq(UserCardLog::getStatus, queryDto.getStatus());
        }
        if (queryDto.getCreateTime() != null) {
            wrapper.between(UserCardLog::getCreateTime, queryDto.getCreateTime(), queryDto.getCreateDate());
        }
        if (queryDto.getProductName() != null) {
            List<UserCard> channel = userCardService.list(new QueryWrapper().eq("channel", queryDto.getProductName()));
            if (channel != null && !channel.isEmpty()) {
                List<String> cardIds = channel.stream().map(c -> c.getCardId()).collect(Collectors.toList());
                wrapper.in(UserCardLog::getCardId, cardIds);
            }
        }
        return wrapper;
    }

    // 计算百分比方法
    private double calculatePercentage(long count, long total) {
        if (total == 0) return 0.0;
        double percentage = (count * 100.0) / total;
        return Math.round(percentage * 100.0) / 100.0; // 保留两位小数
    }
}
