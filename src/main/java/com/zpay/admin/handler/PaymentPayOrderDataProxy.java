package com.zpay.admin.handler;

import com.zpay.admin.model.PaymentPayOrder;
import generator.domain.User;
import generator.service.PaymentPayPayeeService;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.DataProxy;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class PaymentPayOrderDataProxy implements DataProxy<PaymentPayOrder> {

    @Autowired
    PaymentPayPayeeService paymentPayPayeeService;

    @Autowired
    UserService userService;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
//            PaymentPayPayee payee = paymentPayPayeeService.queryChain()
//                    .from(PAYMENT_PAY_PAYEE)
//                    .select(PAYMENT_PAY_PAYEE.CURRENCY)
//                    .where(PAYMENT_PAY_PAYEE.PAYEE_ID.eq(map.get("payeeId")))
//                    .one();
//            map.put("toCurrency", payee.getCurrency());
            User user = userService.getUserByBusinessId(Integer.valueOf((String) map.get("businessId")));
            map.put("businessName", user.getBusinessName());
        }
    }

    @Override
    public void addBehavior(PaymentPayOrder m) {

    }

    @Override
    public void beforeAdd(PaymentPayOrder m) {

    }

    @Override
    public void editBehavior(PaymentPayOrder m) {
    }

    @Override
    public void beforeUpdate(PaymentPayOrder m) {
    }
}
