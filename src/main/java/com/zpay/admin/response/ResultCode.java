package com.zpay.admin.response;

import lombok.Getter;


/**
 * 枚举了一些常用API操作码
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@Getter
public enum ResultCode implements IErrorCode {

    /**
     * 操作码
     */
    SUCCESS(200, "操作成功"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "未登录"),
    FORBIDDEN(403, "没有相关权限"),
    NOT_FIND(404, "资源不存在"),
    SERVER_ERR(500, "服务器异常"),
    FAILED(9000, "操作失败"),
    SYS_ERR(9999, "系统异常");


    private final long code;
    private final String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }


}
