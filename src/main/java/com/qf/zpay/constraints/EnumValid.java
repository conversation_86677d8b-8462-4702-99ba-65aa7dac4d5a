package com.qf.zpay.constraints;


import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/6/26 14:39
 */
public interface EnumValid {

    /**
     * 编码: 用于数据校验
     *
     * @return 编码 code
     */
    String getCode();

    /**
     * 描述: 用于人性化显示
     *
     * @return 描述 desc
     */
    String getDesc();

    /**
     * 获取所有的枚举值 code
     *
     * @return 枚举值 code 列表
     */
    default List<Object> validValues() {
        Class<?> currentClass = getClass();
        return Arrays.stream(currentClass.getEnumConstants())
                .map(enumInstance -> {
                    try {
                        Field field = enumInstance.getClass().getDeclaredField("code");
                        field.setAccessible(true);
                        return field.get(enumInstance);
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
    }
}
