package com.qf.zpay.constants;

import lombok.Getter;

@Getter
public enum PayOrderStatusEnum {
    CREATED("CREATED", "已创建"),
    PAID("PAID", "已付款"),
    REVIEWING("REVIEWING", "审核中"),
    PENDING("PENDING", "付款渠道处理中"),
    REJECTED("REJECTED", "已拒绝"),
    COMPLETED("COMPLETED", "已完成"),
    FAILED("FAILED", "付款失败"),
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    PayOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return code + ": " + description;
    }
}
