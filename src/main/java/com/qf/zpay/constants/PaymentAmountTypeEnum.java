package com.qf.zpay.constants;

import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

import java.util.List;

@Getter
public enum PaymentAmountTypeEnum implements EnumValid {
    PAY_AMOUNT("PAY_AMOUNT", "提交的金额是支付的总金额"),
    RECEIVE_AMOUNT("RECEIVE_AMOUNT", "提交的金额是收款人收到的金额,收款金额的币种在创建付款人时已指定");

    private final String code;
    private final String desc;

    PaymentAmountTypeEnum(String code, String description) {
        this.code = code;
        this.desc = description;
    }

}