package com.qf.zpay.constants;

import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

@Getter
public enum PayeeEnum implements EnumValid {
    INDIVIDUAL("INDIVIDUAL","个人"),
    ENTERPRISE("ENTERPRISE","企业");
    private final String code;
    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private PayeeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
