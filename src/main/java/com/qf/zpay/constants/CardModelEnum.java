package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 13:45
 */
@Getter
public enum CardModelEnum implements EnumValid {

    /**
     * ShareBalance
     * 卡片模型
     */
    RECHARGE("recharge", "充值"),
    SHARE("share", "分享"),
    PHYSICAL("physical", "实体卡");

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardModelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CardModelEnum fromCode(String code) {
        for (CardModelEnum cardModel : CardModelEnum.values()) {
            if (cardModel.code.equals(code)) {
                return cardModel;
            }
        }
        throw new IllegalArgumentException("Invalid CardModelEnum code: " + code);
    }
}
