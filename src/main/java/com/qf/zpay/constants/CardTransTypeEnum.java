package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

@Getter
public enum CardTransTypeEnum implements EnumValid {

    /**
     * 卡片交易类型
     */
    // 消费
    Auth("Auth", "消费"),
    // 用卡人在平台操作绑定卡片并点击提交后，由平台立即发起验证交易，金额不限。
    Verification("Verification", "验证"),
    // 用卡人在平台发起消费，且平台订单状态为“成功”的情况下，因订单被平台、卖家取消或者用卡人取消/申请退款，平台将此类交易订单置为“退款”，款项会退回进行。
    Refund("Refund", "退款"),
    // 用卡人在平台发起消费，且平台订单状态为“成功”的情况下，因订单被平台取消或者用卡人主动取消，平台将此类交易订单置为“撤销”，款项会退回进行。
    Void("Void", "撤销退款"),
    // 用卡人在平台下单发起交易后，但持卡人在平台进行“撤销交易”或“申请退款”的操作，在平台交易订单状态为“已成功”的情况下，平台可能会向渠道发起“强制扣款”，且最终该笔交易在平台的订单状态为“成功”。此交易类型一定会扣款成功。
    CorrectiveAuth("CorrectiveAuth", "纠正授权"),
    // 当原始交易订单生成对应退款订单后，原始订单交易状态变更为“消费失败”，渠道会返回退款，通过规则进行系统筛查，判定退款订单是否为“可疑订单”。若不是“可疑订单”，人工操作正常退款。若是“可疑订单”，则需要用卡人配合提供材料，予以佐证可退款。
    CorrectiveRefund("CorrectiveRefund", "校正退款"),
    // 纠正退款交易的撤销状态，此交易类型和退款撤销类似，但撤销的类型是校正退款。
    CorrectiveRefundVoid("CorrectiveRefundVoid", "校正退款取消"),
    //注销卡⽚后，充值卡余额将退回⾄钱包余额内。
    DiscardRechargeReturn("DiscardRechargeReturn", "销卡余额返回"),
    //服务费
    ServiceFee("ServiceFee", "服务费"),
    //交易手续费
    TransFee("TransFee", "交易手续费"),
    //交易手续费
    RefundTransFee("RefundTransFee", "退款手续费"),
    //订单交易状态为退款，且渠道侧已经退款成功，⽤卡⼈、已收到退款。此时平台认定该笔的原始订单实际上不符合退款条件，
    RefundReversal("RefundReversal", "退款撤销"),
    // 系统退款同时退回手续费
    FeeReturn("FeeReturn", "手续费退回"),

    OpenCardFee("OpenCardFee", "开卡费"),
    CardRecharge("CardRecharge", "卡片充值"),
    RechargeFee("RechargeFee", "充值手续费"),
    CancelCardFee("CancelCardFee", "销卡费"),
    CardWithdraw("CardWithdraw", "卡片提出");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardTransTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
