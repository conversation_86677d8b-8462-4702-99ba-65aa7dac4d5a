package com.qf.zpay.constants;

import lombok.Getter;

@Getter
public enum PaymentCategoryEnum {
    SALARY("SALARY", "发薪"),
    FAMILY_SUPPORT("FAMILY_SUPPORT", "家庭支出"),
    CLOTHES_BAGS_SHOES("CLOTHES_BAGS_SHOES", "服装 鞋帽 箱包购物"),
    DAILY_SUPPLIES_AND_COSMETICS("DAILY_SUPPLIES_AND_COSMETICS", "化妆 日用品购物"),
    ELECTRONICS_AND_HOME_APPLIANCES("ELECTRONICS_AND_HOME_APPLIANCES", "数码家电购物"),
    TOYS_KIDS_BABIES("TOYS_KIDS_BABIES", "玩具 婴幼儿用品购物"),
    INTERPRETATION_SERVICE("INTERPRETATION_SERVICE", "口译服务费用"),
    TRANSLATION_SERVICE("TRANSLATION_SERVICE", "笔译服务费用"),
    HUMAN_RESOURCE_SERVICE("HUMAN_RESOURCE_SERVICE", "人才中介服务费用"),
    ESTATE_AGENCY_SERVICE("ESTATE_AGENCY_SERVICE", "房屋中介服务费用"),
    SOFTWARE_DEVELOPMENT_SERVICE("SOFTWARE_DEVELOPMENT_SERVICE", "软件开发者服务费用"),
    WEB_DESIGN_OR_DEVELOPMENT_SERVICE("WEB_DESIGN_OR_DEVELOPMENT_SERVICE", "网站开发/网页设计类服务费用"),
    DRAFTING_LEGAL_SERVICE("DRAFTING_LEGAL_SERVICE", "起草法务文件服务费用"),
    LEGAL_RELATED_CERTIFICATION_SERVICE("LEGAL_RELATED_CERTIFICATION_SERVICE", "法律相关认证服务费用"),
    ACCOUNTING_SERVICE("ACCOUNTING_SERVICE", "会计记录报表审计咨询规划服务费用"),
    TAX_SERVICE("TAX_SERVICE", "准备税务文件服务费用"),
    ARCHITECTURAL_DECORATION_DESIGN_SERVICE("ARCHITECTURAL_DECORATION_DESIGN_SERVICE", "建筑装潢设计服务费用"),
    ADVERTISING_SERVICE("ADVERTISING_SERVICE", "广告设计服务费用"),
    MARKET_RESEARCH_SERVICE("MARKET_RESEARCH_SERVICE", "市场调查服务费用"),
    EXHIBITION_BOOTH_SERVICE("EXHIBITION_BOOTH_SERVICE", "展会摊位租赁服务费用"),
    PRODUCT_PROMOTION_SERVICE("PRODUCT_PROMOTION_SERVICE", "产品内容推广服务费收入"),
    ECOMMERCE_PROMOTION_SERVICE("ECOMMERCE_PROMOTION_SERVICE", "电商成交订单佣金服务费收入");


    private final String code;
    private final String name;

    PaymentCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}