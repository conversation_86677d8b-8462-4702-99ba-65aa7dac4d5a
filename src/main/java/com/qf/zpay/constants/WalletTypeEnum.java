package com.qf.zpay.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 13:45
 */
@Getter
public enum WalletTypeEnum {

    /**
     * 账户钱包交易类型
     */
    RECHARGE(1, "充值"),
    WITHDRAW(2, "提现"),
    BE_FROM_STORE_WALLET(3, "来自储值卡钱包划转"),
    BE_FROM_SHARE_WALLET(4, "来自共享卡钱包划转"),
    BE_FROM_PHYSICAL_WALLET(5, "来自实体卡钱包划转"),
    BE_FROM_PAYMENT_WALLET(6, "来自代付钱包划转"),
    BE_FROM_USDT_WALLET(7, "来自USDT钱包划转"),
    BE_FROM_TOKEN_WALLET(8, "来自代币钱包划转"),

    TRANSFER_TO_STORE_WALLET(9, "划转至储值卡钱包"),
    TRANSFER_TO_SHARE_WALLET(10, "划转至共享卡钱包"),
    TRANSFER_TO_PHYSICAL_WALLET(11, "划转至实体卡钱包"),
    TRANSFER_TO_PAYMENT_WALLET(12, "划转至代付钱包"),
    TRANSFER_TO_USDT_WALLET(13, "划转至USDT钱包"),
    TRANSFER_TO_TOKEN_WALLET(14, "划转至代币钱包"),

    /**
     * 共享钱包交易类型
     * 划转至账户钱包，来自账户钱包划转，开卡费，销卡手续费，纠正授权，验证，撤销，退款，销卡返回，服务费，退款撤销，纠正退款，纠正退款撤销
     */
    CORRECTIVE_AUTHORIZATION(19, "纠正授权"),
    VERIFY(20, "验证"),
    CANCEL(21, "撤销"),
    REFUND(22, "退款"),
    SERVICE_CHARGE(24, "服务费"),
    REFUND_CANCELLATION(25, "退款撤销"),
    CORRECTIVE_REFUND(26, "纠正退款"),
    CORRECTIVE_REFUND_CANCELLATION(27, "纠正退款撤销"),

    /**
     * 储值钱包交易类型:划转至账户钱包，来自账户钱包划转，开卡费，卡片充值，销卡手续费
     */
    TRANSFER_TO_WALLET_MAIN(15, "划转至账户钱包"),
    BE_FROM_TRANSFER_TO_WALLET_MAIN(16, "来自账户钱包划转"),

    WITHDRAW_FEE(17, "提现手续费"),


    // 31开卡费 32划转充值到卡片 33充值手续费 34销卡返回 35 销卡手续费
    OPEN_CARD_FEE(31, "开卡费"),
    RECHARGE_TO_CARD(32, "划转充值到卡片"),
    RECHARGE_FEE(33, "充值手续费"),
    CARD_CANCEL_RETURN(34, "销卡返回"),
    CARD_CANCEL_FEE(35, "销卡手续费"),
    CARD_WITHDRAW(36, "卡片提出"),
    ADVANCE_CARD_FEES(37, "垫付卡片费用"),

    //  41 开卡费收入 43充值手续费收入 45销卡费收入
    OPEN_CARD_FEE_INCOME(41, "开卡费收入"),
    RECHARGE_FEE_INCOME(43, "充值手续费收入"),
    CARD_CANCEL_FEE_INCOME(45, "销卡费收入"),
    REFUND_THE_ADVANCE_FEE(47, "返还垫付费用"),

    CONSUME(51, "消费减少"),
    CONSUME_RETURN(52, "消费退款"),

    // 60  --代付模块
    COMMISSION_FEE(62, "代付手续费"),
    COMMISSION(63, "代付金额"),
    PAYMENT_EARN(64, "代付收益"),

    BACKSTAGE_TOP_UP(91, "后台充值"),
    BACKSTAGE_DEDUCTION(92, "后台扣减"),
    WITHDRAW_TURN_DOWN(93, "提现驳回");


    private final Integer code;
    private final String desc;


    // 静态方法，根据code查询desc
    public static String getNameByCode(int code) {
        for (WalletTypeEnum e : WalletTypeEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }
        return null;
    }

    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private WalletTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
