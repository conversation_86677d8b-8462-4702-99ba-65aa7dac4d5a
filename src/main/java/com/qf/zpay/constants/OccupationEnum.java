package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 15:35
 */
@Getter
public enum OccupationEnum {

    /**
     * 卡片渠道
     */
    Finance("金融/银行", "Finance/Banking"),
    Medicine("医药/医疗", "Medicine/Medical"),
    IT("信息科技/互联网", "Information Technology/Internet"),
    corpuscle("电子", "electronic engineering"),
    Communications("通讯/电讯", "Communications/Telecommunications"),
    Media("媒体/影视/广告/艺术文化", "Media/film/Advertising/Art and culture"),
    Retail("零售/电商", "Retail/E-commerce"),
    Logistics("物流/运输/航运", "Logistics/Transportation/Shipping"),
    Tourism("旅游业/酒店", "Tourism/Hospitality"),
    CommercialService("商业服务", "Commercial service"),
    Wholesale("批发/进出口贸易", "Wholesale/import/export trade"),
    Dining("餐饮/休闲娱乐及康乐活动", "Dining/Leisure Entertainment and recreational activities"),
    OnlineGameIndustry("网上游戏业", "Online game industry");


    @EnumValue
    @JsonValue
    private final String textCh;
    private final String textEn;


    /**
     * 构造方法
     *
     * @param textCh 编码
     * @param textEn 描述
     */
    private OccupationEnum(String textCh, String textEn) {
        this.textCh = textCh;
        this.textEn = textEn;
    }

    public static String getNameByCode(String textCh) {
        for (OccupationEnum option : OccupationEnum.values()) {
            if (option.getTextCh().equals(textCh)) {
                return option.getTextEn();
            }
        }
        return null; // 如果没有找到对应的code，则返回null
    }
}



