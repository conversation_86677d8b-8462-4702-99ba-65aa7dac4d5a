package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

/**
 * <AUTHOR>
 * @updateTime 2024/6/25 10:01
 */
@Getter
public enum CardSchemeEnum implements EnumValid {

    /**
     * 卡组织
     */
    Master("Master", "万事达卡"),
    Visa("VISA", "VISA"),
    Discover("Discover", "发现卡"),
    UnionPay("UnionPay", "国际银联");


    /**
     * 编码
     * EnumValue注解标记数据库存储值
     */
    @EnumValue
    @JsonValue
    public final String code;
    public final String desc;

    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardSchemeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
