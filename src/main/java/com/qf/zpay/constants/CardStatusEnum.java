package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 15:35
 */
@Getter
public enum CardStatusEnum implements EnumValid {

    /**
     * 卡片状态
     */
    Active("Active", "活跃"),
    Blocked("Blocked", "锁定"),
    Cancel("Cancel", "注销"),
    Expired("Expired", "过期");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}



