package com.qf.zpay.constants;

import com.qf.zpay.constraints.EnumValid;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/6/25 11:56
 */
@Getter
public enum CardStatusActionEnum implements EnumValid {

    /**
     * 卡片操作
     */
    Freeze("freeze", "冻结", "false"),
    Unfreeze("unfreeze", "解冻", "true");


    private final String code;
    private final String desc;
    private final String enable;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardStatusActionEnum(String code, String desc, String enable) {
        this.code = code;
        this.desc = desc;
        this.enable = enable;
    }

    @Override
    public List<Object> validValues() {
        return Arrays.stream(CardStatusActionEnum.values())
                .map(CardStatusActionEnum::getCode)
                .collect(Collectors.toList());
    }
}
