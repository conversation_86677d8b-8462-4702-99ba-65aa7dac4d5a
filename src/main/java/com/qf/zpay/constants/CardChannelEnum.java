package com.qf.zpay.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 15:35
 */
@Getter
public enum CardChannelEnum {

    /**
     * 卡片渠道
     */
    <PERSON><PERSON>("skyee", "天易"),
    Photon("photon", "光子易"),
    Photon2("photon2", "光子易2"),
    ZPhysical("ZPhysical", "ZNET 手动实体卡"),
    GSalary("GSalary", "GSalary虚拟卡"),
    Asinx("asinx", "Asinx虚拟卡"),
    AsinxPhysical("asinxPhysical", "Asinx实体卡"),
    ZNet("ZNet", "测试渠道");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private CardChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}



