package com.qf.zpay.constants;

import lombok.Data;

@Data
public final class WalletTypeConstants {
    /**
     * 钱包类型
     * main_wallet 主账户钱包
     * store_wallet 储值卡钱包
     * share_wallet 共享卡钱包
     * physical_wallet 实体卡钱包
     * payment_wallet 代付钱包
     * usdt_wallet USDT 钱包
     * token_wallet代币钱包
     */
    public static final String STWALLET_TYPE_MAIN="main";
    public static final String STWALLET_TYPE_STORE="store";
    public static final String STWALLET_TYPE_SHARE="share";
    public static final String STWALLET_TYPE_PHYSICAL="physical";
    public static final String STWALLET_TYPE_PAYMENT="payment";
    public static final String STWALLET_TYPE_USDT="usdt";
    public static final String STWALLET_TYPE_TOKEN="token";

    /**
     * 钱包操作类型:  1 划转进 2 划转出 3 交易扣减
     */
    public static final Integer TYPE_OF_OPERATION_ROLL_INTO= 1;
    public static final Integer TYPE_OF_OPERATION_ROLL_OVER= 2;
    public static final Integer TYPE_OF_OPERATION_TRANSACTION_DEDUCTION= 3;

    /**
     * 流水号开头
     */
    public static final String SERIAL_NUMBER="Z-";
    public static final String SERIAL_PAYMENT="Z-P";
    public static final String SERIAL_PAYMENT_ORDER="Z-PO";

    /**
     * 1:提现待审核 2：提现审核通过 3：提现已驳回
     */
    public static final Integer WITHDRAWAL_PENDING_REVIEW = 1;
    public static final Integer The_withdrawal_was_approved = 2;
    public static final Integer THE_WITHDRAWAL_HAS_BEEN_REJECTED = 3;

    public static final Integer SUB_TYPE_WITHDRAW = 69;
    public static final Integer SUB_TYPE_WITHDRAWAL_COMMISSION = 1007;
    /**
     * 变动类型 1：增加 2：减少 0:金额不变动
     *
     */
    public static final Integer TYPE_DECREASE =2;

    private WalletTypeConstants() {}


}
