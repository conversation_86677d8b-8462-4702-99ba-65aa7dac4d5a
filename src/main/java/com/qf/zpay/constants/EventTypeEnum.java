package com.qf.zpay.constants;

import lombok.Getter;

@Getter
public enum EventTypeEnum {
    CARD_TRANSACTION("CARD_TRANSACTION", "卡交易"),
    CARD_STATUS_UPDATE("CARD_STATUS_UPDATE", "卡状态更新"),
    PAYEE_ACCOUNT_ACTIVE("PAYEE_ACCOUNT_ACTIVE", "收款人账户启用"),
    REMITTANCE_FAIL("REMITTANCE_FAIL", "付款订单失败"),
    REMITTANCE_COMPLETE("REMITTANCE_COMPLETE", "付款订单完成");

    private final String code;
    private final String description;

    EventTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}