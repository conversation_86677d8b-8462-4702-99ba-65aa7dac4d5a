package com.qf.zpay.job;

import generator.service.CardActionLogService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BusinessCardEarnJob extends QuartzJobBean {

    @Autowired
    private CardActionLogService cardActionLogService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("流水（开卡）定时任务开启");
        try {
            cardActionLogService.journalAccountStatistics();
            log.info("流水（开卡）定时任务结算");
        } catch (Exception e) {
            log.error("流水（开卡）获取数据异常", e);
        }
    }
}
