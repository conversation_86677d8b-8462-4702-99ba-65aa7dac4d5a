package com.qf.zpay.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.config.ImapConfig;
import com.qf.zpay.service.EmailService;
import com.qf.zpay.service.ImapIdleService;
import com.qf.zpay.util.EmailUtil;
import generator.domain.UserCard;
import generator.mapper.UserCardManageMapper;
import generator.mapper.UserCardMapper;
import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PhotonEmailForwardJob {
    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Value("${imap.zpaynetwork.host}")
    private String host;
    @Value("${imap.zpaynetwork.port}")
    private Integer port;
    @Value("${imap.zpaynetwork.user}")
    private String user;
    @Value("${imap.zpaynetwork.pass}")
    private String pass;


    @PostConstruct
    public void init() {
        if ("prod".equals(activeProfile)) {
            checkNow();
        }
    }

    public void checkNow() {
        ImapConfig config = new ImapConfig();
        config.setHost(host);
        config.setPort(port);
        config.setUser(user);
        config.setPass(pass);
        ImapIdleService imapIdleService = new ImapIdleService(config);
        imapIdleService.listening(msg -> {
            try {
                String subject = msg.getSubject();
                String from = msg.getFrom()[0].toString();
                log.info("邮件主题: {},{}", subject, from);
                String[] userEmail = EmailUtil.parseFrom(from);
                log.info("邮件发送人: user: {} email: {}", userEmail[0], userEmail[1]);
                if (!"Photonpay".equals(userEmail[0])) {
                    return;
                }

                switch (subject) {
                    case "钱包绑卡验证通知":
                        String content = EmailUtil.getTextContent(msg);
                        forwardCode(content);
                        break;
                    default:
                        break;
                }
            } catch (MessagingException e) {
                log.error("获取邮件主题失败", e);
            }
        });
    }

    @Autowired
    UserCardMapper userCardMapper;
    @Autowired
    UserCardManageMapper userCardManageMapper;
    @Autowired
    private EmailService emailService;

    private void forwardCode(String content) {
        try {
            String verificationCode = null;
            String cardLastFour = null;

            Pattern codePattern = Pattern.compile("(?<=验证码是|验证码：)\\s*(\\d{6})");
            Pattern cardPattern = Pattern.compile("(?<=尾号为)\\s*(\\d{4})");
            String[] lines = content.split("\n");
            for (String line : lines) {
                // 提取验证码
                Matcher codeMatcher = codePattern.matcher(line);
                if (codeMatcher.find()) {
                    verificationCode = codeMatcher.group(1);
                }
                // 提取卡尾号
                Matcher cardMatcher = cardPattern.matcher(line);
                if (cardMatcher.find()) {
                    cardLastFour = cardMatcher.group(1);
                }
            }

            log.info("尾号：{} 验证码：{}", cardLastFour, verificationCode);
            cardLastFour = "3460";
            List<String> productCodeList = Arrays.asList("54502000", "558325");
//        List<Integer> ids = userCardManageMapper.getIdsByProductCodeList(productCodeList);
            UserCard card = userCardMapper.selectOne(new LambdaQueryWrapper<UserCard>()
//                .in(UserCard::getCardManageId, ids)
                            .in(UserCard::getProductCode, productCodeList)
                            .likeLeft(UserCard::getCardNumber, cardLastFour)
            );
            if (card == null) {
                log.error("未找到对应卡片");
                return;
            }
            if (card.getContact() == null) {
                log.error("未找到卡片联系信息");
                return;
            }
            String text = String.format("Your verification code is <span style=\"color: rgb(32,186,214); font-weight: bold;\">%s</span>. ", verificationCode);
            text += "Please complete the verification within 8 minutes. If you did not make this request, please disregard this message. <br/><br/>ZNetwork";

            emailService.sendSimpleMessage(card.getContact(), "绑卡验证通知", text);
            log.info("转发邮件成功");
        } catch (Exception e) {
            log.error("转发邮件失败", e);
        }
    }
}
