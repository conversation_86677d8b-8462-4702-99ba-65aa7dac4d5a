package com.qf.zpay.job;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.qf.zpay.service.ImapIdleService;
import com.qf.zpay.util.EmailUtil;
import generator.domain.CardApplyOrder;
import generator.domain.CardHolder;
import generator.domain.UserCard;
import generator.service.CardApplyOrderService;
import generator.service.CardHolderService;
import generator.service.UserCardService;
import jakarta.annotation.PostConstruct;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @updateTime 2024/10/28 16:02
 */
@Slf4j
@Component
public class GlobalCashEmailCheckJob {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Autowired
    ImapIdleService imapIdleService;

    @Value("${file.upload-dir}")
    private String uploadDir;

    @Autowired
    RedissonClient redissonClient;

    @PostConstruct
    public void init() {
        if ("prod".equals(activeProfile)) {
            checkNow();
        }
    }


    public void checkNow() {
        imapIdleService.listening(msg -> {
            try {
                String subject = msg.getSubject();
                String from = msg.getFrom()[0].toString();
                log.info("邮件主题: {}", subject);
                String[] userEmail = EmailUtil.parseFrom(from);
                log.info("邮件发送人: user: {} email: {}", userEmail[0], userEmail[1]);
                if (!"Global Cash 全球付".equals(userEmail[0])) {
                    return;
                }

                switch (subject) {
                    case "登录全球付商户服务平台":
                        String content = EmailUtil.getTextContent(msg);
//                        log.info("邮件内容: {}", content);
                        codeHandle(content);
                        break;
                    case "实名同步结果/Real name sync results":
                        // 暂不处理
                        break;
                    case "实名审核结果/Real name audit results":
                        // 识别审核结果
                        auditHandle(msg);
                        break;
                    default:
                        break;
                }
            } catch (MessagingException e) {
                log.error("获取邮件主题失败", e);
            }
        });
    }

    private void codeHandle(String content) {
        String regex = "验证码：(\\d{6})";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            String code = matcher.group(1);
            log.info("GlobalCash 验证码: {}", code);
            RBucket<String> codeCache = redissonClient.getBucket("qxf_code");
            codeCache.set(code);
            codeCache.expire(Duration.ofMinutes(5));
        }
    }


    @Autowired
    UserCardService userCardService;
    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    CardApplyOrderService cardApplyOrderService;

    private void auditHandle(Message msg) {
        try {
            log.info("开始处理审核结果");
            Path path = Paths.get(uploadDir, "audit");
            List<Path> list = EmailUtil.parseAndSaveAttachments(msg, path);
            ExcelReader reader = ExcelUtil.getReader(list.getFirst().toFile());
            List<Map<String, Object>> readAll = reader.readAll();
            for (Map<String, Object> map : readAll) {
                log.info("{}", map);
                String cardNo = map.get("卡号").toString();
                String auditResult = map.get("结果").toString();
                log.info("卡号: {} 审核结果: {}", cardNo, auditResult);

                String cardBin = cardNo.substring(0, 4);
                String cardEnd = cardNo.substring(cardNo.length() - 4);
                UserCard userCard = userCardService.getByCardNumberLike(cardBin, cardEnd);
                if (userCard == null) {
                    log.error("未找到卡片，卡号: {}", cardNo);
                    continue;
                }
                CardHolder cardHolder = cardHolderService.getById(userCard.getHolderId());
                CardApplyOrder cardApplyOrder = cardApplyOrderService.getOpenCardOneByCardNumber(userCard.getCardNumber());

                if ("审核通过".equals(auditResult)) {
                    cardHolder.setStatus(1);
                    userCard.setStatus(1);
                    cardApplyOrder.setStatus(3);
                    cardApplyOrder.setNote("");
                } else {
                    // 审核不通过
                    cardHolder.setStatus(2);
                    userCard.setStatus(0);
                    cardApplyOrder.setStatus(4);
                    cardApplyOrder.setNote(map.get("原因").toString());
                }
                cardHolderService.updateById(cardHolder);
                userCardService.updateById(userCard);
                cardApplyOrderService.updateById(cardApplyOrder);
            }
        } catch (Exception e) {
            log.error("更新卡审核状态失败", e);
        }
    }
}
