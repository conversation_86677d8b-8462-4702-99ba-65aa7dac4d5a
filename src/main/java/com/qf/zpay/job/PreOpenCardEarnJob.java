package com.qf.zpay.job;

import com.qf.zpay.service.CardService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PreOpenCardEarnJob extends QuartzJobBean {

    @Autowired
    private CardService cardService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("预开卡定时任务开启");
        try {
            cardService.preOpenCard();
            log.info("预开卡定时任务结算");
        } catch (Exception e) {
            log.error("预开卡获取数据异常", e);
        }
    }
}
