package com.qf.zpay.job;

import com.qf.zpay.service.CardService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 月费收取JOB
 */
@Slf4j
@Component
public class MonthFee<PERSON><PERSON> extends QuartzJobBean {

    @Autowired
    private CardService cardService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("【月费收取】定时任务开启 时间 ：{}", new Date());
        try {
            cardService.doCardMonthFee();
            log.info("【月费收取】定时任务结算");
        } catch (Exception e) {
            log.error("【月费收取】获取数据异常", e);
        }
    }
}
