package com.qf.zpay.job;

import com.qf.zpay.service.FTPService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GlobalCashCheckKycByFtpJob extends QuartzJobBean {

    @Autowired
    private FTPService ftpService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("全球付 FTP 检查状态任务开启");
        try {
            ftpService.modifyState();
            log.info("全球付检查 KYC 定时任务开启");
        } catch (Exception e) {
            log.error("全球付 ", e);
        }
    }
}
