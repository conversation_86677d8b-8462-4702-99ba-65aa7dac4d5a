package com.qf.zpay.job;

import com.qf.zpay.service.GlobalcashService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GlobalCashCardLogJ<PERSON> extends QuartzJobBean {

    @Autowired
    private GlobalcashService globalcashService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("全球付采集卡片日志定时任务开启");
        globalcashService.UserCardLog();
    }
}
