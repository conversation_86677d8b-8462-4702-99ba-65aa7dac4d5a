package com.qf.zpay.job;

import com.qf.zpay.util.SpringContextUtil;
import generator.service.PaymentPayOrderService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BusinessPaymentJob extends QuartzJobBean {

    @Autowired
    PaymentPayOrderService paymentPayOrderService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("流水（代付）定时任务开启");
        PaymentPayOrderService bean = SpringContextUtil.getBean(PaymentPayOrderService.class);
        try {
            paymentPayOrderService.paymentPayOrderStatistics();
            log.info("流水（代付）定时任务结算");
        } catch (Exception e) {
            log.error("流水（代付）获取数据异常", e);
        }
    }
}
