package com.qf.zpay.job;

import com.qf.zpay.service.GmailAttachmentDownloader;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GlobalCashCheckKycByEmailJob extends QuartzJobBean {

    @Autowired
    GmailAttachmentDownloader gmailAttachmentDownloader;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("全球付检查 KYC 定时任务开启");
        try {
            gmailAttachmentDownloader.checkForNewMessages();
            log.info("全球付检查 KYC 成功");
        } catch (Exception e) {
            log.error("全球付检查 KYC 异常", e);
        }
    }
}
