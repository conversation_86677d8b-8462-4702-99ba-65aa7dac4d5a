package com.qf.zpay.job;

import com.qf.zpay.service.DelayQueueService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class Thaw<PERSON><PERSON><PERSON><PERSON> extends QuartzJobBean {

    @Autowired
    private DelayQueueService delayQueueService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        try {
            delayQueueService.pollAndProcessDelayedMessages();
        } catch (Exception e) {
            log.error("解冻后操作数据异常", e);
        }
    }
}
