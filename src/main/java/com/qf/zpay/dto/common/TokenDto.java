package com.qf.zpay.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @updateTime 2024/6/13 10:48
 */
@Data
public class TokenDto {

    @NotNull
    @NotBlank
    @JsonProperty("AccessToken")
    private BaseTokenDto accessToken;


    @NotNull
    @NotBlank
    @Size(min = 32, max = 32)
    @JsonProperty("RefreshToken")
    private BaseTokenDto refreshToken;
}
