package com.qf.zpay.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 10:26
 */
@Data
public class CardProductDto {

    /**
     * 卡片产品ID
     */
    private Integer cardManageId;


    private CardSchemeEnum cardScheme;

    /**
     * 商户ID
     */
    private Integer businessId;

    /**
     * 卡片产品上游唯一编号
     */
    private String productCode;

    /**
     * 渠道名称
     */
    private CardChannelEnum channel;

    /**
     * 卡片产品模式
     */
    private CardModelEnum cardModel;


    /**
     * 卡片产品状态 false 关闭，不能开卡
     */
    private Boolean cardStatus;

    private BigDecimal costOpenCardFee;

    private BigDecimal costChargeRate;

    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台充值手续费率%
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;


    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;


    /**
     * 卡片首充金额
     */
    private BigDecimal firstRecharge;


    /**
     * 卡片是否可用 商户端控制启、禁用
     */
    private Boolean isAble;

    /**
     * 可开卡数
     */
    private Integer totalOpenCardNum;


}
