package com.qf.zpay.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/6/13 17:17
 */
@Data
public class BaseTokenDto {

    @NotNull
    @NotBlank
    private String value;


    @NotNull
    private Long expire;
}
