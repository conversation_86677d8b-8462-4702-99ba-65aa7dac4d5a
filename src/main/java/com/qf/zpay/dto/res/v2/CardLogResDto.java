package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.constants.CardModelEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/7/24 10:22
 */
@Data
public class CardLogResDto {
    /**
     * 消息通知类型【cardPay:卡消费通知、acctColl:收款入账通知、acctExchange:换汇结果通知、acctPay:付款相关通知、acctAuth:报备信息审核结果通知】另外自己加上  开卡费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  实际充值到账realCardRecharge  销卡结算金额cardCancel
     */
    private String noticeType;

    /**
     * 交易id
     */
    private String orderId;

    /**
     * 币种
     */
    @JsonProperty("transCurrency")
    private String transAmountCurrency;

    /**
     * 交易金额
     */
    private BigDecimal transAmount;

    /**
     * 预授权币种
     */
    @JsonProperty("authCurrency")
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    private BigDecimal authAmount;

    /**
     * 卡ID
     */
    private String cardId;


    /**
     * 脱敏卡号
     */
    private String maskCardNumber;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;


    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户国家代码
     */
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 商户所在州或区
     */
    private String merchantState;

    /**
     * 商户邮编
     */
    private String merchantZipCode;

    /**
     * 商户描述
     */
    private String merchantDesc;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    private String status;

    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    private String fundsDirection;


    private String transactionType;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 可用额度
     */
    private BigDecimal availableCredit;

    private Date createTime;
}
