package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.service.pojo.CardInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 22:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OpenCardResDto extends CardInfoDto {

    /**
     * 开卡费
     */
//    public BigDecimal platformOpenCardFee;

    @JsonProperty("openCardFee")
    public BigDecimal businessOpenCardFee;

    /**
     * 本次充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 本次充值手续费
     */
//    public BigDecimal platformRechargeFee;
            
    @JsonProperty("rechargeFee")
    public BigDecimal businessRechargeFee;

}
