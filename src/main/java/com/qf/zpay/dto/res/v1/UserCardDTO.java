package com.qf.zpay.dto.res.v1;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 查询卡片信息DTO
 */
@Data
public class UserCardDTO {

    @JsonProperty("cardId")
    private String cardId; // 卡ID

    /**
     * 可用金额
     */
    @JsonProperty("amount")
    private BigDecimal amount; // 卡金额

    /**
     * 卡模式 Standard:标准卡,ShareBalance:共享卡
     */
    @JsonProperty("cardModel")
    private String cardModel; // 卡模式

    @JsonProperty("cardCurrency")
    private String cardCurrency; // 卡币种

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    @JsonProperty("cardAmt")
    private String cardAmt; // 卡金额

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    @JsonProperty("cardTotalAmt")
    private String cardTotalAmt; // 卡总金额

    @JsonProperty("cardExpirationMMYY")
    private String cardExpirationMmyy; // 卡有效期

    @JsonProperty("cardCVV")
    private String cardCvv; // 卡CVV

    @JsonProperty("cardNumber")
    private String cardNumber; // 卡号

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    @JsonProperty("cardStatus")
    private String cardStatus; // 卡状态

    @JsonProperty("productCode")
    private String productCode; // 产品代码

    /**
     * 卡片申请时间
     */
    @JsonProperty("applyTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date applyTime;

    @JsonProperty("card_manage_id")
    private Integer cardManageId; // 卡管理ID

    @JsonProperty("card_lei")
    private String cardLei; // 卡类型

    @JsonProperty("card_name")
    private String cardName; // 卡名称

    @JsonProperty("card_belong_to")
    private String cardBelongTo; // 卡所属

    /**
     * 应用场景
     */
    @JsonProperty("card_changjing")
    private String cardChangjing;

    /**
     * 发卡国家
     */
    @JsonProperty("card_source_country")
    private String cardSourceCountry; // 卡来源国家

    @JsonProperty("card_duan")
    private String cardDuan; // 卡段

    /**
     * 卡月费
     */
    @JsonProperty("card_month_fee")
    private BigDecimal cardMonthFee; // 卡月费

    /**
     * 卡单次消费上限（USD）
     */
    @JsonProperty("card_danci_xiaofei_max")
    private BigDecimal cardDanciXiaofeiMax; // 卡单次消费上限

    /**
     * 卡月消费上限（USD）
     */
    @JsonProperty("card_month_xiaofei_max")
    private BigDecimal cardMonthXiaofeiMax; // 卡每月消费上限

    /**
     * 卡储值上限（USD）
     */
    @JsonProperty("card_chuzhi_max")
    private BigDecimal cardChuzhiMax; // 卡充值上限

    /**
     * 商户开卡费
     */
    @JsonProperty("business_open_card_fee")
    private BigDecimal businessOpenCardFee; // 商户开启卡费用

    /**
     * 商户手续费百分比（%）
     */
    @JsonProperty("business_charge_rate")
    private BigDecimal businessChargeRate;

    /**
     * 平台销卡费
     */
    @JsonProperty("platform_cancel_card_fee")
    private BigDecimal platformCancelCardFee; // 平台取消卡费用

    /**
     * 实际开卡的时间
     */
    @JsonProperty("openCardTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;

    /**
     * 注销卡片的时间
     */
    @JsonProperty("cancelCardTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;

}
