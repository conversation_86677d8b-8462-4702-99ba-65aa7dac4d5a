package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @updateTime 2024/7/10 11:27
 */
@Data
public class CardManageResDto {

//    private String cardName;

    /**
     * 卡组织
     */
    @JsonProperty("cardScheme")
    private CardSchemeEnum cardBelongTo;

    /**
     * 应用场景
     */
    @JsonProperty("cardScene")
    private List<String> cardChangjing;

    /**
     * 发卡国家
     */
    @JsonProperty("cardCountry")
    private String cardSourceCountry;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    @JsonProperty("cardSingleLimit")
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    @JsonProperty("cardMonthLimit")
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    @JsonProperty("cardTotalLimit")
    private BigDecimal cardChuzhiMax;

    /**
     * 卡背图
     */
    private String cardImg;

    /**
     *
     */
    private CardModelEnum cardModel;


    /**
     * 商户id
     */
//    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    /**
     * 卡段
     */
    private String cardBin;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Boolean isAble;

    /**
     * 开卡数
     */
    @JsonProperty("totalOpenCardNum")
    private Integer numberOfCardsThatCanBeOpened;


    /**
     * 已开卡数
     */
    private Integer alreadyOpenedCardNum;


    /**
     * 平台开卡费
     */
//    @JsonProperty("openCardFee")
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    @JsonProperty("platformRechargeFeeRate")
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
//    @JsonProperty("cancelCardFee")
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    @JsonProperty("businessRechargeFeeRate")
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;


    private BigDecimal firstRecharge;

    /**
     * 是否需要kyc
     */
    private Boolean needKyc = false;

    /**
     * 是否可以设置支付密码pin
     */
    private Boolean canSetPwd = false;


    /**
     * 是否需要设置联系人
     */
    private Boolean needSetContact = false;


}
