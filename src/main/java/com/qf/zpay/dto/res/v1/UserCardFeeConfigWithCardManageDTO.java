package com.qf.zpay.dto.res.v1;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserCardFeeConfigWithCardManageDTO {
    // 商户ID
    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    // 卡段
    private String cardDuan;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;

    /**
     * 成本开卡费
     */
    private BigDecimal costOpenCardFee;

    /**
     * 成本充值手续费%
     */
    private BigDecimal costChargeRate;

    /**
     * 成本销卡费
     */
    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;

    /**
     * 卡片来源地
     */
    private String cardSource;

    /**
     * 卡片类别
     */
    private Integer cardCategory;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡归属
     */
    private String cardBelongTo;

    /**
     * 开卡费（成本）
     */
    private BigDecimal cardOpenFee;

    /**
     * 划转手续费%（成本）
     */
    private BigDecimal cardChargeRate;

    /**
     * 销卡费（成本）
     */
    private BigDecimal cardCancelFee;

    /**
     * 卡类
     */
    private String cardLei;

    /**
     * 应用场景
     */
    private String cardChangjing;

    /**
     * 发卡国家
     */
    private String cardSourceCountry;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    private BigDecimal cardChuzhiMax;

    /**
     * 卡背图
     */
    private String cardImg;

    /**
     * 卡模式
     */
    private Object cardModel;

    /**
     * 卡状态
     */
    private Integer cardStatus;

}
