package com.qf.zpay.dto.res.v1;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 获取卡片列表DTO
 */
@Data
public class UserCardListDTO {

    @JsonProperty("cardId")
    private String cardId; // 卡ID

    @JsonProperty("cardNumber")
    private String cardNumber; // 卡号

    @JsonProperty("cardExpirationMMYY")
    private String cardExpirationMmyy; // 卡有效期

    @JsonProperty("cardCVV")
    private String cardCvv; // 卡CVV

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    @JsonProperty("cardStatus")
    private String cardStatus; // 卡状态

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    @JsonProperty("cardAmt")
    private String cardAmt; // 卡金额

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    @JsonProperty("cardTotalAmt")
    private String cardTotalAmt; // 卡总金额


    /**
     * 实际开卡的时间
     */
    @JsonProperty("openCardTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;

    /**
     * 注销卡片的时间
     */
    @JsonProperty("cancelCardTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;

}
