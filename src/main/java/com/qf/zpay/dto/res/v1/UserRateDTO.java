package com.qf.zpay.dto.res.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 获取商户的费率DTO
 */
@Data
public class UserRateDTO {

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    @JsonProperty("card_manage_id")
    private Integer cardManageId;

    /**
     * 卡段
     */
    @JsonProperty("card_duan")
    private String cardDuan;

    /**
     * 商户开卡费用
     */
    @JsonProperty("business_open_card_fee")
    private BigDecimal businessOpenCardFee;

    /**
     * 商户划转费率
     */
    @JsonProperty("business_charge_rate")
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费用
     */
    @JsonProperty("platform_cancel_card_fee")
    private BigDecimal platformCancelCardFee;

    /**
     * 卡片服务费
     */
    @JsonProperty("card_month_fee")
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限
     */
    @JsonProperty("card_danci_xiaofei_max")
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡每月消费上限
     */
    @JsonProperty("card_month_xiaofei_max")
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限
     */
    @JsonProperty("card_chuzhi_max")
    private BigDecimal cardChuzhiMax;
}
