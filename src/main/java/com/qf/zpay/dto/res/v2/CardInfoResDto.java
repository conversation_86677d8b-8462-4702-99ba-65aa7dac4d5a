package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.constants.CardStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/7/9 16:41
 */
@Data
public class CardInfoResDto {

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     * 账面金额
     */
    private BigDecimal virtualAmt;

    /**
     * 卡模式 CardModelEnum
     */
    private CardModelEnum cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额
     */
//    @JsonProperty("cardSingleLimit")
//    private BigDecimal cardAmt;

    /**
     * 总限额
     */
//    @JsonProperty("cardTotalLimit")
//    private BigDecimal cardTotalAmt;

    /**
     * 有效期
     */
    @JsonProperty("cardExpiration")
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡状态 CardStatusEnum
     */
    private CardStatusEnum cardStatus;

    /**
     * 实际开卡的时间
     */
    private Date openCardTime;

    /**
     * 注销卡片的时间
     */
    private Date cancelCardTime;


    private CardSchemeEnum cardScheme;

}
