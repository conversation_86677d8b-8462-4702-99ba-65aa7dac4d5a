package com.qf.zpay.dto.res.v2;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Data
public class PaymentVo {

    private String orderId;

    /**
     * 交易状态
     */
    private String status;

    private String paymentMethod;

    private String  paymentAccount;
    /**
     * 到账金额
     */
    private Map<String,Object> amountReceived;
    /**
     * 付款金额
     */
    private  Map<String,Object> paymentAmount;
    /**
     * 手续费
     */
    private  Map<String,Object> fee;
    /**
     * 付款费率
     */
    private BigDecimal paymentRate;
    /**
     * 创建时间
     */
    private Date createTime;

    private String remark;

    private Date paymentTime;
}
