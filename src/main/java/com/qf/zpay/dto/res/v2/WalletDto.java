package com.qf.zpay.dto.res.v2;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 15:26
 */
@Data
public class WalletDto {

    /**
     * 主账户钱包
     */
    private BigDecimal mainWallet;

    /**
     * 储值卡钱包
     */
    private BigDecimal storeWallet;

    /**
     * 共享卡钱包
     */
    private BigDecimal shareWallet;

    /**
     * 实体卡钱包
     */
    private BigDecimal physicalWallet;

    /**
     * 代付钱包
     */
    private BigDecimal paymentWallet;

    /**
     * USDT 钱包
     */
    private BigDecimal usdtWallet;

    /**
     * 代币钱包
     */
    private BigDecimal tokenWallet;

}
