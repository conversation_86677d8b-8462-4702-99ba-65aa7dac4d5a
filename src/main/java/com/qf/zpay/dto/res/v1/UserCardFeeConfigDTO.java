package com.qf.zpay.dto.res.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取可用的卡产品列表DTO
 */
@Data
public class UserCardFeeConfigDTO {

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    @JsonProperty("card_manage_id")
    private Integer cardManageId;

    /**
     * 卡片类别
     */
    @JsonProperty("card_category")
    private Integer cardCategory;

    /**
     * 卡片产品名称
     */
    @JsonProperty("card_name")
    private String cardName;

    /**
     * 卡段
     */
    @JsonProperty("card_duan")
    private String cardDuan;

    /**
     * 卡片所属组织
     */
    @JsonProperty("card_belong_to")
    private String cardBelongTo;

    /**
     * 卡片类型（实体卡，虚拟卡）
     */
    @JsonProperty("card_lei")
    private String cardLei;

    /**
     * 卡片应用场景
     */
    @JsonProperty("card_changjing")
    private List<String> cardChangjing = new ArrayList<>();

    /**
     * 商户开卡费用
     */
    @JsonProperty("business_open_card_fee")
    private BigDecimal businessOpenCardFee;

    /**
     * 商户充值费率
     */
    @JsonProperty("business_charge_rate")
    private BigDecimal businessChargeRate;

    /**
     * 平台取消卡费用
     */
    @JsonProperty("platform_cancel_card_fee")
    private BigDecimal platformCancelCardFee;

    /**
     * 产品编码
     */
    @JsonProperty("productCode")
    private String productCode;

    /**
     * 卡片模式
     */
    @JsonProperty("cardModel")
    private String cardModel;

    /**
     * 卡片结算币种
     */
    @JsonProperty("cardCurrency")
    private String cardCurrency;

    /**
     * 强制充值
     */
    @JsonProperty("force_recharge")
    private String forceRecharge;
}
