package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 22:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelCardResDto extends ChangeStatusResDto {

    /**
     * 销卡费
     */
//    public BigDecimal platformCancelCardFee;

    @JsonProperty("cancelCardFee")
    public BigDecimal businessCancelCardFee;
}
