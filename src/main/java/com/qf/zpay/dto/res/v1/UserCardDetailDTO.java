package com.qf.zpay.dto.res.v1;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 查询流水明细DTO
 */
@Data
public class UserCardDetailDTO {

    /**
     * 订单id
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 预授权币种
     */
    @JsonProperty("authAmountCurrency")
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    @JsonProperty("authAmount")
    private BigDecimal authAmount;

    /**
     * 交易币种
     */
    @JsonProperty("transAmountCurrency")
    private String transAmountCurrency;

    /**
     * 交易金额
     */
    @JsonProperty("transAmount")
    private BigDecimal transAmount;

    /**
     * 商户名称
     */
    @JsonProperty("merchantName")
    private String merchantName;

    /**
     * 商户国家代码
     */
    @JsonProperty("merchantCountryCode")
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    @JsonProperty("merchantCity")
    private String merchantCity;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
