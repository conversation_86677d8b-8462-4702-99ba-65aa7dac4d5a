package com.qf.zpay.dto.res.v2;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/7/9 16:41
 */
@Data
public class ExpendDto {

    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 开卡费支出
     */
    private BigDecimal openCardFeeExpenditure;
    /**
     * 手续费支出
     */
    private BigDecimal rechargeChargeCardExpenditure;
    /**
     * 卡注销支出
     */
    private BigDecimal cardCancellationExpenditure;
    /**
     * 总支出
     */
    private BigDecimal chargeTotalExpenditure;
}
