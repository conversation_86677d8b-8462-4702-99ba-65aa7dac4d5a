package com.qf.zpay.dto.res.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 17:06
 */
@Data
public class CardRechargeResDto {

    private String cardId;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     * 账面金额
     */
    private BigDecimal virtualAmt;

    /**
     * 本次充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 本次充值手续费
     */
//    public BigDecimal platformRechargeFee;

    @JsonProperty("rechargeFee")
    public BigDecimal businessRechargeFee;


}
