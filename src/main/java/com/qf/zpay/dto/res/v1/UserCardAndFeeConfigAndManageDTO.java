package com.qf.zpay.dto.res.v1;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserCardAndFeeConfigAndManageDTO {


    /**
     * 商户id
     */
    private Integer uid;

    /**
     * 商户旗下的用户id
     */
    private Integer uidChild;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     *
     */
    private BigDecimal virtualAmt;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 卡模式 Standard:标准卡,ShareBalance:共享卡
     */
    private String cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    private String cardAmt;

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    private String cardTotalAmt;

    /**
     * 订单任务编号
     */
    private String orderNo;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 有效期
     */
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    private String cardStatus;

    /**
     * 卡片申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 实际开卡的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;


    /**
     * 注销卡片的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;


    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    /**
     * 卡段
     */
    private String cardDuan;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;

    /**
     * 成本开卡费
     */
    private BigDecimal costOpenCardFee;

    /**
     * 成本充值手续费%
     */
    private BigDecimal costChargeRate;

    /**
     * 成本销卡费
     */
    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;

    /**
     * 卡段创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String updateBy;

    /**
     * 卡片来源地
     */
    private String cardSource;

    /**
     * 卡片类别
     */
    private Integer cardCategory;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡归属
     */
    private String cardBelongTo;

    /**
     * 开卡费（成本）
     */
    private BigDecimal cardOpenFee;

    /**
     * 划转手续费%（成本）
     */
    private BigDecimal cardChargeRate;

    /**
     * 销卡费（成本）
     */
    private BigDecimal cardCancelFee;

    /**
     * 卡类
     */
    private String cardLei;

    /**
     * 应用场景
     */
    private String cardChangjing;

    /**
     * 发卡国家
     */
    private String cardSourceCountry;


    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    private BigDecimal cardChuzhiMax;

    /**
     *
     */
    private String cardManageCreateBy;

    /**
     *
     */
    private Date cardManageCreateTime;

    /**
     *
     */
    private String cardManageUpdateBy;

    /**
     *
     */
    private Date cardManageUpdateTime;

    /**
     * 卡背图
     */
    private String cardImg;


    /**
     * 卡管理状态
     */
    private Integer cardManageStatus;
}
