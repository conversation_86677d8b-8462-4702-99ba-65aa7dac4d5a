package com.qf.zpay.dto.res.v1;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询卡片流水DTO
 */
@Data
public class UserCardLogDTO {

    /**
     * 表userCardLogId
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 订单id
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * cardId
     */
    @JsonProperty("card_id")
    private String cardId;

    /**
     *
     */
    @JsonProperty("maskCardNumber")
    private String maskCardNumber;

    /**
     * 日志创建时间
     */
    @JsonProperty("create_time")
    private Date createTime;

    /**
     *
     */
    @JsonProperty("authAmountCurrency")
    private String authAmountCurrency;

    /**
     *
     */
    @JsonProperty("authAmount")
    private BigDecimal authAmount;

    /**
     *
     */
    @JsonProperty("fundsDirection")
    private String fundsDirection;

    /**
     *
     */
    @JsonProperty("transactionType")
    private String transactionType;

    /**
     * 商户名称
     */
    @JsonProperty("merchantName")
    private String merchantName;

    /**
     * 商户国家代码
     */
    @JsonProperty("merchantCountryCode")
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    @JsonProperty("merchantCity")
    private String merchantCity;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    @JsonProperty("status")
    private String status;

    /**
     * 失败原因
     */
    @JsonProperty("failureReason")
    private String failureReason;
}
