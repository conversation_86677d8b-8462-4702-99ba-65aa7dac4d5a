package com.qf.zpay.dto.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class UserPasswordDto {
    /**
     * 密码
     */
    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
    private String password;
    /**
     * 确认密码
     */
    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
    private String verifyPassword;
    /**
     * 验证码
     */
    @Length(min = 6, max = 6, message = "{Valid.code}")
    private String code;
}
