package com.qf.zpay.dto.req;

import com.qf.zpay.constants.PayeeEnum;
import com.qf.zpay.constraints.InEnum;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class PaymentPayeeDto {

    /**
     * 商户id
     */
    private Integer businessId;

    private Integer id;
    /**
     * 配置id
     */
    private Integer payConfigId;

    private String payeeId;

    /**
     * 收款人类型（INDIVIDUAL 个人 / ENTERPRISE 企业）
     */
    @NotBlank(message = "{Valid.subjectType}")
    @InEnum(value = PayeeEnum.class)
    private String subjectType;

    /**
     * 国家/地区编码
     */
    @NotBlank(message = "{Valid.country}")
    private String country;

    /**
     * 收款币种
     */
    @NotBlank(message = "{Valid.currency}")
    private String currency;

    /**
     * 收款人名（个人类型必填）
     */
    @NotBlank(message = "{Valid.firstName}")
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String firstName;

    /**
     * 收款人姓（个人类型必填）
     */
    @NotBlank(message = "{Valid.lastName}")
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String lastName;

    /**
     * 公司名称（企业类型必填）
     */
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String accountHolder;

    /**
     * 区号
     */
    @NotBlank(message = "{Valid.nationCode}")
    private String nationCode;
    /**
     * 手机号
     */
    @Digits(integer = 20, fraction = 0, message = "{Valid.phone2}")
    @NotBlank(message = "{Valid.phone}")
    private String phone;

    /**
     * 地址
     */
    private String address;

    private String englishNameCountry;

    /**
     * 国家code
     */
    private String twoLetterCode;
    private String accountType;


    /**
     * 付款用途
     */
//    @NotBlank(message = "The purpose of payment cannot be empty")
    private String paymentPurpose;

}
