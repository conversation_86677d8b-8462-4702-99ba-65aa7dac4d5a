package com.qf.zpay.dto.req;

import lombok.Data;

import java.util.Date;

@Data
public class PayOrderDto {

    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 流水号
     */
    private String orderId;
    /**
     * 收款人
     */
    private String firstName;
    private String lastName;
    /**
     * 地区
     */
    private String country;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 付款用途
     */
    private String paymentPurpose;
    /**
     * 开始
     */
    private String dateRange;
    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 当前页数
     */
    private Integer page;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

}
