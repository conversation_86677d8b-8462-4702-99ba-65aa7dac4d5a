package com.qf.zpay.dto.req.v2;

import com.qf.zpay.constants.PaymentAmountTypeEnum;
import com.qf.zpay.constraints.InEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LockTheCurrencyDto {
    /**
     * 收款人账户
     */
    @NotBlank(message = "{Valid.payeeId}")
    private String payeeId;

    /**
     * 付款币种
     */
    @NotBlank(message = "{Valid.payCurrency}")
    private String payCurrency;
    /**
     * 付款金额
     */
    @NotNull(message = "{Valid.amount}")
    @Min(value = 1, message = "{Valid.amount_min}")
    private BigDecimal amount;
    /**
     * 到提交的金额是收款人收到的金额，收款金额的币种在创建付款人时已指定
     */
    @InEnum(value = PaymentAmountTypeEnum.class)
    private String amountType;
    /**
     * 备注
     */
    private String remark;

}
