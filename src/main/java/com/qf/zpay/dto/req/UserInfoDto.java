package com.qf.zpay.dto.req;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UserInfoDto {
    private Integer businessId;
    private String businessName;
    private String email;
    private String appId;
    private String appSecret;
    private JSONArray enableModule;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastLoginTime;
    /**
     * 密钥
     */
    private String secretKey;
}
