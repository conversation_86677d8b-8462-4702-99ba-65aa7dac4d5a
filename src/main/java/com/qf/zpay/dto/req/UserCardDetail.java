package com.qf.zpay.dto.req;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.constants.CardStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_user_card
 */
@TableName(value = "tb_user_card")
@Data
public class UserCardDetail implements Serializable {

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     *
     */
    private BigDecimal virtualAmt;

    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 卡昵称
     */
    private String cardNickname;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    private BigDecimal cardAmt;

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    private BigDecimal cardTotalAmt;

    /**
     * 订单任务编号
     */
    private String orderNo;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 有效期
     */
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    @ExcelProperty("cardNumber")
    private String cardNumber;

    /**
     * 卡状态
     */
    private CardStatusEnum cardStatus;


    /**
     * 卡片申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 卡片申请处理状态 0
     */
    private Integer status;

    /**
     * 实际开卡的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;

    /**
     * 实际开卡的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardDate;

    /**
     * 注销卡片的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;

    /**
     * 注销卡片的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardDate;


    /**
     *
     */
    private Integer cardManageId;

    /**
     * 持卡人信息
     */
    private Integer holderId;

    /**
     * 卡组织
     */
    private CardSchemeEnum cardScheme;

    /**
     * 联系信息 email or phone
     */
    private String contact;
}