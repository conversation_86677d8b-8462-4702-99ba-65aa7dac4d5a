package com.qf.zpay.dto.req.v2;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 17:14
 */
@Data
public class CardRechargeReqDto {

    @NotEmpty
    private String cardId;

    @NotNull
    @Positive
    private BigDecimal amount;

}
