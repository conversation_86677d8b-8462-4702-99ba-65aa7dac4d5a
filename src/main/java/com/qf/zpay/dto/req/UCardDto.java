package com.qf.zpay.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UCardDto implements Serializable {

    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 电话前缀
     */
    private String mobilePrefix;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 姓氏
     */
    private String firstName;
    /**
     * 名字
     */
    private String lastName;
    /**
     * 生日 yyyy-mm-dd
     */
    private String dateOfBirth;
    /**
     * 国家代码
     */
    private String countryCode;
    /**
     * 电话
     */
    private String phoneNumber;

    /**
     * 城市
     */
    private String city;
    /**
     * 国家名称
     */
    private String state;
    /**
     * 地址
     */
    private String line1;
    /**
     * 街
     */
    private String line2;
    /**
     * 邮编
     */
    private String postalCode;
}