package com.qf.zpay.dto.req;

import com.qf.zpay.constants.PayeeEnum;
import com.qf.zpay.constraints.InEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class PaymentPayeeSaveDto {

    /**
     * 商户id
     */
    private Integer businessId;

    private String payeeId;

    /**
     * 收款人类型（INDIVIDUAL 个人 / ENTERPRISE 企业）
     */
    @NotBlank(message = "{Valid.subjectType}")
    @InEnum(value = PayeeEnum.class)
    private String subjectType;

    /**
     * 收款币种
     */
    private String currency;

    /**
     * 国家code
     */
    private String country;

    /**
     * 收款人名（个人类型必填）
     */
    @NotBlank(message = "{Valid.firstName}")
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String firstName;

    /**
     * 收款人姓（个人类型必填）
     */
    @NotBlank(message = "{Valid.lastName}")
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String lastName;

    /**
     * 区号
     */
    private String nationCode;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 国家code
     */
    private String twoLetterCode;

    /**
     * 地址
     */
    private String address;


    /**
     * 收款账户类型  电子钱包：E_WALLET  银行账户：BANK_ACCOUNT
     */
    private String accountType;


    /**
     * 城市名称
     */
    private String city;


    /**
     * 州编码，仅美国必填。可参考美国城市列表
     */
    private String state;

    /**
     * 邮编
     */
    private String postcode;


}
