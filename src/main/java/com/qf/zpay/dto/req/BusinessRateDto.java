package com.qf.zpay.dto.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BusinessRateDto {

    private Integer cardManageId;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户充值手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;


    @NotNull
    private BigDecimal cardSingleLimit;
    @NotNull
    private BigDecimal cardMonthLimit;
    @NotNull
    private BigDecimal cardTotalLimit;
}
