package com.qf.zpay.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentPayOrderDto {
    private Integer id;

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 收款人id
     */
    private Integer payeeId;
    /**
     * 付款方式
     */
    private String paymentMethod;
    /**
     * 到账币种
     */
    private String currencyReceived;
    /**
     * 到账金额
     */
    private BigDecimal amountReceived;
    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;
    /**
     * 平台单笔手续费
     */
    private BigDecimal platformFee;
    /**
     * 单笔手续费
     */
    private BigDecimal platformFeeRate;
    /**
     * 付款费率
     */
    private BigDecimal platformFeeTotal;
    /**
     * 上游总收费
     */
    private BigDecimal businessFee;
    /**
     * 上游手续费
     */
    private BigDecimal businessFeeRate;
    private BigDecimal businessFeeTotal;

    /**
     * 付款用途
     */
    private String payer;
    /**
     * 交易状态
     */
    private String paymentPurpose;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 失败原因
     */
    private String failureReason;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;
    /**
     * 付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date paymentTime;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date completionTime;
    /**
     * 锁汇id
     */
    private String quoteId;


    private String currency;
    /**
     * 地区
     */
    private String country;
    /**
     * 人名
     */
    private String firstName;

    private String lastName;

    private String englishNameCountry;
    /**
     * 公司
     */
    private String accountHolder;
    /**
     * INDIVIDUAL 个人   / ENTERPRISE	企业
     */
    private String subjectType;


    /**
     * 商户id
     */
    private Integer businessId;


    /**
     * 商户名
     */
    private String businessName;

    private String fullName;

    /**
     * 上游手续费
     */
    private String upstreamOrderId;


    private String remark;

    private String costPaymentRate;

    /**
     * 付款账户
     */
    private String paymentAccount;
}
