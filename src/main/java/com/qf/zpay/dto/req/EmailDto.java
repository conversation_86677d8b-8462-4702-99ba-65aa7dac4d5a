package com.qf.zpay.dto.req;


import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 16:51
 */
@Data
public class EmailDto {

    @NotNull(message = "{Valid.email}")
    @NotBlank(message = "{Valid.email}")
    @Email(message = "{Valid.email}")
    private String email;
}
