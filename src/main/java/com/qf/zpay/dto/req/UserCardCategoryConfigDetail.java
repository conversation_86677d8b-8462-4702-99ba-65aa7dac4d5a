package com.qf.zpay.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @TableName tb_user_card_category_config
 */
@Data
public class UserCardCategoryConfigDetail implements Serializable {

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支持的产品模式【Standard:标准卡,ShareBalance:共享卡】
     */
    private String cardModel;

    /**
     * 支持的结算币种
     */
    private String cardCurrency;

    /**
     *
     */
    private String cardScheme;

    /**
     * 状态：0不可用，1可用
     */
    private Integer state;
}