package com.qf.zpay.dto.req;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片交易流水
 */
@Data
public class CardTransactionStatementDto implements Serializable {

    private Integer id;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    private String cardNumber;

    /**
     * 额度流向
     */
    private String fundsDirection;
    /**
     * 币种
     */
    private String transAmountCurrency;
    private String authAmountCurrency;
    /**
     * 交易金额
     */

    private BigDecimal transAmount;
    /**
     * 交易手续费
     */

    private BigDecimal handlingCharge;
    /**
     * 变动金额
     */
    private BigDecimal settledAmount;

    private String orderId;

    private String cardId;

    /**
     * 卡模式 Standard:标准卡,ShareBalance:共享卡
     */
    private String cardModel;

    /**
     * 交易类型【Consume:消费、ConsumeRefund:消费退款、ConsumeDispute:消费争议、DisputeRelease:争议释放、
     * ConsumeReversal:消费冲正、ConsumeRefundReversal:消费退款冲正、AuthQuery:预授权查询、另外自己加上
     * 开卡手续费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  销卡结算金额cardCancel】
     */
    private String transactionType;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    private String status;

    /**
     * 失败原因
     */
    private String failureReason = "成功";

    /**
     * 商户名
     */
    private String businessName;
    /**
     * 可用额度
     */
    private BigDecimal availableCredit;
    private BigDecimal authAmount;


    /**
     * 卡昵称
     */
    private String cardNickname;
    /**
     * 交易金额 + 币种
     */
    private String transAmountStr;


}
