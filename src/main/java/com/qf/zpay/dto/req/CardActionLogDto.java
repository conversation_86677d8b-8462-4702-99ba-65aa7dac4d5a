package com.qf.zpay.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CardActionLogDto {

    private Integer id;

    /**
     * 1 开卡 2 充值 3 销卡
     */
    private Integer type;

    /**
     * 记录金额
     */
    private BigDecimal money;

    /**
     * 卡片Id
     */
    private String cardId;

    /**
     * 商户id
     */
    private Integer businessId;


    /**
     * 商户费用
     */
    private BigDecimal businessFee;

    /**
     * 商户费率
     */
    private BigDecimal businessRate;

    /**
     * 平台收益
     */
    private BigDecimal platformEarn;

    /**
     * 商户收益
     */
    private BigDecimal businessEarn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;

    /**
     * 交易Id
     */
    private String orderId;

    private String cardNumber;
    /**
     * 状态
     */
    private String status;

    /**
     * 卡模式
     */
    private String cardModel;

    /**
     * 备注
     */
    private String note;

    private String typeDesc;
}