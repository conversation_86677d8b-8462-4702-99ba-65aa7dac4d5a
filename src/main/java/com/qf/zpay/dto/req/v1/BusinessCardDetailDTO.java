package com.qf.zpay.dto.req.v1;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

@Data
public class BusinessCardDetailDTO {

    /**
     * 商户号id
     */
    @JsonProperty("business_id")
    private Integer businessId;

    /**
     * 卡片id
     */
    @JsonProperty("cardId")
    private String cardId;

    /**
     * 当前页数
     */
    @JsonProperty("page")
    private Integer page;

    /**
     * 每页显示条数
     */
    @JsonProperty("pageSize")
    private Integer pageSize;

    /**
     * 数据类型
     */
    @Nullable
    @JsonProperty("type")
    private String type;
}
