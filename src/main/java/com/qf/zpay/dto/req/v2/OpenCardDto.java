package com.qf.zpay.dto.req.v2;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/26 14:21
 */
@Data
public class OpenCardDto {

    @NotNull
    @Positive
    private Integer cardManageId;

    //    @NotNull
//    @Positive
    // 可以不传 amount
    private BigDecimal amount;
}
