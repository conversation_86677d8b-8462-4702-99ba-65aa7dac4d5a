package com.qf.zpay.dto.req;

import com.qf.zpay.constants.PayeeEnum;
import com.qf.zpay.constraints.InEnum;
import generator.domain.PaymentPayeeAccount;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentBankingDto {

    @Valid
    private PaymentPayeeAccount account;
    /**
     * 银行代付锁汇需要
     */
    @Valid
    private AccountRegistryDto accountRegistryDto;

    /**
     * 金额
     */
    @NotNull(message = "{Valid.amount}")
    @Min(value = 1, message = "{Valid.amount_min}")
    private BigDecimal amount;


    /**
     * 收款人类型（INDIVIDUAL 个人 / ENTERPRISE 企业）
     */
    @NotBlank(message = "{Valid.subjectType}")
    @InEnum(value = PayeeEnum.class)
    private String subjectType;

    /**
     * 收款币种
     */
    private String currency;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 收款人名（个人类型必填）
     */
    @NotBlank(message = "{Valid.firstName}")
    @Pattern(regexp = "^[\u4e00-\u9fa5]+$", message = "{Valid.accountHolderChinese}")
    private String firstName;

    /**
     * 收款人姓（个人类型必填）
     */
    @NotBlank(message = "{Valid.lastName}")
    @Pattern(regexp = "^[\u4e00-\u9fa5]+$", message = "{Valid.accountHolderChinese}")
    private String lastName;

    /**
     * 区号
     */
    private String nationCode;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 国家code
     */
    private String twoLetterCode;

    /**
     * 地址
     */
    private String address;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 州编码，仅美国必填。可参考美国城市列表
     */
    private String state;

    /**
     * 邮编
     */
    private String postcode;
}
