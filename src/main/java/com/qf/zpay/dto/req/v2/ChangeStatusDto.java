package com.qf.zpay.dto.req.v2;

import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.constraints.InEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @updateTime 2024/6/25 17:06
 */
@Data
public class ChangeStatusDto {

    @NotNull
    @NotBlank
    @InEnum(value = CardStatusActionEnum.class)
    private String action;

}
