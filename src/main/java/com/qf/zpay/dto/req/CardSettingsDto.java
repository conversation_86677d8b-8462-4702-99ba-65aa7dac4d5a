package com.qf.zpay.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CardSettingsDto {

    /**
     * 交易流水号
     */
    private String transactionSerialNumber;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡id
     */
    private String cardId;

    private String cardName;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 卡模式 Standard:标准卡,ShareBalance:共享卡
     */
    private String cardModel;
    /**
     * 交易类型
     */
    private String productName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createDate;
    /**
     * 交易状态
     */
    private String status;

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    private String cardStatus;

    /**
     * 开始日期
     */
    private String dateRange;
    /**
     * 结束日期
     */
    private String EndDate;
    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    private String fundsDirection;

    private String cardDuan;
    private Integer sort;
    private Integer subType;

    /**
     * 钱包类型
     * main_wallet 主账户钱包
     * store_wallet 储值卡钱包
     * share_wallet 共享卡钱包
     * physical_wallet 实体卡钱包
     * payment_wallet 代付钱包
     * usdt_wallet USDT 钱包
     * token_wallet代币钱包
     */
    private String walletType;

    /**
     * 当前页数
     */
    private Integer page;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

}
