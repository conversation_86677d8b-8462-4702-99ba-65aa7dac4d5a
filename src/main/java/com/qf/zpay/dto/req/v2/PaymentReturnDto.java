package com.qf.zpay.dto.req.v2;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentReturnDto {
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 收款人id
     */
    private String payeeId;

    /**
     * 收款人到账金额
     */
    private Surcharge receiveAmount;
    /**
     * 付款金额
     */
    private Surcharge payAmount;

    /**
     * 手续费
     */
    private Surcharge surcharge;

    /**
     * 收款人账户id
     */
    private String accountNo;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 收款方式
     */
    private String paymentMethod;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;


}
