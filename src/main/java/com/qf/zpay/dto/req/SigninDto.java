package com.qf.zpay.dto.req;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @updateTime 2024/6/4 13:56
 */
@Data
public class SigninDto {

    @Email(message = "{Valid.email}")
    private String email;


    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
    private String password;


    @Length(min = 6, max = 6, message = "{Valid.code}")
    private String code;

    /**
     * 商户名称
     */
    private String businessName;


}
