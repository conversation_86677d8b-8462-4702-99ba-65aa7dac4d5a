package com.qf.zpay.dto.req.v2;

import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.constraints.InEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @updateTime 2024/7/9 18:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CardListReqDto extends PageReqDto {


    @InEnum(value = CardModelEnum.class)
    private String cardModel;

    @InEnum(value = CardSchemeEnum.class)
    private String cardScheme;

    @InEnum(value = CardStatusEnum.class)
    private String cardStatus;

}
