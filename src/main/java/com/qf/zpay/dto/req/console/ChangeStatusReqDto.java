package com.qf.zpay.dto.req.console;

import com.qf.zpay.dto.req.v2.ChangeStatusDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @updateTime 2024/6/28 14:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChangeStatusReqDto extends ChangeStatusDto {

    @NotBlank
    private String cardId;
}
