package com.qf.zpay.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @TableName tb_user_card_log
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserCardTradeStatDto implements Serializable {
    /**
     * 成功数
     */
    private Long authSuccessSum;
    /**
     * 失败数
     */
    private Long authFailureSum;
    /**
     * 成功率
     */
    private double authSuccessRate;
    /**
     * 失败率
     */
    private double authFailureRate;
}