package com.qf.zpay.dto.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @TableName tb_user_card_manage
 */
@Data
public class UserCardManageDetail implements Serializable {

    /**
     * 卡片类别
     */
    private Integer cardCategory;
    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡归属
     */
    private CardSchemeEnum cardBelongTo;

    /**
     * 应用场景
     */
    private String cardChangjing;

    /**
     * 发卡国家
     */
    private String cardSourceCountry;

    /**
     * 卡段编号
     */
    private String cardDuan;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    private BigDecimal cardChuzhiMax;

    /**
     * 卡背图
     */
    private String cardImg;

    /**
     *
     */
    private CardModelEnum cardModel;

    /**
     *
     */
    private Integer cardStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}