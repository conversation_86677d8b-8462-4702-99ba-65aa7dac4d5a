package com.qf.zpay.dto.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class UserCardDto {

    private Integer id;

    /**
     * 商户id
     */
    private Integer uid;

    /**
     * 商户旗下的用户id
     */
    private Integer uidChild;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     *
     */
    private BigDecimal virtualAmt;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 卡模式 Standard:标准卡,ShareBalance:共享卡
     */
    private String cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    private String cardAmt;

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    private String cardTotalAmt;

    /**
     * 订单任务编号
     */
    private String orderNo;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 有效期
     */
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    private String cardStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 卡片申请时间
     */
    private Date applyTime;

    /**
     *
     */
    private Integer status;

    /**
     * 实际开卡的时间
     */
    private Date openCardTime;

    /**
     * 实际开卡的日期
     */
    private Date openCardDate;

    /**
     * 注销卡片的时间
     */
    private Date cancelCardTime;

    /**
     * 注销卡片的日期
     */
    private Date cancelCardDate;

    /**
     * 上游渠道
     */
    private Object channel;

    /**
     *
     */
    private Integer cardManageId;
}
