package com.qf.zpay.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片费率配置表
 *
 * @TableName tb_user_card_fee_config
 */

@Data
public class UserCardFeeConfigDetailVo implements Serializable {
    /**
     *
     */
    private Integer id;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    /**
     * 卡段
     */
    private String cardDuan;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;
    /**
     * 开卡数
     */
    private Integer numberOfCardsThatCanBeOpened;


    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;

    /**
     * 卡段创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String updateBy;
    private String cardCurrency;

    private String productName;

    private BigDecimal firstRecharge;
    private Integer numberOfCardsIssued;

    private BigDecimal cardMonthFee;
    private BigDecimal cardSingleLimit;
    private BigDecimal cardMonthLimit;
    private BigDecimal cardTotalLimit;

    private UserCardManageDetail userCardManage;

        /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;

}