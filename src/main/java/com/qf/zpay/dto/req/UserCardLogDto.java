package com.qf.zpay.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qf.zpay.controller.open.v2.HolderController;
import generator.domain.CardHolder;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName tb_user_card_log
 */

@Data
public class UserCardLogDto implements Serializable {
    /**
     * 
     */

    private Integer id;

    /**
     * 商户下的用户id
     */
    private Integer childUserId;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 消息通知类型【cardPay:卡消费通知、acctColl:收款入账通知、acctExchange:换汇结果通知、
     * acctPay:付款相关通知、acctAuth:报备信息审核结果通知】另外自己加上  开卡费cardOpenCharge
     * 充值（划转）cardRecharge  充值手续费cardRechargeCharge  实际充值到账realCardRecharge  销卡结算金额cardCancel
     */
    private String noticeType;

    /**
     * 交易id
     */
    private String orderId;

    /**
     * 预授权时间
     */
    private String authTime;

    /**
     * 币种
     */
    private String transAmountCurrency;

    /**
     * 	交易金额
     */
    private BigDecimal transAmount;

    /**
     * 
     */
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    private BigDecimal authAmount;

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 脱敏卡号
     */
    private String maskCardNumber;

    /**
     * 卡模式
     */
    private String cardModel;

    /**
     * 卡别名
     */
    private String cardAlias;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户国家代码
     */
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 商户所在州或区
     */
    private String merchantState;

    /**
     * 商户邮编
     */
    private String merchantZipCode;

    /**
     * 商户描述
     */
    private String merchantDesc;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    private String status;

    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    private String fundsDirection;

    /**
     * 交易类型【Consume:消费、ConsumeRefund:消费退款、ConsumeDispute:消费争议、DisputeRelease:争议释放、
     * ConsumeReversal:消费冲正、ConsumeRefundReversal:消费退款冲正、AuthQuery:预授权查询、另外自己加上
     * 开卡手续费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  销卡结算金额cardCancel】
     */
    private String transactionType;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 交易备注
     */
    private String note;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createDate;

    /**
     * 商户名
     */
    private String businessName;
    /**
     * 可用额度
     */
    private BigDecimal availableCredit;

    private String cardNumber;
    private String cardBelongTo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date applyTime ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime ;
    private Integer holderId;
    private UserCardLog userCardLog;




}