package com.qf.zpay.dto.req;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片费率配置表
 *
 * @TableName tb_user_card_fee_config
 */
@Data
public class UserCardFeeConfigDetail implements Serializable {


    /**
     * 卡段
     */
    private String cardBin;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;
    /**
     * 开卡数
     */
    private Integer numberOfCardsThatCanBeOpened;


    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;

    /**
     * 卡段创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 首充金额
     */
    private BigDecimal firstRecharge;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;
    /**
     * 卡单笔限额
     */
    private BigDecimal cardSingleLimit;
    /**
     * 卡月限额
     */
    private BigDecimal cardMonthLimit;
    /**
     * 卡总限费
     */
    private BigDecimal cardTotalLimit;

    /**
     * 授权手续费
     */
    private BigDecimal preAuthFee;
    /**
     * 跨境手续费率%
     */
    private BigDecimal crossBroadFeeRate;
    /**
     * 交易手续费率%
     */
    private BigDecimal transactionFeeRate;
    /**
     * 退款手续费
     */
    private BigDecimal refundFee;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}