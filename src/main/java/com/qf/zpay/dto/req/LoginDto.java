package com.qf.zpay.dto.req;

import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @updateTime 2024/6/4 13:56
 */
@Data
public class LoginDto {

    @Email(message = "{Valid.email}")
    private String email;


    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
    private String password;


    @Length(min = 6, max = 6, message = "{Valid.code}")
    private String code;
}
