package com.qf.zpay.dto.req;

import generator.domain.PaymentPayeeAccount;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ApplyQuoteDto {

    @Valid
    private PaymentPayeeAccount account;
    /**
     * 银行代付锁汇需要
     */
    @Valid
    private AccountRegistryDto accountRegistryDto;
    /**
     * 金额
     */
    @NotNull(message = "{Valid.amount}")
    @Min(value = 1, message = "{Valid.amount_min}")
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;


    /**
     * 渠道id
     */
    private String channelId;

}
