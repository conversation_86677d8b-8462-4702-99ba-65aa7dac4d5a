package com.qf.zpay.dto.req;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PayeeRequestDto {
    /**
     * 收款人id
     */
    private String accountId;
    /**
     * 金额
     */
    @NotNull(message = "{Valid.amount}")
    @Min(value = 1, message = "{Valid.amount_min}")
    private BigDecimal amount;

    private String remark;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 渠道方式
     */
    private String paymentMethod;
    /**
     * 渠道方式
     */
    private String clearingNetwork;

}
