package com.qf.zpay.dto.req;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Map;

@Data
public class PaymentPayeeVO {

    private Integer id;
    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 国家
     */
    private String country;

    /**
     * 收款人
     */
    private String payeeId;

    /**
     * 收款币种
     */
    private String currency;

    /**
     * 收款人名（个人类型必填）
     */
    private String firstName;

    /**
     * 收款人姓（个人类型必填）
     */
    private String lastName;

    /**
     * 手机号
     */
    private String phone;


    @TableField(exist = false)
    private Map<String, String> countries;

}
