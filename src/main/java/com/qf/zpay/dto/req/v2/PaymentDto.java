package com.qf.zpay.dto.req.v2;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Data
public class PaymentDto {
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 收款人
     */
    private String payeeId;

    private String  paymentAccount;

    private String paymentMethod;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 失败原因
     */
    private String failureReason;
    /**
     * 到账金额
     */
    private Map<String,Object> amountReceived;
    /**
     * 付款金额
     */
    private  Map<String,Object> paymentAmount;
    /**
     * 手续费
     */
    private  Map<String,Object> fee;
    /**
     * 付款费率
     */
    private BigDecimal paymentRate;

    private String remark;

    private Date createTime;

    private Date paymentTime;


}
