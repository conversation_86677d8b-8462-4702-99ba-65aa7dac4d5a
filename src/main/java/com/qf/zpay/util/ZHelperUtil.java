package com.qf.zpay.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gsalary.sdk.GSalaryClient;
import com.gsalary.sdk.GSalaryConnectionConfig;
import com.gsalary.sdk.entity.RawGSalaryRequest;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.GSalaryException;
import com.qf.zpay.response.ResultCode;
import generator.domain.ReqLog;
import generator.mapper.ReqLogMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.*;


/**
 * <AUTHOR>
 * @updateTime 2024/6/19 10:26
 */
@Slf4j
@Component
public final class ZHelperUtil {
    @Value("${GSalary.url}")
    private String url;
    @Value("${GSalary.appId}")
    private String appId;

    public static String genOrderId(String prefix) {
        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        return prefix + dateStr + (int) (Math.random() * 1000);
    }

    public GSalaryClient client() {
        return getGSalaryClient(this.appId, this.url);
    }

    public static GSalaryClient getGSalaryClient(String appId, String url) {
        String basePath = new File("").getAbsolutePath(); // 获取项目的根路径
        String privateKeyPath = basePath + "/cert/private/gsalary.pem";
        String publicKeyPath = basePath + "/cert/public/gsalary.pem";
        try {
            FileInputStream privateKeyStream = new FileInputStream(privateKeyPath);
            FileInputStream publicKeyStream = new FileInputStream(publicKeyPath);
            GSalaryConnectionConfig config = new GSalaryConnectionConfig()
                    .setAppid(appId)
                    .setClientPrivateKeyFromStream(privateKeyStream)
                    .setServerPublicKeyFromStream(publicKeyStream)
                    .setEndpoint(url);
            return new GSalaryClient(config);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验DTO
     *
     * @param obj   请求参数
     * @param clazz DTO类
     * @return DTO对象
     */
    public static <T> T checkDto(JSONObject obj, Class<T> clazz) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        T dtoBean = JSONUtil.toBean(obj, clazz);
        Set<ConstraintViolation<T>> violations = validator.validate(dtoBean);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
        return dtoBean;
    }


    /**
     * 发送请求封装
     *
     * @param url     请求地址
     * @param method  请求方法
     * @param headers 请求头
     * @param data    请求数据
     * @param channel 渠道标识
     */
    public JSONObject sendReq(
            String url,
            Method method,
            HashMap<String, String> headers,
            JSONObject data,
            String channel
    ) {
        try {
            if (method == Method.GET) {
                url += "?" + HttpUtil.toParams(data);
                data = null;
            }
            Integer reqLogId = logReq2db(channel, url, headers, method.toString(), data);
            HttpRequest httpClient = HttpRequest.of(url);
            httpClient.timeout(20000);
            httpClient.setMethod(method);
            headers.forEach(httpClient::header);
            httpClient.body(JSONUtil.toJsonStr(data));
            httpClient.setFollowRedirects(true);
//            httpClient.setHttpProxy("127.0.0.1", 3128);
            HttpResponse result = httpClient.execute();
            if (result.getStatus() != 200) {
                log.error("请求失败，状态码：{}, body: {}", result.getStatus(), result.body());
                throw new ApiException(ResultCode.FAILED);
            }
            logReqOk(reqLogId, result.headers(), result.body());
            return JSONUtil.parseObj(result.body());
        } catch (Exception e) {
            log.error("请求失败", e);
            throw new ApiException(ResultCode.FAILED);
        }
    }


    @Autowired
    ReqLogMapper reqLogMapper;

    public Integer logReq2db(String channel, String url, Object header, String method, JSONObject body) {
        ReqLog reqLog = new ReqLog();
        reqLog.setChannel(channel);
        reqLog.setReqUrl(url);
        reqLog.setReqHeader(header.toString());
        reqLog.setReqMethod(method);
        if (body != null) {
            reqLog.setReqBody(body.toString());
        }
        reqLog.setReqTime(new Date());
        reqLogMapper.insert(reqLog);
        return reqLog.getId();
    }

    public void logReqOk(Integer id, Object header, Object body) {
        ReqLog reqLog = reqLogMapper.selectById(id);
        reqLog.setResBody(body.toString());
        reqLog.setResHeader(header.toString());
        reqLog.setResTime(new Date());
        reqLog.setStatus(1);
        reqLogMapper.updateById(reqLog);
    }


    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty()) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static String getI18nMessages(String name) {
        ResourceBundle messages = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
        return messages.getString(name);
    }


    public JSONObject sendReqGSalary(String path, Map<String, Object> reqMap) throws JsonProcessingException {
        Integer id = logReq2db("GSalary", this.url + path, " ", Method.POST.toString(), new JSONObject(reqMap));
        RawGSalaryRequest request = RawGSalaryRequest.post(path, null, new ObjectMapper().writeValueAsString(reqMap));
        String request1 = client().request(request);
        logReqOk(id, " ", request1);
        return getEntries(request1);
    }

    public JSONObject sendReqPutGSalary(String path, Map<String, Object> reqMap) throws JsonProcessingException {
        Integer id = logReq2db("GSalary", this.url + path, " ", Method.PUT.toString(), new JSONObject(reqMap));
        RawGSalaryRequest request = RawGSalaryRequest.put(path, null, new ObjectMapper().writeValueAsString(reqMap));
        String request1 = client().request(request);
        logReqOk(id, " ", request1);
        return getEntries(request1);
    }

    public JSONObject sendReqGetGSalary(String path, Map<String, String> reqMap) {
        Integer id = logReq2db("GSalary", this.url + path, " ", Method.GET.toString(), new JSONObject(reqMap));
        RawGSalaryRequest request = RawGSalaryRequest.get(path, reqMap);
        String request1 = client().request(request);
        logReqOk(id, " ", request1);
        return getEntries(request1);
    }

    public JSONObject sendReqDelGSalary(String path, Map<String, String> reqMap) {
        Integer id = logReq2db("GSalary", this.url + path, " ", Method.DELETE.toString(), new JSONObject(reqMap));
        RawGSalaryRequest request = RawGSalaryRequest.delete(path, reqMap);
        String request1 = client().request(request);
        logReqOk(id, " ", request1);
        return getEntries(request1);
    }

    private JSONObject getEntries(String request1) {
        JSONObject jsonObject = new JSONObject(request1);
        String result1 = jsonObject.getJSONObject("result").getStr("result");
        if (result1.equals("F") || result1.equals("U")) {
            throw new GSalaryException(jsonObject.getJSONObject("result").getStr("code"), jsonObject.getJSONObject("result").getStr("message"));
        }

        return jsonObject.isNull("data") ? null : jsonObject.getJSONObject("data");
    }

    public String sendReqDleGSalary(String path, Map<String, String> reqMap) {
        Integer id = logReq2db("GSalary", this.url + path, " ", Method.DELETE.toString(), new JSONObject(reqMap));
        RawGSalaryRequest delete = RawGSalaryRequest.delete(path, reqMap);
        String request1 = client().request(delete);
        logReqOk(id, " ", request1);
        return request1;
    }
}
