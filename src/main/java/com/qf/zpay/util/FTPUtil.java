package com.qf.zpay.util;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.itextpdf.text.Font;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 实名认证生成类
 */
@Slf4j
public class FTPUtil {

    /**
     * 日期格式
     */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 填充PDF模板并生成图片
     *
     * @param sourceMap  数据源Map
     * @param cardNumber 卡号
     * @param newPdfPath 新的PDF路径
     */
    public static void fillPDFTemplate(Map<String, Object> sourceMap, String cardNumber, String PdfPath, String newPdfPath, String basePath) throws IOException, DocumentException {
        PdfReader reader = new PdfReader(PdfPath);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(reader, bos);
        AcroFields form = stamper.getAcroFields();
        String sex = sourceMap.get("sex").toString();
        String purpose = sourceMap.get("purpose").toString();
        if ("M".equals(sex)) {
            addTextByXY(stamper, 170, 705, 14);
        } else {
            addTextByXY(stamper, 200, 705, 14);
        }
        if ("消费购物(持卡人自用)".equals(purpose)) {
            addTextByXY(stamper, 129, 390, 10);
        } else if ("薪金/奖金发放和提取".equals(purpose)) {
            addTextByXY(stamper, 196, 390, 10);
        } else if ("代购(私人性质)".equals(purpose)) {
            addTextByXY(stamper, 339, 390, 10);
        } else if ("日常小额消费".equals(purpose)) {
            addTextByXY(stamper, 432, 390, 10);
        } else if ("缴付私人财务欠款".equals(purpose)) {
            addTextByXY(stamper, 129, 374, 10);
        } else if ("缴付朋友之间的账单费用".equals(purpose)) {
            addTextByXY(stamper, 264, 374, 10);
        } else {
            addTextByXY(stamper, 411, 375, 10);
        }
        // 设置 SimSun 字体
        BaseFont baseFont = BaseFont.createFont(basePath + "/SIMSUN.TTC,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        // 处理文字内容
        Map<String, String> dataMap = (Map<String, String>) sourceMap.get("dataMap");
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            String value = formatValue(entry.getValue());
            Font font = new Font(baseFont, 9);
            if ("professionEn".equals(entry.getKey())) {
                font = new Font(baseFont, 6);
            }
            form.setFieldProperty(entry.getKey(), "textfont", baseFont, null);
            form.setFieldProperty(entry.getKey(), "textsize", (font.getSize()), null);
            form.setField(entry.getKey(), value);
        }

        // 处理图片内容
        Map<String, Object> imageMap = (Map<String, Object>) sourceMap.get("imageMap");
        for (Map.Entry<String, Object> entry : imageMap.entrySet()) {
            String imgpath = entry.getValue().toString();
            if (form.getFieldPositions(entry.getKey()) != null) {
                int pageNo = form.getFieldPositions(entry.getKey()).get(0).page;
                Rectangle signRect = form.getFieldPositions(entry.getKey()).get(0).position;
                float x = signRect.getLeft();
                float y = signRect.getBottom();
                Image image = Image.getInstance(imgpath);
                PdfContentByte under = stamper.getOverContent(pageNo);
                image.scaleToFit(signRect.getWidth(), signRect.getHeight());
                image.setAbsolutePosition(x, y);
                under.addImage(image);
            }
        }
        stamper.setFormFlattening(true);
        stamper.close();

        // 将填充后的PDF转换为图片
        PDDocument pdDocument = PDDocument.load(new ByteArrayInputStream(bos.toByteArray()));
        PDFRenderer pdfRenderer = new PDFRenderer(pdDocument);
        ImageIO.setUseCache(false);
        BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(0, 300, ImageType.RGB);

        // 创建目标文件夹
        File targetFolder = new File(newPdfPath);
        if (!targetFolder.exists()) {
            targetFolder.mkdirs();
        }
        ImageIO.write(bufferedImage, "jpg", new File(newPdfPath + "/" + cardNumber + "_4.jpg"));
        reader.close();
        bos.close();
        pdDocument.close();
    }

    private static void addTextByXY(PdfStamper stamper, Integer x, Integer y, Integer size) throws DocumentException, IOException {
        PdfContentByte content = stamper.getOverContent(1);
        BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
        content.beginText();
        content.setFontAndSize(base, size);
        content.showTextAligned(Element.ALIGN_CENTER, "O", x, y, 0);
        content.endText();
    }

    /**
     * 复制图片
     *
     * @param certificateImage 图片地址
     * @param destinationPath  指定路径
     * @param cardNumber       卡号
     */
    public static void renameAndMoveImages(String certificateImage, String destinationPath, String cardNumber) throws IOException {
        List<String> imageUrls = Arrays.stream(certificateImage.split("\\|"))
                .map(String::trim)
                .toList();
        File destinationDir = new File(destinationPath);
        if (!destinationDir.exists()) {
            if (!destinationDir.mkdirs()) {
                return;
            }
        }
        for (String imageUrl : imageUrls) {
            String fileName = getFileNameFromUrl(imageUrl);
            String newFileName = cardNumber + "_" + fileName;
            Path sourcePath = Paths.get("./storage" + imageUrl);
            Path destinationPath2 = Paths.get(destinationPath, newFileName);
            Files.copy(sourcePath, destinationPath2, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * Excel模板填充数据
     *
     * @param templatePath 模板路径
     * @param dataMap      模板数据
     * @param outputPath   指定路径
     */
    public static void fillExcelAndSave(String templatePath, Map<String, Object> dataMap, String outputPath) throws IOException {
        // 读取Excel模板文件
        FileInputStream fis = new FileInputStream(templatePath);
        Workbook workbook = new XSSFWorkbook(fis);

        // 获取第一个sheet页
        Sheet sheet = workbook.getSheetAt(0);

        // 创建文字格式的单元格样式
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat((short) 49);

        // 遍历sheet页，根据数据map填充占位符
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell.getCellTypeEnum() == CellType.STRING) {
                    String cellValue = cell.getStringCellValue();
                    if (cellValue.contains("{")) {
                        String placeholder = getPlaceholder(cellValue);
                        if (dataMap.containsKey(placeholder)) {
                            cell.setCellStyle(textStyle);
                            cell.setCellValue(dataMap.get(placeholder).toString());
                        }
                    }
                }
            }
        }

        // 保存Excel文件
        FileOutputStream fos = new FileOutputStream(outputPath + "/data.xlsx");
        workbook.write(fos);
        fos.close();
    }

    private static String getPlaceholder(String cellValue) {
        Pattern pattern = Pattern.compile("\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(cellValue);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    private static String getFileNameFromUrl(String url) {
        int lastSlashIndex = url.lastIndexOf("/");
        if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }
        return "";
    }

    /**
     * 格式化值为字符串
     *
     * @param value 要格式化的值
     * @return 格式化后的字符串
     */
    private static String formatValue(Object value) {
        if (value instanceof Date) {
            return DATE_FORMAT.format((Date) value);
        }
        return value.toString();
    }
}
