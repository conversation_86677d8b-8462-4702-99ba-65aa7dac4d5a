package com.qf.zpay.util;

import jakarta.mail.BodyPart;
import jakarta.mail.Message;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @updateTime 2024/10/29 17:04
 */
@Slf4j
@Component
public class EmailUtil {

    public static String[] parseFrom(String emailSender) {
        // 正则表达式：匹配 "用户名 <邮箱>" 格式
        Pattern pattern = Pattern.compile("^(.*?)\\s*<([^>]+)>$");
        Matcher matcher = pattern.matcher(emailSender.trim());
        if (matcher.find()) {
            return new String[]{matcher.group(1).trim().replaceAll("^\"|\"$", ""), matcher.group(2).trim()};
        }
        return new String[]{null, null};
    }

    public static String getTextContent(Message msg) {
        StringBuilder result = new StringBuilder();
        try {
            Object content = msg.getContent();
            if (content instanceof String) {
                result.append((String) content);
            } else if (content instanceof Multipart) {
                result.append(getTextFromMultipart((Multipart) content));
            }
        } catch (Exception e) {
            log.error("解析邮件内容失败", e);
        }
        return result.toString();
    }

    private static String getTextFromMultipart(Multipart multipart) {
        StringBuilder result = new StringBuilder();
        try {
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                if (bodyPart.isMimeType("text/plain")) {
                    result.append(bodyPart.getContent());
                } else if (bodyPart.isMimeType("text/html")) {
                    String html = (String) bodyPart.getContent();
                    result.append(Jsoup.parse(html).text());
                } else if (bodyPart.getContent() instanceof Multipart) {
                    result.append(getTextFromMultipart((Multipart) bodyPart.getContent()));
                }
            }
        } catch (Exception e) {
            log.error("提取邮件内容失败", e);
        }
        return result.toString();
    }

    public static List<Path> parseAndSaveAttachments(Message msg, Path path) {
        List<Path> attachments = new ArrayList<>();
        try {
            Multipart multipart = (Multipart) msg.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                    InputStream is = bodyPart.getInputStream();
//                    String filePath = dir + File.separator + bodyPart.getFileName();
                    Path filePath = Paths.get(path.toString(), File.separator, bodyPart.getFileName());
                    File file = new File(String.valueOf(filePath));
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        byte[] buf = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = is.read(buf)) != -1) {
                            fos.write(buf, 0, bytesRead);
                        }
                    }
                    attachments.add(filePath);
                }
            }
        } catch (Exception e) {
            log.error("解析邮件附件失败", e);
        }
        return attachments;
    }

}
