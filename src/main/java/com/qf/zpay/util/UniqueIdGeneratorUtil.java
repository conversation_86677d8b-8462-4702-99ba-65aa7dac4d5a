package com.qf.zpay.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public final class UniqueIdGeneratorUtil {

    private static final int RANDOM_DIGITS = 4;

    private UniqueIdGeneratorUtil() {
        // 私有构造函数,防止被实例化
    }

    public static String generateUniqueId(String orderId) {
        LocalDateTime currentTime = LocalDateTime.now();
        String timeString = getCurrentTimeString(currentTime);
        int randomNumber = getRandomNumber();
        return orderId + timeString + String.format("%04d", randomNumber);
    }

    private static String getCurrentTimeString(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        return dateTime.format(formatter);
    }

    private static int getRandomNumber() {
        Random random = new Random();
        return random.nextInt((int) Math.pow(10, RANDOM_DIGITS));
    }
}