package com.qf.zpay.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;


/**
 * 日期工具类
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 15:43
 */
public class DateUtil {

    /**
     * 从Date类型的时间中提取日期部分
     */
    public static Date getDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 从Date类型的时间中提取时间部分
     */
    public static Date getTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.YEAR, 1970);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getUTCDate() {
        ZonedDateTime utc = ZonedDateTime.now(ZoneId.of("UTC"));
        return Date.from(utc.toInstant());
    }

    public static Date getUTCDate(String date) {
        Instant expireAtInstant = Instant.parse(date);
        ZonedDateTime expireAtZdt = ZonedDateTime.ofInstant(expireAtInstant, ZoneId.of("UTC"));
        return Date.from(expireAtZdt.toInstant());
    }

    public static String getSpecificDateTime() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 设置为00:00:00时间
        LocalDateTime datetime = today.atStartOfDay();
        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return datetime.format(formatter);
    }
    public static String convertISOToCustomFormat(String isoDateTimeStr) {
        // Parse the ISO 8601 date-time string to LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(isoDateTimeStr, DateTimeFormatter.ISO_DATE_TIME);

        // Format the LocalDateTime to the desired "yyyy-MM-dd HH:mm:ss" format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }
    public static String getCurrentDateTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        return now.format(formatter);
    }

    public static Date getCurrentDateTime(String dateString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
// 将字符串解析为 Date 对象
        Date date = null;
        try {
            date = dateFormat.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
            // 处理解析错误，例如设置为默认日期或重新抛出异常
        }
        return date;
    }
}
