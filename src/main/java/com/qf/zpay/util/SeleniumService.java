package com.qf.zpay.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import generator.domain.SeleniumConfig;
import generator.service.SeleniumConfigService;
import io.github.bonigarcia.wdm.WebDriverManager;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @updateTime 2024/11/7 16:11
 */
@Service
@Slf4j
public class SeleniumService {

    @Autowired
    private RedissonClient redis;
    @Autowired
    private SeleniumConfigService seleniumConfigService;

    // 多渠道 枚举
    private final HashMap<String, ChromeDriver> insMap = new HashMap<>();

    public synchronized ChromeDriver getInstance(String flag) {
        RLock login = redis.getLock("Login");
        login.lock();
        SeleniumConfig seleniumConfig = seleniumConfigService.getOne(new LambdaQueryWrapper<SeleniumConfig>().eq(SeleniumConfig::getFlag, flag).eq(SeleniumConfig::getState, 1));
        ChromeDriver chromeDriver = insMap.get(flag);
        switch (flag) {
            case "globalcash":
                if (verifyDriverGC(chromeDriver, seleniumConfig)) {
                    return chromeDriver;
                }
                log.info("【浏览器实例】重新获取实例");
                chromeDriver = initLoginGC(seleniumConfig.getUsername(), seleniumConfig.getPassword(), seleniumConfig.getLoginUrl());
                insMap.put("globalcash", chromeDriver);
                break;
        }
        return chromeDriver;
    }


    public synchronized void giveBack() {
        RLock login = redis.getLock("Login");
        if (login.isHeldByCurrentThread()) {
            login.unlock();
        }
    }

    /**
     * 判断driver是否可用
     *
     * @param driver
     * @param seleniumConfig
     * @return
     */
    private boolean verifyDriverGC(ChromeDriver driver, SeleniumConfig seleniumConfig) {
        if (null == seleniumConfig || null == driver) {
            return false;
        }
        driver.get(seleniumConfig.getIndexUrl());
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String currentUrl = driver.getCurrentUrl();
        log.info("验证 driver 地址  {}=={}", currentUrl, seleniumConfig.getIndexUrl());
        if (!currentUrl.equals(seleniumConfig.getIndexUrl())) {
            driver.quit();
            return false;
        }
        return true;
    }

    /**
     * globalcash 初始化登录
     *
     * @param username 账户
     * @param password 密码
     * @param loginUrl 登录地址
     * @return
     */
    private ChromeDriver initLoginGC(String username, String password, String loginUrl) {
        // 设置并初始化 Chrome WebDriver
        WebDriverManager.chromedriver().setup();
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless"); // 无头模式
        options.addArguments("--disable-gpu"); // 禁用 GPU 硬件加速
        options.addArguments("--no-sandbox"); // 禁用沙盒
        ChromeDriver driver = new ChromeDriver(options);
        // 导航到登录页面并执行登录流程
        driver.get(loginUrl);
        // 显式等待用户名输入框的出现，使用 Duration.ofSeconds
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(40));
        WebElement usernameField = wait.until(ExpectedConditions.visibilityOfElementLocated(By.name("username")));
        usernameField.sendKeys(username);
        WebElement passwordField = driver.findElement(By.name("password"));
        passwordField.sendKeys(password);
        WebElement getCodeButton = driver.findElement(By.cssSelector("div[data-v-05b8e2aa].getSMSCode span[data-v-05b8e2aa]"));
        getCodeButton.click();
        WebElement captchaField = driver.findElement(By.name("captcha"));
        String verificationCode = getCode();
        if (verificationCode == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        captchaField.sendKeys(verificationCode);
        WebElement loginButton = driver.findElement(By.cssSelector(".el-button.loginBtn.el-button--primary"));
        loginButton.click();
        try {
            log.info("登录 {}=={}=={}", username, password, verificationCode);
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return driver;
    }

    public String getCode() {
        try {
            long startTime = System.currentTimeMillis();
            Thread.sleep(3000);
            while (System.currentTimeMillis() - startTime < 180000) {
                RBucket<String> codeCache = redis.getBucket("qxf_code");
                if (codeCache.isExists()) {
                    String code = codeCache.get();
                    codeCache.delete();
                    return code;
                }
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("获取验证码失败", e);
        }
        log.warn("等待超时，未获取到验证码");
        return null;
    }

}
