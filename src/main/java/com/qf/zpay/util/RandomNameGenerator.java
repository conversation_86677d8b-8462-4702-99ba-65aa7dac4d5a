package com.qf.zpay.util;

import java.util.Random;

public class RandomNameGenerator {

    private static final Random random = new Random();
    private static final String vowels = "aeiou";
    private static final String consonants = "bcdfghjklmnpqrstvwxyz";

    public static String generateRandomName() {
        return generateRandomWord(4, 8) + " " + generateRandomWord(5, 10);
    }

    private static String generateRandomWord(int minLength, int maxLength) {
        int length = minLength + random.nextInt(maxLength - minLength + 1);
        StringBuilder word = new StringBuilder(length);
        boolean startWithVowel = random.nextBoolean();

        for (int i = 0; i < length; i++) {
            if (i % 2 == 0) {
                // Alternate between vowels and consonants
                word.append(startWithVowel ? randomVowel() : randomConsonant());
            } else {
                word.append(startWithVowel ? randomConsonant() : randomVowel());
            }
        }

        return word.toString().substring(0, 1).toUpperCase() + word.toString().substring(1);
    }

    private static char randomVowel() {
        return vowels.charAt(random.nextInt(vowels.length()));
    }

    private static char randomConsonant() {
        return consonants.charAt(random.nextInt(consonants.length()));
    }

}
