package com.qf.zpay.exception;


import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 17:53
 */
@ControllerAdvice
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler({
            NoResourceFoundException.class,
            NoHandlerFoundException.class,
            HttpRequestMethodNotSupportedException.class
    })
    @ResponseStatus(value = HttpStatus.NOT_FOUND)
    public JsonResult<Object> handleNotFound(Exception e) {
        return JsonResult.notFind();
    }


    @ExceptionHandler(Exception.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public JsonResult<Object> handleInternalServerError(HttpServletRequest request, Exception e) {
        log.error("Request Method: {} URL: {}", request.getMethod(), request.getRequestURL());
        log.error("Internal Server Error: ", e);
        return JsonResult.serverErr();
    }

    @ResponseBody
    @ExceptionHandler(value = ApiException.class)
    public JsonResult<Object> handle(ApiException e) {
        if (e.getErrorCode() != null) {
            return JsonResult.ko(e.getErrorCode());
        }
        return JsonResult.ko();
    }

    @ResponseBody
    @ExceptionHandler(value = GSalaryException.class)
    public JsonResult<Object> handle(GSalaryException e) {
        if (e.getCode() != null) {
            return JsonResult.serverErr2(Integer.valueOf(e.getCode()), e.getMessage());
        }
        return JsonResult.ko();
    }

    @ResponseBody
    @ExceptionHandler(value = UnAuthException.class)
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public JsonResult<Object> handle(UnAuthException e) {
        if (e.getErrorCode() != null) {
            return JsonResult.ko(e.getErrorCode());
        }
        if (e.getStatusCode() != null) {
            return JsonResult.serverErr2(Integer.valueOf(e.getStatusCode()), e.getMessage());
        }
        return JsonResult.ko();
    }

    @ResponseBody
    @ExceptionHandler(value = ParamErrException.class)
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    public JsonResult<Object> handle(ParamErrException e) {
        if (e.getErrorCode() != null) {
            return JsonResult.ko(e.getErrorCode());
        }
        return JsonResult.ko();
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<Object> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        return JsonResult.ko(ResultCode.PARAM_MISS);
    }


    @ExceptionHandler({
            MethodArgumentNotValidException.class,
            ConstraintViolationException.class,
            BindException.class
    })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<Object> methodArgumentNotValidExceptionHandler(Exception e) {
        List<String> errors;
        if (e instanceof MethodArgumentNotValidException) {
            BindingResult result = ((MethodArgumentNotValidException) e).getBindingResult();
            errors = result.getFieldErrors().stream()
                    .map(fieldError -> fieldError.getField() + ": " + fieldError.getDefaultMessage())
                    .collect(Collectors.toList());
        } else if (e instanceof ConstraintViolationException) {
            Set<ConstraintViolation<?>> violations = ((ConstraintViolationException) e).getConstraintViolations();
            errors = violations.stream()
                    .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                    .collect(Collectors.toList());
        } else {
            errors = List.of(e.getMessage());
        }
        return JsonResult.validateFailed(errors);
    }
}
