package com.qf.zpay.exception;

import com.qf.zpay.response.IErrorCode;
import com.qf.zpay.response.ResultCode;
import lombok.Data;
import lombok.Getter;


/**
 * 自定义API异常
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:33
 */
@Getter
public class UnAuthException extends RuntimeException {
    private  IErrorCode errorCode;

    private  String statusCode;

    public UnAuthException(String statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
    }

    public UnAuthException(IErrorCode errorCode) {
        this.errorCode = errorCode;
    }

}
