package com.qf.zpay.exception;


import com.qf.zpay.response.IErrorCode;
import com.qf.zpay.response.ResultCode;

/**
 * 断言处理类，用于抛出各种API异常
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:33
 */
public class Assert {

    public static void fail(IErrorCode errorCode) {
        throw new ApiException(errorCode);
    }

    public static void unAuth() {
        throw new UnAuthException(ResultCode.UNAUTHORIZED);
    }

    public static void unAuth(IErrorCode errorCode) {
        throw new UnAuthException(errorCode);
    }

    public static void paramErr() {
        throw new ParamErrException(ResultCode.PARAM_ERR);
    }

    public static void paramErr(IErrorCode errorCode) {
        throw new ParamErrException(errorCode);
    }
}
