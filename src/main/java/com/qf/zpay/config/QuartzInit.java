package com.qf.zpay.config;

import generator.domain.ZJob;
import generator.service.ZJobService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Slf4j
@Configuration
public class QuartzInit {

    @Autowired
    private Scheduler scheduler;

    @Resource
    ZJobService zJobService;

    @PostConstruct
    public synchronized void init() {
        try {
            List<ZJob> jobList = zJobService.getEnableJobList();
            for (ZJob job : jobList) {
                addJob(job);
            }
            scheduler.start();
            log.info("Scheduler start with {} jobs", jobList.size());
        } catch (Exception e) {
            log.error("Init quartz error", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            scheduler.shutdown();
            log.info("Scheduler shutdown");
        } catch (SchedulerException e) {
            log.error("Destroy quartz error", e);
        }
    }

    private synchronized void addJob(ZJob zJob) {
        try {
            JobDetail job = JobBuilder.newJob(Class.forName(zJob.getHandler()).asSubclass(Job.class))
                    .withIdentity(zJob.getName())
                    .storeDurably()
                    .build();
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .forJob(job)
                    .withIdentity(zJob.getName())
                    .withSchedule(CronScheduleBuilder.cronSchedule(zJob.getCron()))
                    .build();
            scheduler.scheduleJob(job, trigger);
        } catch (Exception e) {
            log.error("Add job error", e);
        }
    }
}