package com.qf.zpay.config;


import com.qf.zpay.security.AuthTokenInterceptor;
import com.qf.zpay.security.JwtTokenInterceptor;
import com.qf.zpay.security.RpcInterceptor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 21:33
 */
@Log4j2
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtTokenInterceptor jwtTokenInterceptor;

    @Autowired
    private AuthTokenInterceptor authTokenInterceptor;

    @Autowired
    private RpcInterceptor rpcInterceptor;


    /**
     * 跨域配置
     * 此处配置跨域失效 原因是请求先经过 过滤器 -> 拦截器 -> Mapping
     */
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**")
//                .allowedOrigins("*")
//                .allowedHeaders("*")
//                .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE")
////                .allowCredentials(true)
//                .maxAge(168000);
//    }
//
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 针对商户端请求注册拦截器
        registry.addInterceptor(jwtTokenInterceptor)
                .addPathPatterns(
                        "/console/**"
                )
                .excludePathPatterns(
                        "/console/home/<USER>"
                );

        // 针对 OpenAPI 请求注册拦截器
        registry.addInterceptor(authTokenInterceptor)
                .addPathPatterns("/v2/**")
                .excludePathPatterns(
                        "/v2/biz/code2token",
                        "/v2/biz/refresh-token"
                );

        // 针对 RPC 请求注册拦截器
        registry.addInterceptor(rpcInterceptor)
                .addPathPatterns("/rpc/**");
    }
}