package com.qf.zpay.config;


import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.database}")
    private int db;

    @Bean()
    @Primary
    public RedissonClient primaryRedisson() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://127.0.0.1:6379")
                .setDatabase(db);
        return Redisson.create(config);
    }

//    @Bean(name = "CenterRedis")
//    public RedissonClient centerRedisson() {
//        Config config = new Config();
//        config.useSingleServer()
//                .setAddress("redis://r-3ns1n8g83h7axhi3fypd.redis.rds.aliyuncs.com:6379")
//                .setUsername("default")
//                .setPassword("asd123456QFKJ");
//        config.setCodec(new StringCodec());
//
//        return Redisson.create(config);
//    }
}