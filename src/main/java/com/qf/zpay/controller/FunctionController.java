package com.qf.zpay.controller;

import com.alibaba.excel.EasyExcel;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.CardService;
import generator.domain.UserCard;
import generator.service.UserCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @updateTime 2024/7/16 17:50
 */
@RestController
@RequestMapping("/function")
@Slf4j
public class FunctionController {

    @Autowired
    private CardService cardService;
    @Autowired
    private UserCardService userCardService;

    @PostMapping("/import")
    public JsonResult<Object> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            List<UserCard> userList = EasyExcel.read(new BufferedInputStream(file.getInputStream())).head(UserCard.class).sheet().doReadSync();
            for (UserCard card : userList) {
                System.out.println(card.getCardNumber());
                String orderNo = generateSerialNumber();
                card.setProductCode("168");
                card.setCardModel(CardModelEnum.PHYSICAL);
                card.setCardCurrency("USD");
                card.setOrderNo(orderNo);
                card.setCardId(orderNo);
                card.setCardStatus(CardStatusEnum.Active);
                card.setStatus(1);
                card.setChannel(CardChannelEnum.AsinxPhysical);
                card.setCardManageId(97);
                card.setCardScheme(CardSchemeEnum.Visa);
                userCardService.save(card);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return JsonResult.ok();
    }

    @GetMapping("/preOpenCard")
    public JsonResult<Object> preOpenCard() {
        cardService.preOpenCard();
        return JsonResult.ok();
    }


    public static String generateSerialNumber() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        // 获取当前时间并格式化为字符串
        String timestamp = sdf.format(new Date());

        // 生成指定长度的随机数字字符串
        Random random = new Random();
        StringBuilder randomNumber = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            randomNumber.append(random.nextInt(10)); // 0~9 的随机数字
        }

        // 拼接结果
        return "P0A" + timestamp + randomNumber.toString();
    }
}
