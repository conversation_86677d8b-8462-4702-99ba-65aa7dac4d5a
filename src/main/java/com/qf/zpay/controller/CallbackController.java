package com.qf.zpay.controller;

import cn.hutool.json.JSONObject;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.card.channel.AsinxPhysical;
import generator.service.CbLogService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@Validated
@Slf4j
@RequestMapping("/cb")
public class CallbackController {

    @Autowired
    private CbLogService reqLogService;
    @Autowired
    private AsinxPhysical asinxPhysical;
    @Autowired
    HttpServletRequest request;

    @PostMapping("/photon")
    public JSONObject photon(HttpServletRequest request, @RequestBody String body) throws IOException {
        return reqLogService.photon(request, body);
    }

    @PostMapping("/payment")
    public JsonResult<Object> paymentPayOrder(HttpServletRequest request, @RequestBody String body) {
        reqLogService.paymentPayOrder(request, body);
        return JsonResult.ok();
    }

    @PostMapping("/asinx")
    public JsonResult<Object> asinx(HttpServletRequest request, HttpServletResponse response) {
        reqLogService.asinx(request, response);
        return JsonResult.ok("success");
    }

}
