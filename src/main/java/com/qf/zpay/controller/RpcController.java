package com.qf.zpay.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.*;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.dto.CardDetailDto;
import generator.domain.CardHolder;
import generator.domain.CoinInOut;
import generator.domain.UserCard;
import generator.service.CardHolderService;
import generator.service.CoinInOutService;
import generator.service.UserCardService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * <AUTHOR>
 * @updateTime 2024/7/16 17:50
 */
@RestController
@RequestMapping("/rpc")
@Slf4j
public class RpcController {

    @Autowired
    CardService cardService;

    @Autowired
    RedissonClient redisson;
    @Autowired
    ExchangeRateService exchangeRateService;
    @Autowired
    GmailAttachmentDownloader gmailAttachmentDownloader;

    @Resource
    private CardHelper cardHelper;


    @PostMapping("card")
    public JsonResult<Object> card(@RequestBody JSONObject body) throws JsonProcessingException {
//        Console.error("body", body);
        String cardId = body.getStr("cardId");
        switch (body.getStr("action")) {
            case "freeze":
                cardService.changeStatus(cardId, CardStatusActionEnum.Freeze);
                break;
            case "unfreeze":
                cardService.changeStatus(cardId, CardStatusActionEnum.Unfreeze);
                break;
            case "cancel":
                cardService.cancelCard(cardId);
                break;
            default:
                break;

        }
        return JsonResult.ok();
    }

    @PostMapping("sms")
    public JsonResult<Object> sms(@RequestBody JSONObject body) {
        String code = body.getStr("code");
        if (code == null) {
            return JsonResult.ko();
        }
        RBucket<String> codeCache = redisson.getBucket("qxf_code");
        codeCache.set(code);
        codeCache.expire(Duration.ofMinutes(5));
        return JsonResult.ok();
    }


    @Autowired
    FTPService ftpService;
    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    UserCardService userCardService;

    @PostMapping("ftp")
    public JsonResult<Object> ftpUpNow(@RequestBody JSONObject body) {
        Integer holderId = body.getInt("holderId");
        String cardId = body.getStr("cardId");

        CardHolder cardHolder;
        UserCard userCard;
        if (holderId != null) {
            cardHolder = cardHolderService.getById(holderId);
            QueryWrapper<UserCard> query = new QueryWrapper<>();
            query.eq("card_model", "physical");
            query.eq("uid", cardHolder.getBusinessId());
            query.eq("holder_id", cardHolder.getId());
            query.orderByAsc("open_card_time");
            userCard = userCardService.getOne(query);
        } else {
            userCard = userCardService.getOne(new LambdaQueryWrapper<>(UserCard.class)
                    .eq(UserCard::getCardId, cardId));
            cardHolder = cardHolderService.getById(userCard.getHolderId());
        }

        try {
            ftpService.upload(userCard, cardHolder);
        } catch (Exception e) {
            log.error("FTP 上传失败：", e);
        }
        return JsonResult.ok();
    }

    @Autowired
    CoinInOutService coinInOutService;
    @Autowired
    UsdtService usdtService;

    @PostMapping("withdraw/{id}")
    public JsonResult<Object> withdraw(@PathVariable("id") String id) {
        CoinInOut coinInOut = coinInOutService.getById(id);
        if (coinInOut == null) {
            JsonResult.ko();
        }
        usdtService.withdraw(coinInOut.getCoinType(),
                coinInOut.getToAddress(),
                coinInOut.getAmount(),
                coinInOut.getId().toString());
        return JsonResult.ok();
    }


    @Autowired
    CardFactory cardFactory;

    @PostMapping("fix-card")
    public JsonResult<Object> fixCard(@RequestBody JSONObject body) {
        String channel = body.getStr("channel", "photon");
        Integer businessId = body.getInt("bid");

        List<UserCard> cardList = userCardService.list(
                new LambdaQueryWrapper<>(UserCard.class)
                        .eq(UserCard::getUid, businessId)
                        .eq(UserCard::getChannel, channel)
        );

        log.info("卡片数量: {}", cardList.size());
        cardList.forEach(userCard -> {
            cardFactory.getBuilder(userCard.getChannel());
            CardDetailDto cardDetail = cardFactory.getCardDetail(userCard.getCardId());
            userCard.setCardStatus(cardDetail.getCardStatus());
            userCard.setAmount(cardDetail.getAmount());
            userCardService.updateById(userCard);
            sleep(1000);
        });
        log.info("修复完成");
        return JsonResult.ok();
    }


    @Autowired
    GlobalcashService globalcashService;

    @PostMapping("recharge")
    public JsonResult<Object> recharge(@RequestBody JSONObject body) {
        String cardNumber = body.getStr("card");
        BigDecimal amount = body.getBigDecimal("amount");
        if (cardNumber != null && amount != null) {
            try {
                globalcashService.recharge(cardNumber, amount);
            } catch (Exception e) {
                log.error("充值失败", e);
                return JsonResult.ko();
            }
        }
        return JsonResult.ok();
    }


    @PostMapping("cardActionAfter")
    public JsonResult<Object> cardRechargeAfter(@RequestBody JSONObject body) {
        String cardId = body.getStr("cardId");
        String type = body.getStr("type");
        BigDecimal amount = body.getBigDecimal("amount");
        UserCard userCard = cardService.cardInfo(cardId);
        if ("recharge".equals(type)) {
            cardHelper.cardRechargeAfter(userCard, amount);
        } else if ("cancel".equals(type)) {
            cardHelper.cardCancelAfter(userCard);
        } else if ("openCard".equals(type)) {
            cardHelper.openCardAfter(userCard);
        }
        return JsonResult.ok();
    }
}
