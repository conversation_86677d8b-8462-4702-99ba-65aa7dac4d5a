package com.qf.zpay.controller.open.v2;

import com.qf.zpay.dto.req.AccountRegistryDto;
import com.qf.zpay.dto.req.ApplyQuoteDto;
import com.qf.zpay.dto.req.PaymentBankingDto;
import com.qf.zpay.dto.req.PaymentPayeeSaveDto;
import com.qf.zpay.dto.req.v2.PaymentDto;
import com.qf.zpay.dto.req.v2.req.PaymentPayReq;
import com.qf.zpay.dto.res.v2.PaymentVo;
import com.qf.zpay.dto.res.v2.QuoteDto;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.PaymentService;
import generator.domain.PaymentPayConfig;
import generator.domain.PaymentPayeeAccount;
import generator.domain.User;
import generator.service.PaymentPayConfigService;
import generator.service.PaymentPayeeAccountService;
import generator.service.PaymentPayeeService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/v2/payment")
@Log4j2
public class PaymentController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PaymentPayeeService paymentPayeeService;
    @Autowired
    private PaymentPayConfigService paymentPayConfigService;
    @Autowired
    PaymentPayeeAccountService paymentPayeeAccountService;

    /**
     * 查询收款人账户
     *
     * @return list
     */
    @GetMapping("/account_list/{payeeId}")
    public JsonResult<Object> accountList(@PathVariable String payeeId) {
        User user = (User) request.getAttribute("user");
        List<PaymentPayeeAccount> list = paymentPayeeAccountService.accountList(user, payeeId);
        return JsonResult.ok(list);
    }

    /**
     * 查询地区
     *
     * @return ；
     */
    @GetMapping("/country_region")
    public JsonResult<Object> getCountryRegion(@RequestParam("country") String country) {
        List<PaymentPayConfig> list = paymentService.getCountryRegion(country);
        ArrayList<HashMap<String, Object>> hashMaps = new ArrayList<>();
        list.forEach(obj -> {
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("country", obj.getTwoLetterCode());
            stringObjectHashMap.put("currency", obj.getSettlementCurrency());
            stringObjectHashMap.put("english_name", obj.getEnglishNameCountry());
            stringObjectHashMap.put("chinese_name", obj.getCountryRegion());
            hashMaps.add(stringObjectHashMap);
        });
        return JsonResult.ok(hashMaps);
    }

    /**
     * 付款
     *
     * @param req 订单号
     * @return 订单 ；
     */
    @PostMapping("/payment")
    public JsonResult<Object> payment(@RequestParam(value = "quoteNo", required = false) String quoteNo, @RequestBody(required = false) PaymentPayReq req) {
        if (StringUtils.isEmpty(quoteNo) && (null == req || StringUtils.isEmpty(req.getQuoteNo()))) {
            return JsonResult.ko(ResultCode.PARAM_MISS);
        } else if (null != req && !StringUtils.isEmpty(req.getQuoteNo())) {
            quoteNo = req.getQuoteNo();
        }
        User user = (User) request.getAttribute("user");
        PaymentVo payment = paymentService.payment(quoteNo, user);
        return JsonResult.ok(payment);
    }

    //###################################################################################

    /**
     * 查询付款单
     *
     * @return 订单
     */
    @GetMapping("/query_payment_order/{orderId}")
    public JsonResult<Object> queryPayment(@PathVariable("orderId") String orderId) {
        PaymentDto payeeDto = paymentService.queryPayment(orderId);
        return JsonResult.ok(payeeDto);
    }

    /**
     * 查询钱包类型填写字段
     *
     * @param product 钱包类型
     * @return ；
     */
    @GetMapping("/product_type/{product}")
    public JsonResult<Object> getProductType(@PathVariable("product") String product) {
        HashMap<String, String> productType = paymentPayConfigService.getProductType(product);
        return JsonResult.ok(productType);
    }

    /**
     * 查询可用付款方式
     */
    @GetMapping("/payment_methods")
    public JsonResult<Object> availablePaymentMethods(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(paymentService.availablePaymentMethods3(user.getBusinessId()));
    }

    /**
     * 电子钱包锁汇
     *
     * @param dto 锁汇数据
     * @return 订单
     */
    @PostMapping("/apply_quote")
    public JsonResult<Object> applyQuote(@Valid @RequestBody(required = false) ApplyQuoteDto dto) {
        User user = (User) request.getAttribute("user");
        QuoteDto paymentPayOrder = paymentService.applyQuote(dto, user);
        return JsonResult.ok(paymentPayOrder);
    }


    /**
     * 银行代付锁汇
     */
    @PostMapping("/bank/quote")
    public JsonResult<Object> bankingQuote(@Valid @RequestBody PaymentBankingDto bankingDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        PaymentPayeeSaveDto dto = new PaymentPayeeSaveDto();
        AccountRegistryDto accountRegistryDto = bankingDto.getAccountRegistryDto();
        BeanUtils.copyProperties(bankingDto, dto);
        dto.setCurrency(accountRegistryDto.getCurrency());
        dto.setBusinessId(user.getBusinessId());
        String payeeId = paymentService.saveBankPayee(dto);
        ApplyQuoteDto quoteDto = new ApplyQuoteDto();

        accountRegistryDto.setPayeeId(payeeId);
        quoteDto.setAccountRegistryDto(accountRegistryDto);
        quoteDto.setAmount(bankingDto.getAmount());
        quoteDto.setAccount(bankingDto.getAccount());
        QuoteDto paymentPayOrder = paymentService.applyQuote(quoteDto, user);
        return JsonResult.ok(paymentPayOrder);
    }

    //===============================================

    /**
     * 查询支持付款国家(银行账户)
     */
    @GetMapping("/bank/support")
    public JsonResult<Object> getPayoutCountries() {
        return JsonResult.ok(paymentService.getPayoutCountries());
    }

    /**
     * 查询收款人
     *
     * @param lastName 姓
     * @return 收款人列表
     */
    @GetMapping("/payee")
    public JsonResult<Object> getPayee(@RequestParam(name = "lastName", required = false) String lastName, @RequestParam(name = "phone", required = false) String phone, @RequestParam(name = "accountType", required = false) String accountType) {
        User user2 = (User) request.getAttribute("user");
        return JsonResult.ok(paymentPayeeService.getPayee(user2.getBusinessId(), lastName, phone, accountType));
    }


    /**
     * 创建收款人（银行账户）
     */
    @PostMapping("/payee")
    public JsonResult<Object> saveBankPayee(@Valid @RequestBody PaymentPayeeSaveDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        dto.setBusinessId(user.getBusinessId());
        return JsonResult.ok(paymentService.saveBankPayee(dto));
    }

    /**
     * 修改收款人
     *
     * @param dto ;
     * @return ;
     */
    @PutMapping("/payee")
    public JsonResult<Object> updatePayee(@Valid @RequestBody PaymentPayeeSaveDto dto) {
        PaymentPayeeSaveDto payeeDto = paymentService.updatePayeeV2(dto);
        return JsonResult.ok(payeeDto);
    }

    /**
     * 获取收款人账户表单
     */
    @GetMapping("/bank/params")
    public JsonResult<Object> getAccountRegisterFormat(@RequestParam("payeeId") String payeeId, @RequestParam(value = "currency", required = false) String currency, @RequestParam(value = "language", required = false) String language) {
        return JsonResult.ok(paymentService.getAccountRegisterFormat(payeeId, currency, language));
    }

}
