package com.qf.zpay.controller.open.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.dto.req.v1.*;
import com.qf.zpay.dto.res.v1.*;
import com.qf.zpay.response.JsonResult;
import generator.domain.UserCard;
import generator.domain.UserCardCategoryConfig;
import generator.domain.UserCardLog;
import generator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 此类用于兼用老版本的接口
 *
 * <AUTHOR>
 * @updateTime 2024/6/3 10:21
 */
@Slf4j
@RestController
@RequestMapping("/card")
public class OldApi {


    @Autowired
    private UserCardFeeConfigService userCardFeeConfigService;

    @Autowired
    private UserCardManageService userCardManageService;

    @Autowired
    private UserCardService userCardService;

    @Autowired
    private UserCardLogService userCardLogService;

    @Autowired
    private UserCardCategoryConfigService userCardCategoryConfigService;

    /**
     * 获取可用的卡产品列表
     */
    @PostMapping("ableCardProductList")
    public JsonResult<Object> ableCardProductList(@RequestBody BusinessIdDTO businessId) {
        // 根据业务ID查询用户卡费配置列表
        List<UserCardFeeConfigWithCardManageDTO> userCardFeeConfigWithCardManageDTOList = userCardFeeConfigService.ableCardProductList(businessId.getBusinessId());
        // 创建一个UserCardFeeConfigDTO的集合
        List<UserCardFeeConfigDTO> userCardFeeConfigDTOS = new ArrayList<>();
        // 遍历userCardFeeConfigWithCardManageDTOList集合
        for (UserCardFeeConfigWithCardManageDTO userCardFeeConfigWithCardManageDTO : userCardFeeConfigWithCardManageDTOList) {
            // 创建一个UserCardFeeConfigDTO对象
            UserCardFeeConfigDTO userCardFeeConfigDTO = new UserCardFeeConfigDTO();
            // 将userCardFeeConfigWithCardManageDTO的属性值复制到userCardFeeConfigDTO中
            BeanUtils.copyProperties(userCardFeeConfigWithCardManageDTO, userCardFeeConfigDTO, "cardChangjing");
            // 获取userCardFeeConfigWithCardManageDTO的cardChangjing属性值,如果cardChangjing属性不为空，则将其拆分为列表，并赋值到userCardFeeConfigDTO的cardChangjing属性中
            if (userCardFeeConfigWithCardManageDTO.getCardChangjing() != null && !userCardFeeConfigWithCardManageDTO.getCardChangjing().isEmpty()) {
                String[] cardChangjingStrings = userCardFeeConfigWithCardManageDTO.getCardChangjing().split("/");
                userCardFeeConfigDTO.setCardChangjing(new ArrayList<>(Arrays.asList(cardChangjingStrings)));
            }
            Integer cardManageId = userCardFeeConfigWithCardManageDTO.getCardManageId();
            UserCardCategoryConfig userCardCategoryConfig = userCardCategoryConfigService.getById(cardManageId);
            userCardFeeConfigDTO.setProductCode(userCardCategoryConfig.getProductCode());
            userCardFeeConfigDTO.setCardModel(userCardCategoryConfig.getCardModel());
            userCardFeeConfigDTO.setCardCurrency(userCardCategoryConfig.getCardCurrency());
            // 将userCardFeeConfigDTO添加到集合中
            userCardFeeConfigDTOS.add(userCardFeeConfigDTO);
        }
        return JsonResult.ok(userCardFeeConfigDTOS);
    }

    /**
     * 获取商户的费率
     *
     * @return
     */
    @PostMapping("getRate")
    public JsonResult<Object> getRate(@RequestBody BusinessCardManageIdDTO businessCardManageIdDTO) {
        // 根据businessId和cardManageId查询商户卡费配置表
        UserCardFeeConfigWithCardManageDTO userCardFeeConfigWithCardManageDTO = userCardFeeConfigService.getRate(businessCardManageIdDTO.getBusinessId(), businessCardManageIdDTO.getCardManageId());
        // 创建一个UserRateDTO对象
        UserRateDTO userRateDTO = new UserRateDTO();
        // 将userCardFeeConfigWithCardManageDTO的属性值复制到userRateDTO中
        BeanUtils.copyProperties(userCardFeeConfigWithCardManageDTO, userRateDTO);
        return JsonResult.ok(userRateDTO);
    }

    /**
     * 查询卡片信息
     *
     * @return
     */
    @PostMapping("getMemberCardDetail")
    public JsonResult<Object> getMemberCardDetail(@RequestBody BusinessCardIdDTO businessCardIdDTO) {
        // 根据businessId和cardId查询用户卡表
        UserCardAndFeeConfigAndManageDTO userCardAndFeeConfigAndManageDTO = userCardService.getMemberCardDetail(businessCardIdDTO.getBusinessId(), businessCardIdDTO.getCardId());
        // 创建一个UserCardDTO对象
        UserCardDTO userCardDTO = new UserCardDTO();
        // 将userCardAndFeeConfigAndManageDTO的属性值复制到userCardDTO中
        BeanUtils.copyProperties(userCardAndFeeConfigAndManageDTO, userCardDTO);
        return JsonResult.ok(userCardDTO);
    }


    /**
     * 查询卡片流水
     *
     * @return
     */
    @PostMapping("cardLogs")
    public JsonResult<Object> cardLogs(@RequestBody BusinessCardDetailDTO businessCardDetailDTO) {
        IPage<UserCardLog> userCardLogIPage = userCardLogService.cardLogs(businessCardDetailDTO.getBusinessId(),
                businessCardDetailDTO.getCardId(),
                businessCardDetailDTO.getPage(),
                businessCardDetailDTO.getPageSize(),
                businessCardDetailDTO.getType());
        // 分页查询的结果转换为DTO对象
        List<UserCardLogDTO> userCardLogDTOList = new ArrayList<>();
        for (UserCardLog userCardLog : userCardLogIPage.getRecords()) {
            UserCardLogDTO userCardLogDTO = new UserCardLogDTO();
            // 转换为DTO对象
            BeanUtils.copyProperties(userCardLog, userCardLogDTO);
            userCardLogDTOList.add(userCardLogDTO);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total_count", userCardLogIPage.getTotal());
        resultMap.put("log_data", userCardLogDTOList);
        return JsonResult.ok(resultMap);
    }

    /**
     * 查询卡片流水详情
     *
     * @param id log 记录 ID
     * @return
     */
    @GetMapping("logDetail")
    public JsonResult<Object> getLogDetail(@RequestParam("id") Integer id) {
        UserCardLog userCardLog = userCardLogService.getById(id);
        UserCardDetailDTO userCardDetailDTO = new UserCardDetailDTO();
        BeanUtils.copyProperties(userCardLog, userCardDetailDTO);
        return JsonResult.ok(userCardDetailDTO);
    }

    /**
     * 获取卡片列表
     *
     * @param businessCardListDTO
     * @return
     */
    @PostMapping("getCardList")
    public JsonResult<Object> getCardList(@RequestBody BusinessCardListDTO businessCardListDTO) {
        // 根据businessId查询商户卡表
        IPage<UserCard> userCardIPage = userCardManageService.getCardList(businessCardListDTO.getBusinessId(), businessCardListDTO.getPage(), businessCardListDTO.getPagesize());
        // 分页查询的结果转换为DTO对象
        List<UserCardListDTO> userCardListDTOS = new ArrayList<>();
        for (UserCard userCard : userCardIPage.getRecords()) {
            UserCardListDTO userCardListDTO = new UserCardListDTO();
            // 转换为DTO对象
            BeanUtils.copyProperties(userCard, userCardListDTO);
            userCardListDTOS.add(userCardListDTO);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total_count", userCardIPage.getTotal());
        resultMap.put("list", userCardListDTOS);
        return JsonResult.ok(resultMap);
    }
}
