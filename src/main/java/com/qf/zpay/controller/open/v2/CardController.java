package com.qf.zpay.controller.open.v2;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.dto.req.EncryptDataDto;
import com.qf.zpay.dto.req.v2.*;
import com.qf.zpay.dto.res.v2.*;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.CardHelper;
import com.qf.zpay.service.CardService;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.pojo.UserCardWithFee;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.MailingAddress;
import generator.domain.User;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import generator.service.UserCardFeeConfigService;
import generator.service.UserCardLogService;
import generator.service.UserCardManageService;
import generator.service.UserCardService;
import generator.vo.CardManageWithFeeVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@RestController
@RequestMapping("/v2/card")
@Slf4j
public class CardController {

    @Autowired
    HttpServletRequest request;

    @Autowired
    RsaService rsaService;

    @Autowired
    CardService cardService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    UserCardManageService cardManageService;

    @Autowired
    UserCardFeeConfigService cardFeeConfigService;

    @Autowired
    UserCardLogService userCardLogService;

    @GetMapping("manage")
    public JsonResult<Object> getCardManage() {
        User user = (User) request.getAttribute("user");
        List<CardManageWithFeeVo> cardManageWithFeeList = cardManageService.getBusinessCardManageWithFee(user.getBusinessId());

        List<CardManageResDto> cardManageList = new ArrayList<>();
        for (CardManageWithFeeVo item : cardManageWithFeeList) {
            CardManageResDto cardManageRes = formatCardManageWithFee(item);
            if (CardModelEnum.PHYSICAL.equals(item.getCardModel()) && CardChannelEnum.AsinxPhysical.equals(item.getChannel())) {
                cardManageRes.setNeedKyc(true);
                cardManageRes.setCanSetPwd(true);
            }
            List<String> appConfig = cardService.findAppConfig();
            if (appConfig.contains(item.getProductCode())) {
                cardManageRes.setNeedSetContact(true);
            }
            cardManageList.add(cardManageRes);
        }
        return JsonResult.ok(cardManageList);

    }

    @GetMapping("manage/{cardManageId}")
    public JsonResult<Object> getCardManageInfo(@PathVariable("cardManageId") String cardManageId) {
        User user = (User) request.getAttribute("user");
        CardManageWithFeeVo cardManageWithFee = cardManageService.getOneBusinessCardManageWithFee(user.getBusinessId(), Integer.valueOf(cardManageId));
        CardManageResDto cardManageRes = formatCardManageWithFee(cardManageWithFee);
        if (CardModelEnum.PHYSICAL.equals(cardManageWithFee.getCardModel()) && CardChannelEnum.AsinxPhysical.equals(cardManageWithFee.getChannel())) {
            cardManageRes.setNeedKyc(true);
            cardManageRes.setCanSetPwd(true);
        }
        List<String> appConfig = cardService.findAppConfig();
        if (appConfig.contains(cardManageWithFee.getProductCode())) {
            cardManageRes.setNeedSetContact(true);
        }
        return JsonResult.ok(cardManageRes);
    }

    /**
     * 商户开卡
     */
    @PostMapping("open")
    public JsonResult<Object> open(@Valid @RequestBody EncryptDataDto encryptDataDto) throws JsonProcessingException {
        User user = (User) request.getAttribute("user");
        JSONObject obj = rsaService.decryptData(user.getId(), encryptDataDto.getData());
        OpenCardDto openCardDto = ZHelperUtil.checkDto(obj, OpenCardDto.class);
        UserCardWithFee cardInfo = cardService.openCard(user, openCardDto.getCardManageId(), openCardDto.getAmount());
        OpenCardResDto openCardInfo = new OpenCardResDto();
        BeanUtils.copyProperties(cardInfo, openCardInfo);
        return JsonResult.ok(openCardInfo);
    }


    @PatchMapping("recharge")
    public JsonResult<Object> recharge(@Valid @RequestBody EncryptDataDto encryptDataDto) {
        User user = (User) request.getAttribute("user");
        JSONObject obj = rsaService.decryptData(user.getId(), encryptDataDto.getData());
        CardRechargeReqDto body = ZHelperUtil.checkDto(obj, CardRechargeReqDto.class);
        String cardId = cardService.checkBizCard(user, body.getCardId(), true);
        UserCardWithFee rechargeInfo = cardService.recharge(user, cardId, body.getAmount());
        CardRechargeResDto cardRechargeResDto = new CardRechargeResDto();
        BeanUtils.copyProperties(rechargeInfo, cardRechargeResDto);
        return JsonResult.ok(cardRechargeResDto);
    }

    @PatchMapping("withdraw")
    public JsonResult<Object> withdraw(@Valid @RequestBody EncryptDataDto encryptDataDto) {
        User user = (User) request.getAttribute("user");
        JSONObject obj = rsaService.decryptData(user.getId(), encryptDataDto.getData());
        CardRechargeReqDto body = ZHelperUtil.checkDto(obj, CardRechargeReqDto.class);
        String cardId = cardService.checkBizCard(user, body.getCardId(), true);
        UserCardWithFee rechargeInfo = cardService.withdraw(cardId, body.getAmount());
        CardRechargeResDto cardRechargeResDto = new CardRechargeResDto();
        BeanUtils.copyProperties(rechargeInfo, cardRechargeResDto);
        return JsonResult.ok(cardRechargeResDto);
    }

    @GetMapping()
    public JsonResult<Object> getCardList(@Valid @ModelAttribute CardListReqDto cardListReqDto) {
        User user = (User) request.getAttribute("user");
        IPage<UserCard> userCards = userCardService.pageCardList(
                user.getBusinessId(),
                cardListReqDto.getCardModel(),
                cardListReqDto.getCardScheme(),
                cardListReqDto.getCardStatus(),
                cardListReqDto.getPage(),
                cardListReqDto.getSize()
        );

        List<CardInfoResDto> cardInfoDtoList = new ArrayList<>();
        for (UserCard userCard : userCards.getRecords()) {
            CardInfoResDto cardInfoDto = new CardInfoResDto();
            BeanUtils.copyProperties(CardHelper.filterCardInfo(userCard), cardInfoDto);
            cardInfoDtoList.add(cardInfoDto);
        }

        HashMap<String, Object> res = new HashMap<>(2);
        res.put("list", cardInfoDtoList);
        res.put("total", userCards.getTotal());
        return JsonResult.ok(res);
    }


    @GetMapping("{cardId}")
    public JsonResult<Object> getCardInfo(@PathVariable("cardId") String cardId) {
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, false);
        UserCard cardInfo = cardService.cardInfo(cardId);
        CardInfoResDto cardInfoDto = new CardInfoResDto();
        BeanUtils.copyProperties(CardHelper.filterCardInfo(cardInfo), cardInfoDto);
        if (CardChannelEnum.AsinxPhysical.equals(cardInfo.getChannel())) {
            cardInfoDto.setCardId(cardInfo.getOrderNo());
        }
        HashMap<String, Object> res = new HashMap<>(2);
        res.put("cardDetail", cardInfoDto);
        CardManageWithFeeVo cardManageWithFee = cardManageService.getOneBusinessCardManageWithFee(user.getBusinessId(), cardInfo.getCardManageId());
        CardManageResDto cardManageRes = formatCardManageWithFee(cardManageWithFee);
        if (CardModelEnum.PHYSICAL.equals(cardManageWithFee.getCardModel())) {
            if (CardChannelEnum.AsinxPhysical.equals(cardManageWithFee.getChannel())) {
                cardManageRes.setCanSetPwd(true);
            }
            if (CardChannelEnum.ZPhysical.equals(cardManageWithFee.getChannel())) {
                cardManageRes.setNeedKyc(true);
            }
        }

        res.put("cardManage", cardManageRes);
        res.put("cardHolder", null);
        return JsonResult.ok(res);
    }


    @PatchMapping("status/{cardId}")
    public JsonResult<Object> changeStatus(
            @PathVariable("cardId") String cardId,
            @Valid @RequestBody ChangeStatusDto body
    ) throws JsonProcessingException {
        if (!CardStatusActionEnum.Freeze.getCode().equals(body.getAction())) {
            return JsonResult.ko(ResultCode.CARD_STATUS_SET_JUST_FREEZE);
        }
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, true);
        UserCard cardInfo = cardService.changeStatus(cardId, CardStatusActionEnum.Freeze);
        ChangeStatusResDto resDto = new ChangeStatusResDto();
        BeanUtils.copyProperties(cardInfo, resDto);
        return JsonResult.ok(resDto);
    }

    @PatchMapping("contact/{cardId}")
    public JsonResult<Object> updateContact(
            @PathVariable("cardId") String cardId,
            @Valid @RequestBody ContactDto contact
    ) {
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, true);
        UpdateContactResDto contactResDto = cardService.updateContact(cardId, contact.getEmail());
        return JsonResult.ok(contactResDto);
    }

    @PatchMapping("pwd/{cardId}")
    public JsonResult<Object> pwd(
            @PathVariable("cardId") String cardId,
            @Valid @RequestBody PwdDto contact
    ) {
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, true);
        String pwd = new String(Base64.getDecoder().decode(contact.getPwd()));
        PwdResDto pwdResDto = cardService.setBankcardPin(cardId, pwd);
        return JsonResult.ok(pwdResDto);
    }

    @DeleteMapping("{cardId}")
    public JsonResult<Object> cancel(@PathVariable("cardId") String cardId) {
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, true);
        UserCardWithFee userCardWithFee = cardService.cancelCard(cardId);
        CancelCardResDto cancelCardResDto = new CancelCardResDto();
        BeanUtils.copyProperties(userCardWithFee, cancelCardResDto);
        return JsonResult.ok(cancelCardResDto);
    }


    @GetMapping("{cardId}/log")
    public JsonResult<Object> getTradeLog(
            @PathVariable("cardId") String cardId,
            @Valid @ModelAttribute PageReqDto pageReqDto
    ) {
        User user = (User) request.getAttribute("user");
        cardId = cardService.checkBizCard(user, cardId, false);

        IPage<UserCardLog> cardLogList = userCardLogService.cardLogPage(cardId, pageReqDto.getPage(), pageReqDto.getSize());
        List<CardLogResDto> cardLogDtoList = new ArrayList<>();
        for (UserCardLog cardLog : cardLogList.getRecords()) {
            CardLogResDto cardLogDto = new CardLogResDto();
            BeanUtils.copyProperties(cardLog, cardLogDto);
            cardLogDtoList.add(cardLogDto);
        }

        HashMap<String, Object> res = new HashMap<>(2);
        res.put("list", cardLogDtoList);
        res.put("total", cardLogList.getTotal());
        return JsonResult.ok(res);
    }

    @GetMapping("log/{orderId}")
    public JsonResult<Object> getTradeLogDetail(@PathVariable("orderId") String orderId) {
        User user = (User) request.getAttribute("user");

        List<UserCardLog> cardLogList = userCardLogService.cardLogDetail(orderId);

        cardService.checkBizCard(user, cardLogList.getFirst().getCardId(), false);

        List<CardLogResDto> cardLogDtoList = new ArrayList<>();
        for (UserCardLog cardLog : cardLogList) {
            CardLogResDto cardLogDto = new CardLogResDto();
            BeanUtils.copyProperties(cardLog, cardLogDto);
            cardLogDtoList.add(cardLogDto);
        }

        return JsonResult.ok(cardLogDtoList);
    }


    private CardManageResDto formatCardManageWithFee(CardManageWithFeeVo cardManageWithFee) {
        CardManageResDto cardManageRes = new CardManageResDto();
        BeanUtils.copyProperties(cardManageWithFee, cardManageRes);
        List<String> cardScene = new ArrayList<>();
        if (cardManageWithFee.getCardChangjing() != null) {
            String[] cardSceneList = cardManageWithFee.getCardChangjing().split(",");
            cardScene = Arrays.stream(cardSceneList).map(String::trim).collect(Collectors.toList());
            cardManageRes.setCardChangjing(cardScene);
        }
        cardManageRes.setCardChangjing(cardScene);
        cardManageRes.setIsAble(cardManageWithFee.getIsAble().equals(1));
        cardManageRes.setPlatformChargeRate(cardManageWithFee.getPlatformChargeRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        cardManageRes.setBusinessChargeRate(cardManageWithFee.getBusinessChargeRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        Integer alreadyOpenedCardNum = cardManageService.getBusinessCardManageOpenCards(cardManageWithFee.getBusinessId(), cardManageWithFee.getCardManageId());
        cardManageRes.setAlreadyOpenedCardNum(alreadyOpenedCardNum);
        return cardManageRes;
    }

    @PatchMapping("/holder")
    public JsonResult<Object> bindCardUser(@RequestBody MailingAddress dto) {
        User user = (User) request.getAttribute("user");
        if (dto.getCardId().isEmpty() || dto.getHolderId() == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        cardService.checkBizCard(user, dto.getCardId(), false);
        cardService.bindCardUser(dto);
        return JsonResult.ok();
    }
}
