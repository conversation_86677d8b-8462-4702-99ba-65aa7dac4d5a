package com.qf.zpay.controller.open.v2;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.res.v2.CardDto;
import com.qf.zpay.dto.res.v2.JournalAccountDto;
import com.qf.zpay.response.JsonResult;
import generator.domain.BusinessDayCardDisburseLog;
import generator.domain.BusinessDayCardEarnLog;
import generator.domain.BusinessDayPaymentFlowLog;
import generator.domain.User;
import generator.service.BusinessDayCardDisburseLogService;
import generator.service.BusinessDayCardEarnLogService;
import generator.service.BusinessDayPaymentFlowLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@RestController
@RequestMapping("/v2/stat")
@Slf4j
public class StatController {

    @Autowired
    private BusinessDayCardEarnLogService businessDayCardEarnLogService;
    @Autowired
    private BusinessDayCardDisburseLogService businessDayCardDisburseLogService;
    @Autowired
    private BusinessDayPaymentFlowLogService businessDayPaymentFlowLogService;

    /**
     * 收益列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/card")
    public JsonResult<Object> journalAccountList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        CardDto result = new CardDto();
        User user = (User) request.getAttribute("user");
        IPage<BusinessDayCardEarnLog> earnLogIPage = businessDayCardEarnLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayCardEarnLog>()
                .eq(BusinessDayCardEarnLog::getBusinessId, user.getBusinessId())
                .eq(!StringUtils.isEmpty(dto.getType()), BusinessDayCardEarnLog::getCardModel, dto.getType())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayCardEarnLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayCardEarnLog::getStatDate));
        IPage<BusinessDayCardDisburseLog> disburseLogIPage = businessDayCardDisburseLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayCardDisburseLog>()
                .eq(BusinessDayCardDisburseLog::getBusinessId, user.getBusinessId())
                .eq(!StringUtils.isEmpty(dto.getType()), BusinessDayCardDisburseLog::getCardModel, dto.getType())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayCardDisburseLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayCardDisburseLog::getStatDate));
        result.setBusinessDayCardEarnLog(earnLogIPage);
        result.setBusinessDayCardDisburseLog(disburseLogIPage);
        return JsonResult.ok(result);
    }

    /**
     * 代付流水列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/payment")
    public JsonResult<Object> paymentPayOrderList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        IPage<BusinessDayPaymentFlowLog> page = businessDayPaymentFlowLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayPaymentFlowLog>()
                .eq(BusinessDayPaymentFlowLog::getBusinessId, user.getBusinessId())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayPaymentFlowLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayPaymentFlowLog::getStatDate));
        return JsonResult.ok(page);
    }

}
