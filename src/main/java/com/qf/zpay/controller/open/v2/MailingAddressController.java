package com.qf.zpay.controller.open.v2;

import com.qf.zpay.response.JsonResult;
import generator.domain.MailingAddress;
import generator.service.MailingAddressService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RestController
@RequestMapping("/v2/address")
public class MailingAddressController {

    @Autowired
    MailingAddressService service;

    @GetMapping("/{cardId}")
    public JsonResult<Object> getMailingAddress(@PathVariable("cardId") String cardId){
        MailingAddress mailingAddress = service.getMailingAddress(cardId);
        return JsonResult.ok(mailingAddress);
    }

}
