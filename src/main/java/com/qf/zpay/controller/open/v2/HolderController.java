package com.qf.zpay.controller.open.v2;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.dto.req.CardHolderRequestDto;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.ImageService;
import com.qf.zpay.util.DateUtil;
import generator.domain.CardHolder;
import generator.domain.CountryCode;
import generator.domain.User;
import generator.service.CardHolderService;
import generator.service.CountryCodeService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/v2/holder")
@Validated
@Slf4j
public class HolderController {
    @Autowired
    HttpServletRequest request;
    @Autowired
    private CardHolderService cardHolderService;
    @Autowired
    private CountryCodeService countryCodeService;

    /**
     * 查询kyc国籍编码
     *
     * @param country 国籍
     * @return list
     */
    @GetMapping()
    public JsonResult<Object> country(@RequestParam("country") String country) {
        List<CountryCode> list = countryCodeService.country(country);
        return JsonResult.ok(list);
    }

    /**
     * 用卡人分页数据
     *
     * @param page     当前页
     * @param pageSize 展示条数
     * @return 分页数据
     */
    @GetMapping("/{page}/{pageSize}")
    public JsonResult<Object> chargeRecord(@PathVariable("page") Integer page, @PathVariable("pageSize") Integer pageSize) {
        User user = (User) request.getAttribute("user");
        CardHolderRequestDto dto = new CardHolderRequestDto();
        dto.setBusinessId(user.getBusinessId());
        dto.setPageSize(pageSize);
        dto.setPage(page);
        IPage<CardHolder> pag = cardHolderService.pageList(dto);
        return JsonResult.ok(pag);
    }

    /**
     * 用户kyc
     *
     * @param userData userData 用户kyc数据
     * @param request  获取请求对象
     * @return 用户kyc信息
     */
    @PostMapping()
    public JsonResult<Object> setUserData(@Validated @RequestBody CardHolder userData, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        CardHolder add = cardHolderService.add(userData, user2);

        return JsonResult.ok(add);
    }

    /**
     * 查询用卡人
     *
     * @param id 用卡人id
     * @return CardHolder用卡人信息
     */
    @GetMapping("/{holderId}")
    public JsonResult<Object> getHolder(@PathVariable("holderId") Integer id) {
        CardHolder byId = cardHolderService.getById(id);
        return JsonResult.ok(byId);
    }


    @Autowired
    private ImageService imageService;

    /**
     * kyc修改
     *
     * @param userData 修改数据;
     * @return ;
     */
    @PatchMapping("/{holderId}")
    public JsonResult<Object> updateKyc(
            @PathVariable("holderId") Integer id,
            @Validated @RequestBody CardHolder userData
    ) {
        User user = (User) request.getAttribute("user");
        CardHolder cardHolder = cardHolderService.getById(id);
        // 获取 CardHolder 类的所有字段
        for (Field field : CardHolder.class.getDeclaredFields()) {
            field.setAccessible(true);
            if (!field.equals("id")) {
                try {
                    Object value = field.get(userData);
                    if (value != null) {
                        field.set(cardHolder, value);
                    }
                } catch (IllegalAccessException e) {
                    // 处理访问异常
                }
            }
        }
        cardHolder.setUpdateTime(new Date());
        cardHolder.setStatus(0);
        cardHolder.setRealNameStatus(3);
        cardHolderService.updateById(cardHolder);
        // 抓取图片写入本地服务
        imageService.saveImageFromUrl(cardHolder);
        return JsonResult.ok(cardHolder);
    }
}
