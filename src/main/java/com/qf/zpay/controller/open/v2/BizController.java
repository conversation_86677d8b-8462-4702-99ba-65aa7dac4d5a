package com.qf.zpay.controller.open.v2;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONObject;
import com.qf.zpay.dto.common.TokenDto;
import com.qf.zpay.dto.req.Code2TokenDto;
import com.qf.zpay.dto.res.v2.WalletDto;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.TokenService;
import generator.domain.User;
import generator.domain.UserWallet;
import generator.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@RestController
@RequestMapping("/v2/biz")
@Slf4j
public class BizController {

    @Autowired
    HttpServletRequest request;

    @Autowired
    UserService userService;

    @Autowired
    RsaService rsaService;

    @Autowired
    TokenService tokenService;


    /**
     * 商户段换取 Token
     */
    @PostMapping("code2token")
    public JsonResult<Object> code2token(@Valid @RequestBody Code2TokenDto code2TokenDto) {
        User user = userService.code2user(code2TokenDto);
        TokenDto token = tokenService.genAuthToken(user);
        return JsonResult.ok(token);
    }

    /**
     * 刷新 Token
     */
    @PostMapping("refresh-token")
    public JsonResult<Object> refreshToken(@RequestBody JSONObject body) {
        TokenDto token = tokenService.refreshAuthToken(body.getStr("refreshToken"));
        return JsonResult.ok(token);
    }


    /**
     * 获取商户钱包
     */
    @GetMapping("wallet")
    public JsonResult<Object> balance() {
        User user = (User) request.getAttribute("user");
        UserWallet userWallet = userService.getWallet(user.getBusinessId());
        WalletDto walletDto = new WalletDto();
        BeanUtils.copyProperties(userWallet, walletDto);
        return JsonResult.ok(walletDto);
    }


}
