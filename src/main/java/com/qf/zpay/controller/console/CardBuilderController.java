package com.qf.zpay.controller.console;


import cn.hutool.json.JSONObject;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.dto.req.console.CancelCardReqDto;
import com.qf.zpay.dto.req.console.ChangeStatusReqDto;
import com.qf.zpay.dto.req.v2.CardRechargeReqDto;
import com.qf.zpay.dto.req.v2.OpenCardDto;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.CardService;
import generator.domain.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * <AUTHOR>
 * @updateTime 2024/5/31 10:28
 */
@RestController
@RequestMapping("/console/CardBuilder")
@Slf4j
public class CardBuilderController {

    @Autowired
    HttpServletRequest request;

    @Autowired
    CardService cardService;

    @PostMapping("open")
    public JsonResult<Object> open(@Valid @RequestBody OpenCardDto openCardDto) {
        User user = (User) request.getAttribute("user");
        cardService.openCard(user, openCardDto.getCardManageId(), openCardDto.getAmount());
        return JsonResult.ok();
    }

    @PostMapping("recharge")
    public JsonResult<Object> recharge(@Valid @RequestBody CardRechargeReqDto rechargeDto) {
        User user = (User) request.getAttribute("user");
        cardService.checkBizCard(user, rechargeDto.getCardId(), true);
        cardService.recharge(user, rechargeDto.getCardId(), rechargeDto.getAmount());
        return JsonResult.ok();
    }

    @PostMapping("status")
    public JsonResult<Object> changeStatus(@Valid @RequestBody ChangeStatusReqDto body) {
        if (!CardStatusActionEnum.Freeze.getCode().equals(body.getAction())) {
            return JsonResult.ko(ResultCode.CARD_STATUS_SET_JUST_FREEZE);
        }
        User user = (User) request.getAttribute("user");
        cardService.checkBizCard(user, body.getCardId(), true);
        cardService.changeStatus(body.getCardId(), CardStatusActionEnum.Freeze);
        return JsonResult.ok();
    }


    @PostMapping("cancel")
    public JsonResult<Object> cancel(@Valid @RequestBody CancelCardReqDto body) {
        User user = (User) request.getAttribute("user");
        cardService.checkBizCard(user, body.getCardId(), true);
        cardService.cancelCard(body.getCardId());
        return JsonResult.ok();
    }


    /**
     * GSalary
     * 添加持卡人
     *
     * @param map 持卡人数据
     */
    @PostMapping("holder")
    public JsonResult<Object> holder(@RequestBody Map<String, Object> map) {
        JSONObject entries = cardService.savaCardHolders(map);
        return JsonResult.ok(entries);
    }


}
