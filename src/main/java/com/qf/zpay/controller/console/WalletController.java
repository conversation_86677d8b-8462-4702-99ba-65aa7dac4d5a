package com.qf.zpay.controller.console;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.CardHelper;
import com.qf.zpay.service.CardService;
import com.qf.zpay.service.ImageService;
import generator.domain.*;
import generator.service.*;
import com.qf.zpay.response.JsonResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @updateTime 2024/6/14 11:32
 */
@RestController
@Validated
@Slf4j
@RequestMapping("/console/WalletManagement")
public class WalletController {
    @Autowired
    private CardHolderService cardHolderService;
    @Autowired
    private UserWalletService userWalletService;
    @Autowired
    private UserAddressService userAddressService;
    @Autowired
    private UserWalletLogService userWalletLogService;
    @Autowired
    HttpServletRequest request;
    @Autowired
    private CountryCodeService countryCodeService;
    @Autowired
    private CardHelper cardHelper;
    @Autowired
    private ImageService imageService;

    /**
     * 用户钱包详情
     *
     * @param request 获取请求对象
     * @return UserWallet钱包详情
     */
    @GetMapping("/userWalletDetails")
    public JsonResult<Object> userWalletDetails(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        UserWallet userWallet = userWalletService.getByBusinessId(user2.getBusinessId());
        return JsonResult.ok(userWallet);
    }

    /**
     * 用户钱包划转详情
     *
     * @param cardSettingsDto 筛选条件
     * @param request         获取请求对象
     * @return 钱包划转详情分页
     */
    @PostMapping("/userWalletTransferDetails")
    public JsonResult<Object> userWalletTransferDetails(@RequestBody CardSettingsDto cardSettingsDto, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<UserWalletLog> userWallet = userWalletLogService.userWalletTransferDetails(cardSettingsDto);
        return JsonResult.ok(userWallet);
    }

    /**
     * 用户钱包导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @PostMapping("/exportUserWalletTransferDetails")
    public void exportUserWalletTransferDetails(@RequestBody CardSettingsDto cardSettingsDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        userWalletLogService.exportUserWalletTransferDetails(cardSettingsDto, response, request);
    }

    /**
     * 用户划转入口
     *
     * @param userWalletTransferDto 划转请求数据dto
     * @param request               获取请求对象
     * @return true
     */
    @PostMapping("/userWalletTransfer")
    public JsonResult<Object> userWalletTransfer(@Valid @RequestBody UserWalletTransferDto userWalletTransferDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        userWalletService.userWalletTransfer(userWalletTransferDto, request);

        // 如果划转到共享卡钱包，触发商户伞下共享卡余额变化
        if (userWalletTransferDto.getTargetAccount().equals(WalletTypeConstants.STWALLET_TYPE_SHARE)) {
            cardHelper.changeBusinessShareCardAmount(user.getBusinessId(), userWalletTransferDto.getAmount(), null);
        } else if (userWalletTransferDto.getAccount().equals(WalletTypeConstants.STWALLET_TYPE_SHARE)) {
            cardHelper.changeBusinessShareCardAmount(user.getBusinessId(), userWalletTransferDto.getAmount().negate(), null);
        }
        return JsonResult.ok();
    }

    /**
     * 获取用户充值地址
     *
     * @param request 获取请求对象
     * @return topUpDto 用户信息
     */
    @GetMapping("/topUp")
    public JsonResult<Object> topUp(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        UserWallet byBusinessId = userWalletService.getByBusinessId(user2.getBusinessId());
        UserAddress byId = userAddressService.getBusinessAddress(user2.getId());
        TopUpDto topUpDto = new TopUpDto();
        topUpDto.setUserAddress(byId);
        topUpDto.setUserWallet(byBusinessId);
        topUpDto.setWithdrawRate(user2.getWithdrawRate());
        topUpDto.setRechargeRate(user2.getRechargeRate());
        return JsonResult.ok(topUpDto);
    }

    /**
     * 用户提现接口
     *
     * @param withdrawDto 提现数据dto
     * @param request     获取请求对象
     * @return true
     */
    @PostMapping("/withdraw")
    public JsonResult<Object> withdraw(@RequestBody WithdrawDto withdrawDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        withdrawDto.setBusinessId(user.getBusinessId());
        withdrawDto.setUserId(user.getId());
        userWalletService.withdraw(withdrawDto);
        return JsonResult.ok();
    }

    /**
     * 充提记录
     *
     * @param cardSettingsDto 筛选条件
     * @param request         获取请求对象
     * @return 充提记录分页数据
     */
    @PostMapping("/chargeRecord")
    public JsonResult<Object> chargeRecord(@RequestBody CardSettingsDto cardSettingsDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user.getBusinessId());
        IPage<CoinInOut> page = userWalletService.getCardTransactionDetails(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 获取国籍代码
     *
     * @param country
     * @return list
     */
    @GetMapping()
    public JsonResult<Object> country(@RequestParam("country") String country) {
        List<CountryCode> list = countryCodeService.country(country);
        return JsonResult.ok(list);
    }

    /**
     * kyc设置入口
     *
     * @param userData 用户kyc数据
     * @return 用户kyc
     */
    @PostMapping("/cardKycSettings")
    public JsonResult<Object> setUserData(@Validated @RequestBody CardHolder userData, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        CardHolder add = cardHolderService.add(userData, user2);
        return JsonResult.ok(add);
    }

    /**
     * 上传图片入口
     */
    @PostMapping("/uploadImage")
    public JsonResult<Object> uploadImage(@RequestParam("image") MultipartFile file) {
        return imageService.uploadImage(file);
    }

    /**
     * 删除图片入口
     *
     * @param imageUrl 图片地址
     * @return true
     * @throws IOException ；
     */
    @DeleteMapping("/deleteImage")
    public JsonResult<Object> deleteImage(@RequestParam("imageUrl") String imageUrl) throws IOException {
        return imageService.deleteImage(imageUrl);
    }

    /**
     * 修改kyc设置
     *
     * @param userData 修改数据
     * @return true
     */
    @PutMapping("/updateKyc")
    public JsonResult<Object> updateKyc(@RequestBody CardHolder userData) {
        userData.setUpdateTime(new Date());
        cardHolderService.updateById(userData);
        return JsonResult.ok();
    }

    /**
     * 用卡人列表
     *
     * @param dto     筛选条件
     * @param request 获取请求对象
     * @return 用卡人分页列表
     */
    @PostMapping("/page")
    public JsonResult<Object> chargeRecord(@RequestBody CardHolderRequestDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        dto.setBusinessId(user.getBusinessId());
        IPage<CardHolder> page = cardHolderService.pageList(dto);
        return JsonResult.ok(page);
    }
}
