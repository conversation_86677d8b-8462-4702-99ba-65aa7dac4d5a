package com.qf.zpay.controller.console;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Console;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.CardService;
import com.qf.zpay.service.GlobalcashService;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.card.CardFactory;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @updateTime 2024/6/4 17:26
 */
@Controller("/")
@Slf4j
public class IndexController {

    @Autowired
    private RsaService rsaService;

    @Autowired
    RedissonClient redisson;


//    @Qualifier("CenterRedis")
//    @Autowired
//    RedissonClient centerRedis;

    @Autowired
    private GlobalcashService globalcashService;

    @Autowired
    CardFactory cardFactory;

    @Autowired
    CardService cardService;


    @GetMapping()
    @ResponseBody
    public JsonResult<Object> index() {
        return JsonResult.ok();
    }


    @GetMapping("test")
    @ResponseBody
    public JsonResult<Object> test() {
        return JsonResult.ok();
    }


    @PostMapping("test-rsa/{uid}")
    @ResponseBody
    public JsonResult<Object> testRsa(
            @PathVariable("uid") Integer uid,
            @RequestBody String body
    ) {
        Console.error("ID: {} | data: {} ", uid, body);
        JSONObject data = JSONUtil.parseObj(body);

        RMap<String, String> rsaMap = rsaService.getUserRsaPair(uid);
        RSA rsa = new RSA(rsaMap.get("private"), rsaMap.get("public"));

        data.set("timestamp", System.currentTimeMillis());
//        data.set("cardId","2024090205460438411000105702");
//        data.set("amount", 100);

        byte[] encrypt = rsa.encrypt(data.toString(), KeyType.PublicKey);
        String encryptStr = Base64.encode(encrypt);

        return JsonResult.ok(encryptStr);
    }


}
