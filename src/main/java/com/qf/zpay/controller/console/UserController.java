package com.qf.zpay.controller.console;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import generator.domain.*;
import generator.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 10:27
 */
@RestController
@RequestMapping("/console/user")
@Slf4j
public class UserController {

    @Autowired
    HttpServletRequest request;

    @Autowired
    private UserService userService;

    @Autowired
    private CustomerServiceInfoService customerServiceInfoService;

    @Autowired
    private UserWalletLogService userWalletLogService;

    @Autowired
    private BusinessDayFeeLogService businessDayFeeLogService;

    @Autowired
    private UserCardFeeConfigService userCardFeeConfigService;

    @Autowired
    private UserDevConfigService userDevConfigService;

    @GetMapping("balance")
    public JsonResult<Object> balance(@ModelAttribute("user") User user, HttpServletRequest request) {
        log.error("user: {}", user);
        // 另一种取用户信息的方式
        User user2 = (User) request.getAttribute("user");
        log.error("user2: {}", request);
        return JsonResult.ok();
    }

    /**
     * 是否有二级密码
     *
     * @param request 获取请求对象
     * @return true or false
     */
    @GetMapping("/verify")
    public JsonResult<Object> secondaryPassword(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        if (Objects.isNull(user2.getPayPassword())) {
            return JsonResult.ok(Boolean.FALSE);
        }
        return JsonResult.ok(Boolean.TRUE);
    }

    /**
     * 验证二级密码
     *
     * @param password 二级密码
     * @param request  请求对象
     * @return true or false
     */
    @GetMapping("/secondaryPassword")
    public JsonResult<Object> verify(@RequestParam(name = "password") String password, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        Boolean verify = userService.verify(user2.getId(), password);
        return JsonResult.ok(verify);
    }

    /**
     * 修改密码
     *
     * @param userPasswordDto 密码数据dto
     * @param request         请求对象
     * @return true
     */
    @PostMapping("/changePassword")
    public JsonResult<Object> changePassword(@RequestBody UserPasswordDto userPasswordDto, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        Boolean verify = userService.changePassword(user2, userPasswordDto);
        return JsonResult.ok(verify);
    }

    /**
     * 修改二级密码
     *
     * @param userPasswordDto 密码数据dto
     * @param request         请求对象
     * @return true
     */
    @PostMapping("/changeSecondaryPassword")
    public JsonResult<Object> changeSecondaryPassword(@RequestBody UserPasswordDto userPasswordDto, HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        Boolean verify = userService.changeSecondaryPassword(user2, userPasswordDto);
        return JsonResult.ok(verify);
    }

    /**
     * 商户详情接口
     *
     * @param request 请求对象
     * @return 商户详情
     */
    @GetMapping("/userInfo")
    public JsonResult<Object> userInfo(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        UserInfoDto userInfoDto = userService.userInfo(user2);
        userInfoDto.setLastLoginTime(user2.getLastLoginTime());
        return JsonResult.ok(userInfoDto);
    }

    /**
     * 下载公钥入口
     *
     * @param request  请求对象
     * @param response 响应
     * @return JsonResult
     */
    @GetMapping("/writePemFile")
    public JsonResult<Object> writePemFile(HttpServletRequest request, HttpServletResponse response) {
        User user2 = (User) request.getAttribute("user");
        userService.writePemFile(user2, response);
        return JsonResult.ok();
    }


    /**
     * 获取商户客服
     *
     * @param request 请求对象
     * @return 商户客服详情
     */
    @GetMapping("/customerService")
    public JsonResult<Object> customerService(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        // 判断是否分配客户
        if (Objects.isNull(user2.getBusinessNo())) {
            throw new ApiException(ResultCode.NOT_FIND);
        }
        CustomerServiceInfo byId = customerServiceInfoService.getById(user2.getBusinessNo());
        return JsonResult.ok(byId);
    }


    /**
     * 仪表盘1
     * 资金变化
     *
     * @param request 请求对象
     * @return Map
     * @throws ParseException ；
     */
    @GetMapping("/changeOfFunds")
    public JsonResult<Object> changeOfFunds(HttpServletRequest request) throws ParseException {
        User user2 = (User) request.getAttribute("user");
        List<Map.Entry<String, List<UserWalletLog>>> dateListMap = userWalletLogService.changeOfFunds(user2);
        return JsonResult.ok(dateListMap);
    }

    /**
     * 交易笔数仪表盘
     *
     * @param request 请求对象
     * @return HashMap；
     */
    @GetMapping("/numberOfTransactions")
    public JsonResult<Object> numberOfTransactions(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        HashMap<Integer, List<NumberOfTransactionsDto>> integerListHashMap = userWalletLogService.numberOfTransactions(user2);
        return JsonResult.ok(integerListHashMap);
    }


    /**
     * 资金流水
     *
     * @param request 请求对象
     * @return 流水数据
     */
    @GetMapping("/flowOfFunds")
    public JsonResult<Object> flowOfFunds(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        List<BusinessDayFeeLog> list = businessDayFeeLogService.flowOfFunds(user.getBusinessId());
        return JsonResult.ok(list);
    }


    /**
     * 剩余可用卡数量
     *
     * @return list；
     */
    @GetMapping("/numberOfAvailableCards")
    public JsonResult<Object> numberOfAvailableCards() {
        User user = (User) request.getAttribute("user");
        List<UserCardFeeConfigDto> list = userCardFeeConfigService.numberOfAvailableCards(user.getBusinessId());
        return JsonResult.ok(list);
    }


    /**
     * 获取商户开通的菜单
     *
     * @return list；
     */
    @GetMapping("/findUserDevConfig")
    public JsonResult<Object> findUserDevConfig() {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(userDevConfigService.getOne(new LambdaQueryWrapper<UserDevConfig>().eq(UserDevConfig::getBusinessId, user.getBusinessId())));
    }


}
