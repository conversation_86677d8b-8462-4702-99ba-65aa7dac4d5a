package com.qf.zpay.controller.console;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.res.v2.JournalAccountDto;
import com.qf.zpay.dto.res.v2.QuoteDto;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.PaymentService;
import generator.domain.*;
import generator.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

@Log4j2
@Validated
@RestController
@RequestMapping("/console/payment")
public class PaymentPayController {


    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Autowired
    private PaymentPayeeService paymentPayeeService;
    @Autowired
    private PaymentPayOrderService paymentPayOrderService;
    @Autowired
    HttpServletRequest request;
    @Autowired
    private PaymentPayConfigService paymentPayConfigService;
    @Autowired
    private PaymentService paymentService;
    @Resource
    private PaymentUserConfigService paymentUserConfigService;
    @Autowired
    private PaymentChannelService paymentChannelService;

    @Autowired
    private BusinessDayCardEarnLogService businessDayCardEarnLogService;

    @Autowired
    private BusinessDayCardDisburseLogService businessDayCardDisburseLogService;
    @Autowired
    private BusinessDayPaymentFlowLogService businessDayPaymentFlowLogService;
    @Autowired
    private UserWalletAdvanceLogService userWalletAdvanceLogService;

    @Autowired
    PaymentPayeeAccountService paymentPayeeAccountService;

    /**
     * 查询收款人账户
     *
     * @return list
     */
    @GetMapping("/accountList/{payeeId}")
    public JsonResult<Object> accountList(@PathVariable String payeeId) {
        User user = (User) request.getAttribute("user");
        List<PaymentPayeeAccount> list = paymentPayeeAccountService.accountList(user, payeeId);
        return JsonResult.ok(list);
    }

    /**
     * 查询地区二字码
     *
     * @return list
     */
    @GetMapping("/getCountryRegion")
    public JsonResult<Object> getCountryRegion(@RequestParam("country") String country) {
        List<PaymentPayConfig> list = paymentService.getCountryRegion(country);
        return JsonResult.ok(list);
    }

    /**
     * 查询可用付款方式
     */
    @GetMapping("/availablePaymentMethods/{country}")
    public JsonResult<Object> availablePaymentMethods(@PathVariable("country") String country) {
        List<JSONObject> jsonObjects = paymentService.availablePaymentMethods(country);
        return JsonResult.ok(jsonObjects);
    }

    /**
     * 添加收款人
     *
     * @param payeeRequest 收款人信息
     * @return ；
     */
    @PostMapping("/savePayee")
    public JsonResult<Object> savePayee(@Valid @RequestBody PaymentPayeeDto payeeRequest) {
        User user2 = (User) request.getAttribute("user");
        payeeRequest.setBusinessId(user2.getBusinessId());
        paymentService.addPayee(payeeRequest);
        return JsonResult.ok();

    }

    /**
     * 更新收款人
     *
     * @return true；
     */
    @PutMapping("/renewalPayee")
    public JsonResult<Object> renewalPayee(@RequestBody PaymentPayee paymentPayee) {
        paymentService.updatePayee(paymentPayee);
        return JsonResult.ok();

    }

    /**
     * 删除收款人
     *
     * @param id 收款人id
     * @return true
     */
    @DeleteMapping("/deletePayee")
    public JsonResult<Object> deletePayee(@RequestParam(name = "id") Integer id) {
        paymentPayeeService.deletePayee(id);
        return JsonResult.ok();
    }

    /**
     * 删除收款人账户
     *
     * @return true
     */
    @DeleteMapping("/deleteAccount")
    public JsonResult<Object> deleteAccount(@RequestParam(name = "accountId") String accountId) {
        paymentService.deleteAccount(accountId);
        return JsonResult.ok();
    }

    /**
     * 添加收款人账户
     *
     * @param dto 账户信息
     * @return JSONObject
     */
    @PostMapping("/saveAccount")
    public JsonResult<Object> saveAccount(@Valid @RequestBody PaymentPayeeAccount dto) {
        User user2 = (User) request.getAttribute("user");
        PaymentPayeeAccount payeeAccount = paymentService.saveAccount(dto, user2);
        return JsonResult.ok(payeeAccount);
    }


    /**
     * 付款订单
     *
     * @return 订单
     */
    @PostMapping("/paymentOrder")
    public JsonResult<Object> paymentOrder(@RequestParam("orderNo") @NotBlank(message = "{Valid.orderId}") String orderNo) {
        User user2 = (User) request.getAttribute("user");
        PaymentPayOrder paymentPayOrder = null;
        if ("prod".equals(activeProfile)) {
            paymentPayOrder = paymentService.paymentOrder(orderNo, user2.getId());
        } else {
            paymentPayOrder = paymentService.paymentOrderV2(orderNo, user2.getId());
        }
        return JsonResult.ok(paymentPayOrder);

    }


    /**
     * 付款订单（模拟付款）
     *
     * @return 订单
     */
    @PostMapping("/paymentOrderTest")
    public JsonResult<Object> paymentOrderTest(@RequestParam("orderNo") @NotBlank(message = "{Valid.orderId}") String orderNo) {
        User user2 = (User) request.getAttribute("user");
        PaymentPayOrder paymentPayOrder = paymentService.paymentOrderV2(orderNo, user2.getId());
        return JsonResult.ok(paymentPayOrder);

    }


    /**
     * 查询收款人
     *
     * @param lastName 姓
     * @return 收款人列表
     */
    @GetMapping("/getPayee")
    public JsonResult<Object> getPayee(@RequestParam(name = "lastName", required = false) String lastName, @RequestParam(name = "phone", required = false) String phone, @RequestParam(name = "accountType", required = false) String accountType) {
        User user2 = (User) request.getAttribute("user");
        return JsonResult.ok(paymentPayeeService.getPayee(user2.getBusinessId(), lastName, phone, accountType));
    }


    /**
     * 获取付款人
     */
    @GetMapping("/getPayment")
    public JsonResult<Object> getPayment() {
        JSONObject payment = paymentService.getPayment();
        return JsonResult.ok(payment);
    }

    /**
     * 查询上游付款数据
     *
     * @param upstreamOrderId 订单id
     * @return JSONObject
     */
    @GetMapping("/modifyOrderStatus")
    public JsonResult<Object> modifyOrderStatus(@RequestParam("upstreamOrderId") String upstreamOrderId) {
        JSONObject entries = paymentService.modifyOrderStatus(upstreamOrderId);
        return JsonResult.ok(entries);
    }

    /**
     * 订单分页
     *
     * @param payOrderDto 筛选条件
     * @return 订单分页数据
     */
    @PostMapping("/payeePage")
    public JsonResult<Object> payeePage(@RequestBody PayOrderDto payOrderDto) {
        User user2 = (User) request.getAttribute("user");
        payOrderDto.setBusinessId(user2.getBusinessId());
        IPage<PaymentPayOrderDto> page = paymentPayOrderService.payeePage(payOrderDto);
        return JsonResult.ok(page);
    }

    /**
     * 导出
     *
     * @param payOrderDto 请求条件
     * @param response    返回
     * @throws IOException 异常
     */
    @PostMapping("/export")
    public void export(@RequestBody PayOrderDto payOrderDto, HttpServletResponse response) throws IOException {
        User user2 = (User) request.getAttribute("user");
        payOrderDto.setBusinessId(user2.getBusinessId());
        Locale locale = request.getLocale(); // 获取用户的语言环境
        paymentPayOrderService.export(payOrderDto, user2, response, locale);
    }

    /**
     * 查询汇率
     *
     * @param payeeId 收款人id
     * @return JSONObject
     */
    @GetMapping("/currentExchangeRate")
    public JsonResult<Object> currentExchangeRate(@RequestParam("id") Integer payeeId) {
        PaymentPayee byId = paymentPayeeService.getById(payeeId);
        JSONObject entries = paymentService.currentExchangeRate(byId.getCurrency());
        return JsonResult.ok(entries);
    }
    //=====================================================================================================================

    /**
     * 查询钱包类型填写字段
     *
     * @param product 钱包类型
     * @return ；
     */
    @GetMapping("/productType/{product}")
    public JsonResult<Object> getProductType(@PathVariable("product") String product) {
        HashMap<String, String> productType = paymentPayConfigService.getProductType(product);
        return JsonResult.ok(productType);
    }

    /**
     * 查询可用付款方式
     */
    @GetMapping("/paymentMethods")
    public JsonResult<Object> availablePaymentMethods(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(paymentService.availablePaymentMethods3(user.getBusinessId()));
    }

    /**
     * 电子钱包锁汇
     *
     * @param dto 锁汇数据
     * @return 订单
     */
    @PostMapping("/applyQuote")
    public JsonResult<Object> applyQuote(@Valid @RequestBody(required = false) ApplyQuoteDto dto) {
        User user = (User) request.getAttribute("user");
        QuoteDto paymentPayOrder = paymentService.applyQuote(dto, user);
        return JsonResult.ok(paymentPayOrder);
    }

    /**
     * 银行代付锁汇
     */
    @PostMapping("/bankingQuote")
    public JsonResult<Object> bankingQuote(@Valid @RequestBody PaymentBankingDto bankingDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        PaymentPayeeSaveDto dto = new PaymentPayeeSaveDto();
        AccountRegistryDto accountRegistryDto = bankingDto.getAccountRegistryDto();
        BeanUtils.copyProperties(bankingDto, dto);
        dto.setCurrency(accountRegistryDto.getCurrency());
        dto.setBusinessId(user.getBusinessId());
        String payeeId = paymentService.saveBankPayee(dto);
        ApplyQuoteDto quoteDto = new ApplyQuoteDto();

        accountRegistryDto.setPayeeId(payeeId);
        quoteDto.setAccountRegistryDto(accountRegistryDto);
        quoteDto.setAmount(bankingDto.getAmount());
        quoteDto.setAccount(bankingDto.getAccount());
        QuoteDto paymentPayOrder = paymentService.applyQuote(quoteDto, user);
        return JsonResult.ok(paymentPayOrder);
    }

    /**
     * 创建收款人（银行账户）
     */
    @PostMapping("/saveBankPayee")
    public JsonResult<Object> saveBankPayee(@Valid @RequestBody PaymentPayeeSaveDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        dto.setBusinessId(user.getBusinessId());
        return JsonResult.ok(paymentService.saveBankPayee(dto));
    }

    /**
     * 获取收款人账户表单
     */
    @GetMapping("/getAccountRegisterFormat")
    public JsonResult<Object> getAccountRegisterFormat(@RequestParam("payeeId") String payeeId, @RequestParam(value = "currency", required = false) String currency, @RequestParam(value = "language", required = false) String language) {
        return JsonResult.ok(paymentService.getAccountRegisterFormat(payeeId, currency, language));
    }

    /**
     * 查询支持付款国家(银行账户)
     */
    @GetMapping("/getPayoutCountries")
    public JsonResult<Object> getPayoutCountries() {
        return JsonResult.ok(paymentService.getPayoutCountries());
    }

    /**
     * 获取收款人的账户
     */
    @GetMapping("/getAccounts")
    public JsonResult<Object> getAccounts(@RequestParam("payeeId") String payeeId) {
        return JsonResult.ok(paymentService.getAccounts(payeeId));
    }

    /**
     * 注册收款人账户（银行账户）
     */
    @PostMapping("/saveAccountRegistry")
    public JsonResult<Object> saveAccountRegistry(HttpServletRequest request, @RequestBody(required = false) AccountRegistryDto dto) {
        User user = (User) request.getAttribute("user");
        dto.setBusinessId(user.getBusinessId());
        return JsonResult.ok(paymentService.saveAccountRegistry(dto));
    }

    @GetMapping("/countries")
    public JsonResult<Object> getCountries() {
        // 获取所有可用的国家/地区代码
        String[] countryCodes = Locale.getISOCountries();
        List<PaymentPayConfig> arrayList = new ArrayList<>();
        // 遍历所有国家/地区代码
        for (String countryCode : countryCodes) {
            // 创建Locale对象
            Locale locale = new Locale("", countryCode);
            // 获取国家名称
            String countryName = locale.getDisplayCountry();
            // 获取英文国家名称
            String countryName2 = locale.getDisplayCountry(Locale.ENGLISH);
            // 获取二字代码
            String twoLetterCode = locale.getCountry();
            // 获取币种代码
            Currency currency = null;
            String currencyCode = "N/A";
            try {
                currency = Currency.getInstance(locale);
                if (currency != null) {
                    currencyCode = currency.getCurrencyCode();
                }
            } catch (Exception e) {
                // 有些地区可能没有对应的币种
            }
            PaymentPayConfig paymentPayConfig = new PaymentPayConfig();
            paymentPayConfig.setCountryRegion(countryName);
            paymentPayConfig.setSettlementCurrency(currencyCode);
            paymentPayConfig.setTwoLetterCode(twoLetterCode);
            paymentPayConfig.setEnglishNameCountry(countryName2);
            arrayList.add(paymentPayConfig);
        }
        paymentPayConfigService.saveBatch(arrayList);
        return JsonResult.ok();
    }


    /**
     * 修改汇率
     *
     * @param dto 汇率信息
     * @return JSONObject
     */
    @PostMapping("/updateUserConfig")
    public JsonResult<Object> updateUserConfig(@Valid @RequestBody PaymentUserConfig dto) {
        PaymentUserConfig paymentUserConfig = paymentUserConfigService.updateUserConfig(dto);
        return JsonResult.ok(paymentUserConfig);
    }

    /**
     * 汇率列表
     *
     * @param
     * @return 分页数据
     */
    @GetMapping("/findExchangeRateByChannelId")
    public JsonResult<Object> findExchangeRateByChannelId(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        List<PaymentUserConfig> paymentUserConfigList = paymentUserConfigService.list(new LambdaQueryWrapper<PaymentUserConfig>()
                .eq(PaymentUserConfig::getBusinessId, user.getBusinessId()).orderByDesc(PaymentUserConfig::getStatus));
        for (PaymentUserConfig paymentUserConfig : paymentUserConfigList) {
            PaymentChannel paymentChannel = paymentChannelService.getById(paymentUserConfig.getChannelId());
            if (null != paymentChannel) {
                paymentUserConfig.setCode(paymentChannel.getCode());
                paymentUserConfig.setCountry(paymentChannel.getCountry());
                paymentUserConfig.setCurrency(paymentChannel.getCurrency());
            }
        }
        return JsonResult.ok(paymentUserConfigList);
    }


    /**
     * 渠道启用禁用
     *
     * @param
     * @return 渠道启用禁用
     */
    @PostMapping("/onOffChannel")
    public JsonResult<Object> onOffChannel(@Valid @RequestBody PaymentUserConfig paymentUserConfig) {
        return JsonResult.ok(paymentUserConfigService.updateById(paymentUserConfig));
    }


    /**
     * 收益列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/journalAccountList")
    public JsonResult<Object> journalAccountList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(businessDayCardEarnLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayCardEarnLog>()
                .eq(BusinessDayCardEarnLog::getBusinessId, user.getBusinessId())
                .eq(!StringUtils.isEmpty(dto.getType()), BusinessDayCardEarnLog::getCardModel, dto.getType())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayCardEarnLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayCardEarnLog::getStatDate)));
    }


    /**
     * 今日收益统计
     *
     * @param
     * @return 分页数据
     */
    @GetMapping("/journalAccountStatistics")
    public JsonResult<Object> journalAccountStatistics(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(businessDayCardEarnLogService.journalAccountStatistics(user.getBusinessId()));
    }

    /**
     * 支出列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/expendList")
    public JsonResult<Object> expendList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        IPage<BusinessDayCardDisburseLog> page = businessDayCardDisburseLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayCardDisburseLog>()
                .eq(BusinessDayCardDisburseLog::getBusinessId, user.getBusinessId())
                .eq(!StringUtils.isEmpty(dto.getType()), BusinessDayCardDisburseLog::getCardModel, dto.getType())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayCardDisburseLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayCardDisburseLog::getStatDate));
        return JsonResult.ok(page);
    }

    /**
     * 支出统计查询
     *
     * @param
     * @return 分页数据
     */
    @GetMapping("/expendStatistics")
    public JsonResult<Object> expendStatistics(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(businessDayCardDisburseLogService.expendStatistics(user.getBusinessId()));
    }

    /**
     * 代付流水列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/paymentPayOrderList")
    public JsonResult<Object> paymentPayOrderList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        IPage<BusinessDayPaymentFlowLog> page = businessDayPaymentFlowLogService.page(new Page<>(dto.getPageIndex(), dto.getPageSize()), new LambdaQueryWrapper<BusinessDayPaymentFlowLog>()
                .eq(BusinessDayPaymentFlowLog::getBusinessId, user.getBusinessId())
                .between(!StringUtils.isEmpty(dto.getStartTime()), BusinessDayPaymentFlowLog::getStatDate, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(BusinessDayPaymentFlowLog::getStatDate));
        return JsonResult.ok(page);
    }

    /**
     * 代付流水统计查询
     *
     * @param
     * @return 分页数据
     */
    @GetMapping("/paymentFlowStatistics")
    public JsonResult<Object> paymentFlowStatistics(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(paymentPayOrderService.paymentFlowStatistics(user.getBusinessId()));
    }


    /**
     * 代付流水列表查询
     *
     * @param
     * @return 分页数据
     */
    @PostMapping("/advanceOrderList")
    public JsonResult<Object> advanceOrderList(@RequestBody(required = false) JournalAccountDto dto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(userWalletAdvanceLogService.advancePage(dto, user.getBusinessId()));
    }

    /**
     * 垫付流水统计查询
     * 垫付张卡  总垫付金额
     *
     * @param
     * @return 分页数据
     */
    @GetMapping("/advanceFlowStatistics")
    public JsonResult<Object> advanceFlowStatistics(HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        return JsonResult.ok(userWalletAdvanceLogService.advanceFlowStatistics(user.getBusinessId()));
    }


}
