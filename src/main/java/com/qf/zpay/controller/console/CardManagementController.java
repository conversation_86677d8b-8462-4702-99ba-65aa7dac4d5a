package com.qf.zpay.controller.console;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.CardService;
import generator.domain.*;
import generator.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Validated
@RestController
@RequestMapping("/console/CardManagement")
public class CardManagementController {

    @Autowired
    private UserCardService userCardService;
    @Autowired
    private UserCardLogService userCardLogService;
    @Autowired
    private CardActionLogService cardActionLogService;
    @Autowired
    private PaymentPayOrderService paymentPayOrderService;
    @Autowired
    private BusinessDayFeeLogService businessDayFeeLogService;
    @Autowired
    private BusinessDayFundLogService businessDayFundLogService;
    @Autowired
    private UserCardFeeConfigService userCardFeeConfigService;
    @Autowired
    private MessageService messageService;
    @Autowired
    HttpServletRequest request;
    @Autowired
    private UserWalletLogService userWalletLogService;
    @Autowired
    private CardApplyOrderService cardApplyOrderService;
    @Autowired
    private CardService cardService;

    /**
     * 卡片管理
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @PostMapping("/listOfCards")
    public JsonResult<Object> listOfCards(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<ListOfCardsDetailDto> page = userCardService.getListOfCards(cardSettingsDto);
        return JsonResult.ok(page);
    }


    /**
     * 卡片管理导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @PostMapping("/exportListOfCards")
    public void exportListOfCards(@RequestBody(required = false) CardSettingsDto cardSettingsDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user2 = (User) request.getAttribute("user");
        if (null == cardSettingsDto) {
            cardSettingsDto = new CardSettingsDto();
        }
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        userCardService.exportListOfCards(cardSettingsDto, response, request);
    }

    /**
     * 可用卡段列表
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @PostMapping("/segmentSettingsList")
    public JsonResult<Object> segmentSettingsList(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<UserCardFeeConfigDetailVo> page = userCardFeeConfigService.segmentSettingsList(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 卡片操作申请数据
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @PostMapping("/cardApplyOrderList")
    public JsonResult<Object> cardApplyOrderList(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<CardApplyOrder> page = cardApplyOrderService.cardApplyOrderList(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 卡段列表
     *
     * @param cardModel 卡段模式
     * @return 卡段列表分页数据
     */
    @GetMapping("/listOfCardsSlot")
    public JsonResult<Object> listOfCardsSlot(@RequestParam(name = "cardModel") String cardModel) {
        User user2 = (User) request.getAttribute("user");
        List<CardsSlotDto> cardsSlotDtos = userCardFeeConfigService.listOfCardsSlot(user2.getBusinessId(), cardModel);
        return JsonResult.ok(cardsSlotDtos);
    }

    /**
     * 卡片禁用or启用接口
     *
     * @param cardManageId 卡片管理配置id
     * @return true
     */
    @GetMapping("/setCardEnabledOrDisabled")
    public JsonResult<Object> setCardEnabledOrDisabled(@RequestParam(name = "cardManageId") Integer cardManageId) {
        User user2 = (User) request.getAttribute("user");
        userCardService.setCardEnabledOrDisabled(cardManageId, user2.getBusinessId());
        return JsonResult.ok();
    }

    /**
     * 卡片详情
     *
     * @param cardId 卡片id
     * @return 卡片数据详情
     */
    @GetMapping("/getMemberCardDetail")
    public JsonResult<Object> getMemberCardDetail(@RequestParam(name = "cardId") String cardId) {
        User user2 = (User) request.getAttribute("user");
        Integer businessId = user2.getBusinessId();
        ListOfCardsDetailDto byMemberCardDetail = userCardService.getByMemberCardDetail(businessId, cardId);
        return JsonResult.ok(byMemberCardDetail);
    }

    /**
     * 卡片交易流水
     *
     * @param cardSettingsDto 筛选条件
     * @return 交易流水分页数据
     */
    @PostMapping("/cardLogs")
    public JsonResult<Object> CardLogs(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        Integer businessId = user2.getBusinessId();
        IPage<UserCardLog> userCardLogIPage = userCardLogService.cardLogsDetail(businessId, cardSettingsDto.getCardNumber(), cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return JsonResult.ok(userCardLogIPage);
    }

    /**
     * 卡片交易成功失败详情
     *
     * @param orderId 订单号
     * @param cardId  卡片id
     * @return 订单详情
     */
    @GetMapping("/transactionDetails")
    public JsonResult<Object> getTransactionDetails(@RequestParam(name = "orderId") String orderId, @RequestParam(name = "cardId") String cardId) {
        List<UserCardLog> userCardLogs = userCardLogService.selectById(orderId, cardId);
        return JsonResult.ok(userCardLogs);
    }

    /**
     * 修改卡片昵称
     *
     * @param cardId   卡id
     * @param cardName 卡片昵称
     * @return true
     */
    @GetMapping("/updateCardName")
    public JsonResult<Object> updateCardName(@RequestParam(name = "cardId") String cardId, @RequestParam(name = "cardName") String cardName) {
        userCardService.updateCardId(cardId, cardName);
        return JsonResult.ok();
    }

    /**
     * 卡片交易详情
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片交易分页数据详情
     */
    @PostMapping("/getCardTransactionDetails")
    public JsonResult<Object> getCardTransactionDetails(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<UserCardLogDto> page = userCardLogService.getCardTransactionDetails(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 卡片交易详情
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片交易分页数据详情
     */
    @PostMapping("/cardTransactionDetailsStat")
    public JsonResult<Object> cardTransactionDetailsStat(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        UserCardTradeStatDto statDto = userCardLogService.cardTransactionDetailsStat(cardSettingsDto);
        return JsonResult.ok(statDto);
    }


    /**
     * 卡片历史
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片历史分页数据详情
     */
    @PostMapping("/getCardHistory")
    public JsonResult<Object> getCardHistory(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<CardActionLogDto> page = cardActionLogService.getCardHistory(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 卡片历史导出
     *
     * @param cardSettingsDto 筛选条件
     * @param response        请求对象
     * @param request         响应数据
     * @throws IOException ；
     */
    @PostMapping("/exportCardHistory")
    public void exportCardHistory(@RequestBody CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        cardActionLogService.exportCardHistory(cardSettingsDto, response, request);
    }

    /**
     * 卡片明细
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片明细数据详情
     */
    @PostMapping("/getShareCardsDetails")
    public JsonResult<Object> getShareCardsDetails(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<UserCardLogDto> page = userCardLogService.getShareCardsDetails(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 资金流水
     */
    @PostMapping("/getFlowDetails")
    public JsonResult<Object> getFlowDetails(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<BusinessDayFeeLog> page = businessDayFeeLogService.getFlowDetails(cardSettingsDto);
        return JsonResult.ok(page);
    }

    /**
     * 支出流水
     */
    @PostMapping("/getExpenditureTurnover")
    public JsonResult<Object> getExpenditureTurnover(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<BusinessDayFundLog> page = businessDayFundLogService.getExpenditureTurnover(cardSettingsDto);
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        List<Integer> subTypes = Arrays.asList(WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.OPEN_CARD_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(), WalletTypeEnum.WITHDRAW_FEE.getCode());
        BigDecimal totalChargeFee = userWalletLogService.queryCardMoneyAmount2(null, cardSettingsDto.getBusinessId(), subTypes);
        BigDecimal totalOpenCard = userWalletLogService.queryCardMoneyAmount2(null, cardSettingsDto.getBusinessId(), Collections.singletonList(WalletTypeEnum.OPEN_CARD_FEE.getCode()));
        List<Integer> subTypes2 = Arrays.asList(WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(), WalletTypeEnum.WITHDRAW_FEE.getCode());
        BigDecimal totalRecharge = userWalletLogService.queryCardMoneyAmount2(null, cardSettingsDto.getBusinessId(), subTypes2);
        BigDecimal cardCancellation = userWalletLogService.queryCardMoneyAmount2(null, cardSettingsDto.getBusinessId(), Collections.singletonList(WalletTypeEnum.CARD_CANCEL_FEE.getCode()));
        objectObjectHashMap.put("page", page);
        objectObjectHashMap.put("totalChargeFee", totalChargeFee != null ? totalChargeFee.negate() : BigDecimal.ZERO);
        objectObjectHashMap.put("totalOpenCard", totalOpenCard != null ? totalOpenCard.negate() : BigDecimal.ZERO);
        objectObjectHashMap.put("totalRecharge", totalRecharge != null ? totalRecharge.negate() : BigDecimal.ZERO);
        objectObjectHashMap.put("cardCancellation", cardCancellation != null ? cardCancellation.negate() : BigDecimal.ZERO);
        return JsonResult.ok(objectObjectHashMap);
    }

    /**
     * 可用卡数量接口
     *
     * @param cardSettingsDto 筛选条件
     * @return userCardFeeConfigDtoIPage 可用卡数量详情
     */
    @PostMapping("/inquireNumberOfAvailableCards")
    public JsonResult<Object> inquireNumberOfAvailableCards(@RequestBody CardSettingsDto cardSettingsDto) {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        IPage<UserCardFeeConfigDto> userCardFeeConfigDtoIPage = userCardFeeConfigService.inquireNumberOfAvailableCards(cardSettingsDto);
        return JsonResult.ok(userCardFeeConfigDtoIPage);
    }

    /**
     * 获取开卡数量接口
     *
     * @param request 请求对象；
     * @return 卡Map
     */
    @GetMapping("/numberOfAvailableCards")
    public JsonResult<Object> numberOfAvailableCards(HttpServletRequest request) {
        User user2 = (User) request.getAttribute("user");
        // 卡段配置，开卡数
        List<UserCardFeeConfigDto> list = userCardFeeConfigService.numberOfAvailableCards2(user2.getBusinessId());
        // 卡段开卡数
        int totalNumberOfCardsIssued = list.stream().mapToInt(UserCardFeeConfigDto::getNumberOfCardsIssued).sum();
        // 卡段可开卡数
        int cardNumber = list.stream().mapToInt(UserCardFeeConfigDto::getCardNumber).sum();
        Map<Object, Object> objectObjectMap = new HashMap<>();
        objectObjectMap.put("numberOfCardsIssued", totalNumberOfCardsIssued);
        objectObjectMap.put("cardNumber", cardNumber);
        return JsonResult.ok(objectObjectMap);
    }


    /**
     * 获取公告数据列表接口
     *
     * @param lang 语言；
     * @return 公告列表
     */
    @GetMapping("/getAnnouncementList")
    public JsonResult<Object> announcement(@RequestParam(name = "lang") Integer lang) {
        QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("lang", lang);
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort");
        List<Message> list = messageService.list(queryWrapper);
        return JsonResult.ok(list);
    }

    /**
     * 绑定用卡人
     *
     * @param cardId   卡id
     * @param holderId 用卡人id
     * @return 修改后卡详情
     */
    @GetMapping("/addHolderId")
    public JsonResult<Object> holderId(@RequestParam(name = "cardId") Integer cardId, @RequestParam(name = "holderId") Integer holderId) {
        UserCard byId = userCardService.getById(cardId);
        byId.setHolderId(holderId);
        userCardService.updateById(byId);
        return JsonResult.ok(byId);
    }

    /**
     * 获取公告详情
     *
     * @param id 数据对应id
     * @return 数据详情
     */
    @GetMapping("/gatMessageById")
    public JsonResult<Object> updateCardName(@RequestParam(name = "id") Integer id) {
        Message byId = messageService.getById(id);
        return JsonResult.ok(byId);
    }

    /**
     * @param cardModel    卡模式
     * @param cardBelongTo 卡归属
     * @return 开卡列表
     */
    @GetMapping("/bankCard")
    public JsonResult<Object> bankCard(@RequestParam(name = "cardModel") String cardModel, @RequestParam(name = "cardBelongTo") String cardBelongTo) {
        User user2 = (User) request.getAttribute("user");
        List<UserCardFeeConfig> userCardFeeConfig = userCardFeeConfigService.bankCard(cardModel, cardBelongTo, user2.getBusinessId());
        return JsonResult.ok(userCardFeeConfig);
    }

    /**
     * 共享or储值卡下载导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @PostMapping("/export")
    public void export(@RequestBody CardSettingsDto cardSettingsDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        userCardLogService.export(cardSettingsDto, response, request);
    }

    /**
     * 卡片交易详情导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @PostMapping("/exportCardTransactionDetails")
    public void exportCardTransactionDetails(@RequestBody CardSettingsDto cardSettingsDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        userCardLogService.exportCardTransactionDetails(cardSettingsDto, response, request);
    }

    /**
     * 编辑商户卡段费率
     *
     * @param businessRateDto
     * @param request
     * @return
     */
    @PostMapping("/updateBusinessRate")
    public JsonResult<Object> updateBusinessRate(@RequestBody BusinessRateDto businessRateDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        userCardFeeConfigService.updateBusinessRate(businessRateDto, user.getBusinessId());
        return JsonResult.ok();
    }

    /**
     * 卡片流水入库测试
     */
    @GetMapping("/journalAccountStatistics")
    public JsonResult<Object> journalAccountStatistics() {
        cardActionLogService.journalAccountStatistics();
        return JsonResult.ok();
    }

    /**
     * 支出流水入库测试
     */
    @GetMapping("/expendStatistics")
    public JsonResult<Object> expendStatistics() {
        cardActionLogService.expendStatistics();
        return JsonResult.ok();
    }

    /**
     * 代付流水入库测试
     */
    @GetMapping("/paymentPayOrderStatistics")
    public JsonResult<Object> paymentPayOrderStatistics() {
        paymentPayOrderService.paymentPayOrderStatistics();
        return JsonResult.ok();
    }

    /**
     * 月费收取入库测试
     */
    @GetMapping("/doCardMonthFee")
    public JsonResult<Object> doCarMonthFee() {
        cardService.doCardMonthFee();
        return JsonResult.ok();
    }

}
