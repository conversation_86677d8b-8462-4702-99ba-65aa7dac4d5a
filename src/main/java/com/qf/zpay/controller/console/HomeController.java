package com.qf.zpay.controller.console;

import cn.hutool.json.JSONObject;
import com.qf.zpay.dto.common.TokenDto;
import com.qf.zpay.dto.req.EmailDto;
import com.qf.zpay.dto.req.LoginDto;
import com.qf.zpay.dto.req.SigninDto;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.TokenService;
import com.qf.zpay.service.UsdtService;
import com.qf.zpay.service.VerifyCodeService;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.User;
import generator.service.UserDevConfigService;
import generator.service.UserService;
import generator.service.UserWalletService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @updateTime 2024/5/31 14:00
 */
@RestController
@RequestMapping("/console/home")
@Validated
@Slf4j
public class HomeController {


    @Autowired
    UserService userService;

    @Autowired
    VerifyCodeService codeService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private UserDevConfigService devConfigService;

    @Autowired
    private UserWalletService userWalletService;

    @Autowired
    HttpServletRequest request;


    @PostMapping("/send-email")
    @ResponseBody
    public JsonResult<Object> sendEmail(@Valid @RequestBody EmailDto emailDto) {
        boolean res = codeService.sendCode(emailDto.getEmail());
        if (!res) {
            return JsonResult.ko();
        }
        return JsonResult.ok();
    }


//    @Autowired
//    SuperAccountService superAccountService;

    @Autowired
    UsdtService usdtService;


    @PostMapping("/signIn")
    public JsonResult<Object> signIn(@Valid @RequestBody SigninDto signinDto) {
        boolean res = codeService.verifyCode(signinDto.getEmail(), signinDto.getCode());
        if (!res) {
            return JsonResult.ko(ResultCode.VCODE_ERR);
        }
        return (userService.signinByEmail(signinDto));
    }

    @PostMapping("/login")
    public JsonResult<Object> login(@Valid @RequestBody LoginDto loginDto) {
        boolean res = codeService.verifyCode(loginDto.getEmail(), loginDto.getCode());
        if (!res) {
            return JsonResult.ko(ResultCode.VCODE_ERR);
        }
        // TODO 超级账号登录
//        superAccountService

        String ip = ZHelperUtil.getClientIp(request);
        User user = userService.loginByEmail(loginDto.getEmail(), loginDto.getPassword(), ip);
        if (user.getStatus().equals(2)) {
            Assert.fail(ResultCode.BUSINESS_LOCK);
        }

        TokenDto token = tokenService.genJwtToken(user);

        rsaService.genRsaPair(user.getId());
        devConfigService.genUserDevConfig(user.getId());
        userWalletService.initWallet(user.getId());
        usdtService.genUserAddress(user.getId());

        return JsonResult.ok(token);
    }


    @PostMapping("/forgotPassword")
    public JsonResult<Object> forgotPassword(@Valid @RequestBody SigninDto signinDto) {
        boolean res = codeService.verifyCode(signinDto.getEmail(), signinDto.getCode());
        if (!res) {
            return JsonResult.ko(ResultCode.VCODE_ERR);
        }
        return (userService.forgotPassword(signinDto));
    }

    @PostMapping("refresh-token")
    public JsonResult<Object> refreshToken(@RequestBody JSONObject body) {
        TokenDto token = tokenService.refreshJwtToken(body.getStr("refreshToken"));
        return JsonResult.ok(token);
    }

}
