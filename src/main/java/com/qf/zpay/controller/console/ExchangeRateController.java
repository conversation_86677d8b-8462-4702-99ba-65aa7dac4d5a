package com.qf.zpay.controller.console;

import com.qf.zpay.response.JsonResult;
import com.qf.zpay.service.ExchangeRateService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@RestController
@RequestMapping("/utils")
public class ExchangeRateController {
    @Autowired
    private RedissonClient redis;

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 获取实时汇率
     *
     * @return
     */
    @GetMapping("/exchangeRate")
    public JsonResult<Object> getExchangeRate() {
        RMap<String, Double> exchangeRateMap = redis.getMap(ExchangeRateService.EXCHANGE_RATE_KEY);
        exchangeRateService.getRatesFromWeb();
        return JsonResult.ok(exchangeRateMap);
    }

}
