package com.qf.zpay.security;


import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.TokenService;
import generator.domain.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 21:36
 */
@Log4j2
@Component
public class JwtTokenInterceptor implements HandlerInterceptor {


    private final TokenService tokenService;

    public JwtTokenInterceptor(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String auth = request.getHeader("Authorization");
//        log.info("----------Authorization: {}", auth);
        if (auth == null || !auth.startsWith("Bearer ")) {
            Assert.fail(ResultCode.UNAUTHORIZED);
            return false;
        }

        String token = auth.substring(7);
        if (token.isEmpty()) {
            Assert.fail(ResultCode.UNAUTHORIZED);
            return false;
        }

        User user = tokenService.validationJwtToken(token);
        if (user.getBusinessStatus().equals(2)) {
            Assert.fail(ResultCode.BUSINESS_DISABLED);
            return false;
        }
        request.setAttribute("user", user);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 这里可以处理请求处理之后的操作，如果需要的话
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 这里可以处理请求完成后的操作，如果需要的话
    }
}

