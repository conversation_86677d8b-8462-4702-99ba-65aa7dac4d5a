package com.qf.zpay.security;


import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import generator.domain.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 21:36
 */
@Log4j2
@Component
public class RpcInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String auth = request.getHeader("SECRET");
        if (!"TAMIA".equalsIgnoreCase(auth)) {
            Assert.fail(ResultCode.UNAUTHORIZED);
            return false;
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 这里可以处理请求处理之后的操作，如果需要的话
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 这里可以处理请求完成后的操作，如果需要的话
    }
}

