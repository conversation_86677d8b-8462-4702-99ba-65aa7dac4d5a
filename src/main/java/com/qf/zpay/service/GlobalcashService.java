package com.qf.zpay.service;

import com.qf.zpay.constants.CardTransTypeEnum;
import com.qf.zpay.util.DateUtil;
import com.qf.zpay.util.SeleniumService;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import generator.service.UserCardLogService;
import generator.service.UserCardService;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class GlobalcashService {

    @Value("${file.proof}")
    private String proof;

    @Autowired
    private RedissonClient redis;

    @Autowired
    SeleniumService seleniumService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    UserCardLogService userCardLogService;

    /**
     * 全球付充值
     *
     * @throws InterruptedException 错误
     */
    @Async
    @Retryable(retryFor = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    public void recharge(String cardNumber, BigDecimal money) throws InterruptedException {
        log.info("【全球付充值】模拟充值开始");
        ChromeDriver driver = null;
        try {
            driver = seleniumService.getInstance("globalcash");
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(40));
            // 1.点击充值
            driver.get("https://merchant.globalcash.cn/v4/cbc/cardRecharge");
            Thread.sleep(2000);

            WebElement container = driver.findElement(By.className("container"));
            List<WebElement> elements = container.findElements(By.xpath("./*"));
            WebElement webElement2 = elements.get(2);
            List<WebElement> elements2 = webElement2.findElements(By.xpath("./*"));
            // 2.卡号
            WebElement cardNumberElement3 = elements2.get(0);
            WebElement cardNumberElement4 = cardNumberElement3.findElement(By.xpath("./*"));
            List<WebElement> cardNumberElement5 = cardNumberElement4.findElements(By.xpath("./*"));
            WebElement cardNumberElement6 = cardNumberElement5.get(1).findElement(By.xpath("./*")).findElement(By.xpath("./*"));
            cardNumberElement6.sendKeys(cardNumber);
            // 3.金额
            WebElement moneyElement3 = elements2.get(1);
            WebElement moneyElement4 = moneyElement3.findElement(By.xpath("./*"));
            List<WebElement> moneyElement5 = moneyElement4.findElements(By.xpath("./*"));
            WebElement moneyElement6 = moneyElement5.get(1).findElement(By.xpath("./*")).findElement(By.xpath("./*"));
            moneyElement6.sendKeys(money.toString());
            // 汇款银行机构/汇款银行/汇款账户名称/汇款凭证
            WebElement remittance = elements.get(5);
            List<WebElement> remittance1 = remittance.findElements(By.xpath("./*"));
            // 4.汇款银行机构
            WebElement orgElement3 = remittance1.get(0);
            WebElement orgElement4 = orgElement3.findElement(By.xpath("./*"));
            WebElement orgElement5 = orgElement4.findElement(By.xpath("./*"));
            List<WebElement> orgelements3 = orgElement5.findElements(By.xpath("./*"));
            WebElement orgElement6 = orgelements3.get(1);
            WebElement orgElement7 = orgElement6.findElement(By.xpath("./*"));
            WebElement orgElement8 = orgElement7.findElement(By.xpath("./*"));
            orgElement8.sendKeys("CbiBank");
            // 5.汇款银行
            WebElement webElement3 = remittance1.get(1);
            WebElement webElement4 = webElement3.findElement(By.xpath("./*"));
            WebElement webElement5 = webElement4.findElement(By.xpath("./*"));
            List<WebElement> elements3 = webElement5.findElements(By.xpath("./*"));
            WebElement webElement6 = elements3.get(1);
            WebElement webElement7 = webElement6.findElement(By.xpath("./*"));
            WebElement webElement8 = webElement7.findElement(By.xpath("./*"));
            webElement8.sendKeys("************");
            // 6.汇款账户名称
            WebElement nameElement3 = remittance1.get(2);
            WebElement nameElement4 = nameElement3.findElement(By.xpath("./*"));
            WebElement nameElement5 = nameElement4.findElement(By.xpath("./*"));
            List<WebElement> nameelements3 = nameElement5.findElements(By.xpath("./*"));
            WebElement nameElement6 = nameelements3.get(1);
            WebElement nameElement7 = nameElement6.findElement(By.xpath("./*"));
            WebElement nameElement8 = nameElement7.findElement(By.xpath("./*"));
            nameElement8.sendKeys("ZNETWORK SOFTWARE SOLUTIONS LIMITED");

            // 7.汇款凭证
            WebElement proofElement3 = remittance1.get(3);
            WebElement proofElement4 = proofElement3.findElement(By.xpath("./*"));
            WebElement proofElement5 = proofElement4.findElement(By.xpath("./*"));
            List<WebElement> proofelements3 = proofElement5.findElements(By.xpath("./*"));
            WebElement proofElement8 = proofelements3.get(1).findElements(By.xpath("./*")).get(0).findElements(By.xpath("./*")).get(0).findElements(By.xpath("./*")).get(1);
            Path path = Paths.get(proof);
            proofElement8.sendKeys(path.toAbsolutePath().toString());

            Thread.sleep(2000);
            driver.manage().window().maximize();
            // 8.下一步
            WebElement nextStepWebElement = elements.get(6);
            WebElement ObscureDiv = nextStepWebElement.findElement(By.xpath("./*")).findElement(By.xpath("./*")).findElements(By.xpath("./*")).get(1).findElements(By.xpath("./*")).get(0).findElement(By.xpath("./*"));
            ObscureDiv.click();

            Thread.sleep(1000);
            // 9.确定
            WebElement confirmWebElement = elements.get(7);
            WebElement confirmSpanElement = confirmWebElement.findElement(By.xpath("./*")).findElements(By.xpath("./*")).get(2).findElement(By.xpath("./*")).findElements(By.xpath("./*")).get(0).findElement(By.xpath("./*"));
            wait.until(ExpectedConditions.elementToBeClickable(confirmSpanElement));
            confirmSpanElement.click();
            // 10.结果
            WebElement result = driver.findElement(By.className("container")).findElement(By.xpath("./*")).findElements(By.xpath("./*")).get(0).findElements(By.xpath("./*")).get(1);
            String resultText = result.getText();
            log.info("卡片：{} 金额：{} 充值状态：{}", cardNumber, money.toString(), (resultText));

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            seleniumService.giveBack();// 解锁
        }
    }

    /**
     * 全球付采集卡片日志
     * 模拟登录 获取cookies
     *
     * @throws InterruptedException 错误
     */
    public void UserCardLog() {
        ChromeDriver driver = null;
        try {
            HashMap<String, UserCardLog> stringUserCardLogHashMap = new HashMap<>();
            RBucket<String> codeCache2 = redis.getBucket("qxf_code");
            codeCache2.delete();
            // 设置并初始化 Chrome WebDriver
            driver = seleniumService.getInstance("globalcash");
            // 显式等待用户名输入框的出现，使用 Duration.ofSeconds
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(40));
            wait.until(ExpectedConditions.urlContains("/v4/cbc"));
            // 进一步确保页面加载完成，可以使用以下等待：
            wait.until(ExpectedConditions.jsReturnsValue("return document.readyState=='complete'"));
            driver.get("https://merchant.globalcash.cn/v4/cbc/trade");
            wait.until(ExpectedConditions.jsReturnsValue("return document.readyState=='complete'"));
            Thread.sleep(5000);
            // 定位按钮元素
            WebElement queryButton = driver.findElement(By.xpath("//button[span[text()='查询']]"));
            queryButton.click();
            Thread.sleep(10000);
            WebElement element = driver.findElement(By.cssSelector("div.el-pagination.is-background"));
            WebElement element1 = element.findElement(By.tagName("ul"));
            List<WebElement> li = element1.findElements(By.tagName("li"));
            extracted(driver, stringUserCardLogHashMap);
            for (int g = 1; g < li.size(); g++) {
                WebElement button = driver.findElement(By.cssSelector("button.btn-next"));
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", button);
                Thread.sleep(10000);
                WebElement tbodyWrapper = driver.findElement(By.cssSelector("div.el-table__body-wrapper.is-scrolling-left"));
                // 在该 <div> 内查找 <tbody> 元素
                WebElement tbody = tbodyWrapper.findElement(By.tagName("tbody"));
                // 获取所有的 <tr> 行元素
                List<WebElement> rows = tbody.findElements(By.tagName("tr"));
                for (WebElement row : rows) {
                    List<WebElement> cells = row.findElements(By.tagName("td"));
                    Map<Integer, String> rowData = new LinkedHashMap<>();
                    for (int i = 0; i < cells.size(); i++) {
                        String text = cells.get(i).getText();
                        rowData.put(i, text);
                    }

                    if (rowData.get(1).equals("卡号") && !rowData.get(17).equals("账户验证-1103") && !rowData.get(17).equals("代充值-0100")) {
                        Boolean orderIdExists = userCardLogService.isOrderIdExists(rowData.get(14));
                        if (orderIdExists) {
                            break;
                        }
                        addUserCardLog(rowData, stringUserCardLogHashMap);
                    }
                }
            }
//            log.error(stringUserCardLogHashMap);
            if (!stringUserCardLogHashMap.isEmpty()) {
                for (Map.Entry<String, UserCardLog> entry : stringUserCardLogHashMap.entrySet()) {
                    UserCard byCardNumber = userCardService.getByCardNumber(entry.getKey());
                    if (byCardNumber != null) {
                        UserCardLog value = entry.getValue();
                        byCardNumber.setAmount(value.getAvailableCredit());
                        userCardService.updateById(byCardNumber);
                    }
                }
            }

        } catch (Exception e) {
            log.error("采集卡片日志定时任务失败", e);
        } finally {
            seleniumService.giveBack();
        }

    }


    private void extracted(ChromeDriver driver, HashMap<String, UserCardLog> stringUserCardLogHashMap) {
        WebElement tbodyWrapper = driver.findElement(By.cssSelector("div.el-table__body-wrapper.is-scrolling-left"));
        // 在该 <div> 内查找 <tbody> 元素
        WebElement tbody = tbodyWrapper.findElement(By.tagName("tbody"));
        // 获取所有的 <tr> 行元素
        List<WebElement> rows = tbody.findElements(By.tagName("tr"));
        // 遍历每一行并将数据与表头关联
        for (WebElement row : rows) {
            List<WebElement> cells = row.findElements(By.tagName("td"));
            Map<Integer, String> rowData = new LinkedHashMap<>();
            for (int i = 0; i < cells.size(); i++) {
                String text = cells.get(i).getText();
                rowData.put(i, text);
            }
            if (rowData.get(1).equals("卡号") && !rowData.get(17).equals("账户验证-1103") && !rowData.get(17).equals("代充值-0100")) {
                addUserCardLog(rowData, stringUserCardLogHashMap);
            }
        }
    }

    /**
     *
     */
    private void addUserCardLog(Map<Integer, String> rowData, HashMap<String, UserCardLog> stringUserCardLogHashMap) {
        UserCard userCard = userCardService.getByCardNumber(rowData.get(0));
        if (!userCardLogService.isOrderIdExists(rowData.get(14))) {
            UserCardLog userCardLog = new UserCardLog();

            userCardLog.setOrderId(rowData.get(14));
            userCardLog.setAuthTime(DateUtil.getCurrentDateTime(rowData.get(3)));
            userCardLog.setTransAmountCurrency(rowData.get(29));
            userCardLog.setTransAmount(rowData.get(28).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(28)).abs());
            userCardLog.setAuthAmount(rowData.get(45).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(45)).abs());
            userCardLog.setSettledAmount(rowData.get(45).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(45)).abs());

            userCardLog.setMerchantName(rowData.get(11));
            userCardLog.setMerchantCountryCode(rowData.get(7));
            userCardLog.setStatus(rowData.get(20).equals("成功") ? "Settled" : "AuthFailure");
            userCardLog.setAuthAmountCurrency(rowData.get(44));
            Map<String, String> stringStringMap = statusSwitch(rowData.get(17));
            userCardLog.setFundsDirection(stringStringMap.get("fundsDirection"));
            userCardLog.setTransactionType(stringStringMap.get("transactionType"));
            userCardLog.setNoticeType(stringStringMap.get("notice_type"));
            if (rowData.get(18).equals("冲正") && rowData.get(20).equals("成功")) {
                userCardLog.setFundsDirection("Income");
                userCardLog.setTransactionType(CardTransTypeEnum.CorrectiveRefund.getCode());
            }
            userCardLog.setFailureReason(rowData.get(20).equals("成功") ? null : rowData.get(26));
            userCardLog.setAvailableCredit(rowData.get(30).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(30)).abs());
            userCardLog.setCreateTime(DateUtil.getCurrentDateTime(rowData.get(3)));
            userCardLog.setCreateDate(DateUtil.getCurrentDateTime(rowData.get(3)));
            if (userCard != null) {
                userCardLog.setBusinessId(userCard.getUid());
                userCardLog.setCardId(userCard.getCardId());
                userCardLog.setProductCode(userCard.getProductCode());
                userCardLog.setMaskCardNumber(userCard.getCardNumber());
                userCardLog.setCardModel(userCard.getCardModel());
                UserCardLog userCardLog1 = stringUserCardLogHashMap.get(userCard.getCardNumber());
                if (userCardLog1 == null && userCardLog.getAvailableCredit().compareTo(BigDecimal.ZERO) > 0) {
                    stringUserCardLogHashMap.put(userCard.getCardNumber(), userCardLog);
                }
            }
            userCardLogService.save(userCardLog);

        }
    }

    /**
     * 卡号脱敏
     *
     * @param cardNumber 卡号
     * @return 脱敏卡号
     */
    public static String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() <= 8) {
            return cardNumber;
        }
        int firstFourDigits = 4;
        int lastFourDigits = 4;
        int maskedDigits = cardNumber.length() - firstFourDigits - lastFourDigits;
        return cardNumber.substring(0, firstFourDigits) +
                "*".repeat(maskedDigits) +
                cardNumber.substring(cardNumber.length() - lastFourDigits);
    }

    public static Map<String, String> statusSwitch(String tradeType) {
        HashMap<String, String> hashMap = new HashMap<>();
        switch (tradeType) {
            case "账户验证-1103":
                hashMap.put("status", "c");
                break;
            case "账户消费-0200",
                 "ATM取款(UPI)-0208",
                 "便利店提款-0218",
                 "账户消费撤销-0930",
                 "授权清算-0202",
                 "消费(UPI)-0210",
                 "代缴费(UPI)-0209",
                 "消费撤销-0920",
                 "ATM取款授权结算(MC/visa)-0206",
                 "预授权完成(UPI)-0211",
                 "ATM余额查询-0702",
                 "预授权完成撤销-0924",
                 "代缴费撤销-0925",
                 "ATM取款授权(MC/Visa)-0205",
                 "自动授权撤销-0923",
                 "预授权-0213",
                 "消费授权撤销-0921",
                 "重新授权-0926",
                 "消费授权-0201",
                 "授权通知-0212",
                 "人工授权撤销-0922",
                 "部分撤销-0928":
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Expenditure");
                hashMap.put("transactionType", CardTransTypeEnum.Auth.getCode());
                hashMap.put("notice_type", "cardPay");
                break;
            case "代充值-0100",
                 "卡激活-0110",
                 "在线充值-0102",
                 "代充值撤销-0927",
                 "线下充值-0104",
                 "卡激活撤销-0310":
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Income");
                hashMap.put("transactionType", CardTransTypeEnum.CardRecharge.getCode());
                hashMap.put("notice_type", "cardRecharge");
                break;
            default:
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Income");
                hashMap.put("transactionType", tradeType);
                hashMap.put("notice_type", "cardPay");
        }
        return hashMap;
    }

    public String getCode() {
        try {
            long startTime = System.currentTimeMillis();
            Thread.sleep(3000);
            while (System.currentTimeMillis() - startTime < 180000) {
                RBucket<String> codeCache = redis.getBucket("qxf_code");
                if (codeCache.isExists()) {
                    String code = codeCache.get();
                    codeCache.delete();
                    return code;
                }
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("获取验证码失败", e);
        }
        log.warn("等待超时，未获取到验证码");
        return null;
    }


}
