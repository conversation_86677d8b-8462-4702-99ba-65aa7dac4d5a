package com.qf.zpay.service.payment;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.gsalary.sdk.exceptions.GSalaryServiceException;
import com.qf.zpay.exception.GSalaryException;
import com.qf.zpay.util.ZHelperUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @updateTime 2024/7/1 10:59
 * GSalary 上游渠道适配
 * @doc <a href="https://api.gsalary.com/doc/index.html?lang=cn#tag/Remittance">...</a>
 */
@Log4j2
@Component("GSalaryCardBuilder")
public class GSalary {
    private final String path = "/v1/remittance";

    @Autowired
    ZHelperUtil zHelper;


    /**
     * 添加收款人
     *
     * @param requestBody ;
     * @return ;
     */
    public String savePayee(Map<String, Object> requestBody) {
        String path = this.path + "/payees";
        JSONObject entries = getEntries(requestBody, path);
        return entries.getStr("payee_id");
    }

    /**
     * 申请锁汇
     *
     * @return JSONObject
     */
    public JSONObject quotes(Map<String, Object> requestBody) {
        String path = this.path + "/quotes";
        return getEntries(requestBody, path);
    }

    /**
     * 付款
     *
     * @param requestBody ；
     * @return ；
     */
    public JSONObject paymentOrder(JSONObject requestBody) {
        String path = this.path + "/orders";
        return getEntries(requestBody, path);

    }

    /**
     * 更新收款人
     */
    public void renewalPayee(Map<String, Object> requestBody, String payeeId) {
        try {
            String path = this.path + "/payees/" + payeeId;
            zHelper.sendReqPutGSalary(path, requestBody);
        } catch (GSalaryServiceException | JsonProcessingException e) {
            log.error(e);
            String message = e.getMessage();
            int index = message.indexOf("GSalary response error:");
            if (index != -1) {
                String errorMessage = message.substring(index + "GSalary response error:".length()).trim();
                throw new GSalaryException("2001", errorMessage);
            }
            throw new GSalaryException("2001", e.getMessage());
        }
    }

    /**
     * 添加收款人账户
     *
     * @param requestBody ；
     * @param payeeId     ；
     * @return ；
     */
    public JSONObject savePayeeAccount(Map<String, Object> requestBody, String payeeId) {
        String path = this.path + "/payees/" + payeeId + "/accounts";
        return getEntries(requestBody, path);
    }

    /**
     * 获取收款人
     *
     * @return ;
     */
    public JSONObject getPayment() {
        String path = this.path + "/payers";
        Map<String, String> args = new HashMap<>();
        return zHelper.sendReqGetGSalary(path, args);
    }

    /**
     * 查询支持付款国家(银行账户)
     *
     * @return ;
     */
    public JSONObject getPayoutCountries() {
        String path = this.path + "/payout_countries";
        Map<String, String> args = new HashMap<>();
        return zHelper.sendReqGetGSalary(path, args);
    }

    /**
     * 获取收款人账户表单
     *
     * @param requestBody ；
     * @param payeeId     ；
     * @return ；
     */
    public JSONObject getAccountRegisterFormat(Map<String, String> requestBody, String payeeId) {
        String path = this.path + "/payees/" + payeeId + "/account_register_format";
        return zHelper.sendReqGetGSalary(path, requestBody);
    }

    /**
     * 获取收款人的账户
     * https://api.gsalary.com/v1/remittance/payees/{payee_id}/accounts
     *
     * @param requestBody ；
     * @param payeeId     ；
     * @return ；
     */
    public JSONObject getAccounts(Map<String, String> requestBody, String payeeId) {
        String path = this.path + "/payees/" + payeeId + "/accounts";
        return zHelper.sendReqGetGSalary(path, requestBody);
    }

    /**
     * 获取收款人的账户
     * https://api.gsalary.com/v1/remittance/clearing_networks
     *
     * @param requestBody ；
     * @return ；
     */
    public JSONObject getClearingNetworks(Map<String, String> requestBody) {
        String path = this.path + "/clearing_networks";
        return zHelper.sendReqGetGSalary(path, requestBody);
    }


    /**
     * 注册收款人账户（银行账户）
     *
     * @param requestBody ；
     * @param payeeId     ；
     * @return ；
     */
    public JSONObject saveAccountRegistry(Map<String, Object> requestBody, String payeeId) {
        String path = this.path + "/payees/" + payeeId + "/account_registry";
        return getEntries(requestBody, path);
    }

    private JSONObject getEntries(Map<String, Object> requestBody, String path) {
        try {
            return zHelper.sendReqGSalary(path, requestBody);
        } catch (GSalaryServiceException | JsonProcessingException e) {
            log.error(e);
            String message = e.getMessage();
            int index = message.indexOf("GSalary response error:");
            if (index != -1) {
                String errorMessage = message.substring(index + "GSalary response error:".length()).trim();
                throw new GSalaryException("2001", errorMessage);
            }
            throw new GSalaryException("2001", e.getMessage());
        }
    }


    /**
     * @param requestBody ;
     * @return ;
     */
    public JSONObject modifyOrderStatus(Map<String, String> requestBody) {
        String path = this.path + "/orders";
        return zHelper.sendReqGetGSalary(path, requestBody);
    }

    /**
     * 查询汇率
     *
     * @param requestBody ;
     * @return ;
     */
    public JSONObject currentExchangeRate(Map<String, String> requestBody) {
        String path = "/v1/exchange/current_exchange_rate";
        return zHelper.sendReqGetGSalary(path, requestBody);
    }

    public JSONObject availablePaymentMethods() {
        String path = this.path + "/available_payment_methods";
        Map<String, String> args = new HashMap<>();
        return zHelper.sendReqGetGSalary(path, args);
    }

    public JSONObject deleteAccount(String accountId) {
        String path = this.path + "/payee_accounts/" + accountId;
        Map<String, String> args = new HashMap<>();
        return zHelper.sendReqDelGSalary(path, args);
    }
}
