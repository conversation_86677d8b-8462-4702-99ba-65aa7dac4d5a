package com.qf.zpay.service.pojo;

import com.qf.zpay.constants.CardTransTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/11/19 16:49
 */
@Data
public class CardTransDto {


    /**
     * 交易id
     */
    private String orderId;

    /**
     * 预授权时间
     */
    private Date authTime;

    /**
     * 交易币种
     */
    private String transAmountCurrency;

    /**
     * 交易金额
     */
    private BigDecimal transAmount;

    /**
     * 授权币种
     */
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    private BigDecimal authAmount;

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 卡ID
     */
    private String cardId;


    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户国家代码
     */
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 商户所在州或区
     */
    private String merchantState;

    /**
     * 商户邮编
     */
    private String merchantZipCode;

    /**
     * 商户描述
     */
    private String merchantDesc;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    private String status;


    /**
     * 失败原因
     */
    private String failureReason;


    /**
     * 可用额度
     */
    private BigDecimal availableCredit;
    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    private String fundsDirection;

    /**
     * 交易类型【Consume:消费、ConsumeRefund:消费退款、ConsumeDispute:消费争议、DisputeRelease:争议释放、ConsumeReversal:消费冲正、ConsumeRefundReversal:消费退款冲正、AuthQuery:预授权查询、另外自己加上  开卡手续费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  销卡结算金额cardCancel】
     */
    private CardTransTypeEnum transactionTypeEnum;

    /**
     * 上游总收费
     */
    private BigDecimal totalFee;

    /**
     * 上游总收费 货币
     */
    private String totalFeeCurrency;
    /**
     * 上游费详情
     */
    private String businessFeeDetail;
    /**
     * 平台费详情
     */
    private String platformFeeDetail;
    /**
     * 手续费扣费金额
     */
    private BigDecimal feeDeductionAmount;

    /**
     * 手续费返还金额
     */
    private BigDecimal feeReturnAmount;


    /**
     * 累计
     */
    private BigDecimal accruingAmounts;

    /**
     * 应扣款金额
     */
    private BigDecimal changeFee;

    /**
     * 上游金额
     */
    private BigDecimal upAmount;
}
