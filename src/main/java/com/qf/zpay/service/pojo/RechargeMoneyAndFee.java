package com.qf.zpay.service.pojo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/11/5 19:37
 */
@Data
public class RechargeMoneyAndFee {
    
    // 充值金额
    public BigDecimal money;
    // 到账金额
    public BigDecimal rechargeMoney;
    // 上游手续费率
    public BigDecimal costFeeRate;
    // 上游手续费率
    public BigDecimal costFee;
    // 平台手续费率
    public BigDecimal platformFeeRate;
    // 平台手续费
    public BigDecimal platformFee;
    // 商户手续费率
    public BigDecimal businessFeeRate;
    // 商户手续费
    public BigDecimal businessFee;
    // 平台盈利
    public BigDecimal platformEarn;
    // 商户盈利
    public BigDecimal businessEarn;

}
