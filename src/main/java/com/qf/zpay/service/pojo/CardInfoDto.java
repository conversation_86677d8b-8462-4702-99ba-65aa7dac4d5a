package com.qf.zpay.service.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/6/19 15:47
 */
@Data
public class CardInfoDto {

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     * 账面金额
     */
    private BigDecimal virtualAmt;

    /**
     * 卡模式 CardModelEnum
     */
    private CardModelEnum cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额
     */
    @JsonProperty("cardSingleLimit")
    private BigDecimal cardAmt;

    /**
     * 总限额
     */
    @JsonProperty("cardTotalLimit")
    private BigDecimal cardTotalAmt;

    /**
     * 有效期
     */
    @JsonProperty("cardExpiration")
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡状态 CardStatusEnum
     */
    private CardStatusEnum cardStatus;


    /**
     * 卡操作状态 0 开卡中 1 卡正常
     */
    private Integer status;

    /**
     * 实际开卡的时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;

    /**
     * 注销卡片的时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;

}
