package com.qf.zpay.service.pojo;

import generator.domain.UserCard;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/6/27 22:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserCardWithFee extends UserCard {

    /**
     * 开卡费
     */
    public BigDecimal platformOpenCardFee;

    public BigDecimal businessOpenCardFee;

    /**
     * 本次充值金额
     */
    public BigDecimal rechargeMoney;

    /**
     * 本次充值手续费
     */
    public BigDecimal platformRechargeFee;

    public BigDecimal businessRechargeFee;

    /**
     * 销卡费
     */
    public BigDecimal platformCancelCardFee;
    
    public BigDecimal businessCancelCardFee;


}
