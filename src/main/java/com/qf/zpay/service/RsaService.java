package com.qf.zpay.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qf.zpay.dto.common.CodeDataDto;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.util.Set;

/**
 * <AUTHOR>
 * @updateTime 2024/6/13 14:33
 */
@Service
@Slf4j
public class RsaService {
    private final Integer encryptExpire = 300000;

    @Autowired
    private RedissonClient redis;

    @Autowired
    private Validator validator;

    @Async
    public void genRsaPair(Integer uid) {
//        RSA rsa = SecureUtil.rsa();
        String rsaKey = "rsa:" + uid;
        if (redis.getMap(rsaKey).isEmpty()) {
//            String publicKeyStr = rsa.getPublicKeyBase64();
//            String privateKeyStr = rsa.getPrivateKeyBase64();
            KeyPair keyPair = SecureUtil.generateKeyPair("RSA", 2048);
            String publicKeyStr = Base64.encode(keyPair.getPublic().getEncoded());
            String privateKeyStr = Base64.encode(keyPair.getPrivate().getEncoded());
            RMap<String, String> rsaMap = redis.getMap("rsa:" + uid);
            rsaMap.put("public", publicKeyStr);
            rsaMap.put("private", privateKeyStr);
        }

//        File certDir = new File("cert");
//        String publicFilename = certDir.getAbsolutePath() + File.separator + "public" + File.separator + uid + ".pem";
//        String privateFilename = certDir.getAbsolutePath() + File.separator + "private" + File.separator + uid + ".pem";
//        try {
//            FileUtil.writeString(publicKeyStr, publicFilename, CharsetUtil.CHARSET_UTF_8);
//            FileUtil.writeString(privateKeyStr, privateFilename, CharsetUtil.CHARSET_UTF_8);
//            System.out.println("Keys generated and saved successfully.");
//        } catch (Exception e) {
//            log.error("保存密钥对失败: {}", e.getMessage());
//        }
    }

    public RMap<String, String> getUserRsaPair(Integer uid) {
        RMap<String, String> rsaMap = redis.getMap("rsa:" + uid);
        if (rsaMap.isEmpty()) {
            Assert.fail(ResultCode.BUSINESS_RSA_ERR);
        }
        return rsaMap;
    }

    public CodeDataDto code2data(Integer uid, String code) {
        JSONObject data = decryptData(uid, code);
        CodeDataDto dataDto = JSONUtil.toBean(data, CodeDataDto.class);
        Set<ConstraintViolation<CodeDataDto>> violations = validator.validate(dataDto);
        if (!violations.isEmpty()) {
            Assert.fail(ResultCode.PARAM_ERR);
        }
        return dataDto;
    }

    public JSONObject decryptData(Integer uid, String encrypt) {
        if (encrypt == null || encrypt.isEmpty()) {
            Assert.paramErr(ResultCode.PARAM_MISS);
        }
        RMap<String, String> rsaMap = getUserRsaPair(uid);
        RSA rsa = new RSA(rsaMap.get("private"), rsaMap.get("public"));
        byte[] decrypt = null;
        try {
            decrypt = rsa.decrypt(Base64.decode(encrypt), KeyType.PrivateKey);
        } catch (Exception e) {
            log.error("解析加密数据串失败：{}", e.getMessage(), e);
            Assert.paramErr();
        }
        String decryptStr = new String(decrypt, StandardCharsets.UTF_8);

        JSONObject data = JSONUtil.parseObj(decryptStr);
        if (data.get("timestamp") == null) {
            Assert.fail(ResultCode.PARAM_ERR);
        }
        if (System.currentTimeMillis() - data.getLong("timestamp") >= encryptExpire) {
            Assert.fail(ResultCode.ENCRYPT_EXP);
        }
        return data;
    }


    /**
     * 对数据进行签名
     *
     * @param flag      标识
     * @param dataStr   数据字符串
     * @param algorithm 算法
     * @return String 签名 Base64 字符串
     */
    public String genSign(String flag, String dataStr, SignAlgorithm algorithm) {
        try {
            RSA rsa = getRsaPair(flag);
            byte[] bodyBytes = dataStr.getBytes(StandardCharsets.UTF_8);
            Sign signer = SecureUtil.sign(algorithm, rsa.getPrivateKeyBase64(), null);
            byte[] signatureBytes = signer.sign(bodyBytes);
            return Base64.encode(signatureBytes);
        } catch (Exception e) {
            log.error("签名失败", e);
            throw e;
        }
    }


    /**
     * 对数据进行验签
     *
     * @param flag      标识
     * @param dataStr   数据字符串
     * @param sign      签名
     * @param algorithm 算法
     * @return Boolean
     */
    public Boolean checkSign(String flag, String dataStr, String sign, SignAlgorithm algorithm) {
        try {
            RSA rsa = getRsaPair(flag);
            Sign verifier = SecureUtil.sign(algorithm, null, rsa.getPublicKeyBase64());
            return verifier.verify(dataStr.getBytes(StandardCharsets.UTF_8), Base64.decode(sign));
        } catch (Exception e) {
            log.error("验签失败", e);
            return false;
        }
    }

    /**
     * 获取业务密钥对
     *
     * @param flag 业务标识
     * @return RMap<String, String> 密钥对内容
     */
    public RSA getRsaPair(String flag) {
        RMap<String, String> rsaMap = redis.getMap("rsa:" + flag);
        if (rsaMap.isEmpty()) {
            File certDir = new File("cert");
            String publicFilename = certDir.getAbsolutePath() + File.separator + "public" + File.separator + flag + ".pem";
            String publicKeyStr = new FileReader(publicFilename).readString();
            String privateFilename = certDir.getAbsolutePath() + File.separator + "private" + File.separator + flag + ".pem";
            String privateKeyStr = new FileReader(privateFilename).readString();
            rsaMap.put("public", publicKeyStr);
            rsaMap.put("private", privateKeyStr);
        }
        return new RSA(rsaMap.get("private"), rsaMap.get("public"));
    }


}
