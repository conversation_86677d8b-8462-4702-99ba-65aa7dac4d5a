package com.qf.zpay.service;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.RandomUtil;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 15:15
 */
@Service
public class VerifyCodeService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Autowired
    private EmailService emailService;

    @Autowired
    private RedissonClient redisson;

    private static final String KEY_PREFIX = "code:";

    /**
     * 发送验证码
     *
     * @param to 邮箱地址或者手机号
     * @return boolean
     */
    public Boolean sendCode(String to) {
        String code = "123456";
        boolean sendStatus = true;
        if ("prod".equals(activeProfile)) {
            code = generateCode();
            // 如果发送 邮箱验证码
            String subject = "ZNetwork verification code";
            String text = String.format("Your verification code is <strong>%s</strong>. ", code);
            text += "Please complete the verification within 15 minutes. If you did not make this request, please disregard this message. <br/><br/>ZNetwork";

            Console.error("Code: ", code);
            sendStatus = emailService.sendSimpleMessage(to, subject, text);
        }
//        if (sendStatus) {
        RBucket<String> bucket = redisson.getBucket(KEY_PREFIX + to);
        bucket.set(code, Duration.ofMinutes(15));
//        }

        return sendStatus;
    }

    /**
     * 验证验证码
     *
     * @param target 邮箱地址或者手机号
     * @param code   验证码
     * @return boolean
     */
    public boolean verifyCode(String target, String code) {
        RBucket<String> bucket = redisson.getBucket(KEY_PREFIX + target);
        String storedCode = bucket.get();
        bucket.delete();
        return code.equals(storedCode);
    }


    private String generateCode() {
        int code = RandomUtil.randomInt(100000, 999999);
        return Integer.toString(code);
    }
}
