package com.qf.zpay.service;

import generator.domain.UserCard;
import generator.mapper.UserCardMapper;
import generator.service.CardApplyOrderService;
import generator.service.CardHolderService;
import jakarta.mail.*;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.search.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class GmailAttachmentDownloader {

    @Autowired
    RedissonClient redissonClient;
    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    private UserCardMapper userCardMapper;
    @Autowired
    private CardApplyOrderService cardApplyOrderService;

    private static final String IMAP_HOST = "imap.gmail.com";
    private static final String USERNAME = "<EMAIL>";
    private static final String PASSWORD = "iiqr hwjk rkgk zvpg"; // 使用应用专用密码
    private static final String SENDER_EMAIL ="<EMAIL>"; // "<EMAIL>"; //
    private static final String ATTACHMENT_NAME_CONTAINS = "实名审核信息_";
    private static final String SUBJECT_KEYWORD = "登录全球付商户服务平台";
    private static final String TEMP_DIR = "./storage"; // 临时目录
    private static final String CODE_REGEX = "\\b\\d{6}\\b"; // 验证码正则表达式

    public void checkForNewMessages() throws Exception {
        Session session = getSession();


        Store store = session.getStore("imap");
        store.connect(IMAP_HOST, USERNAME, PASSWORD);

        Folder inbox = store.getFolder("INBOX");
        inbox.open(Folder.READ_ONLY);

        SearchTerm fromSender = new FromStringTerm(SENDER_EMAIL);
        Date today = new Date();
        SearchTerm receivedToday = new ReceivedDateTerm(ReceivedDateTerm.EQ, today);
        SearchTerm searchTerm = new AndTerm(fromSender, receivedToday);
        Message[] messages = inbox.search(searchTerm);

        for (Message message : messages) {
            log.info("Subject: " + message.getSubject());
            if (message.isMimeType("multipart/mixed")) {
                MimeMultipart multipart = (MimeMultipart) message.getContent();
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                        String fileName = bodyPart.getFileName();
                        if (fileName != null && fileName.contains(ATTACHMENT_NAME_CONTAINS)) {
                            File tempFile = new File(TEMP_DIR, fileName);
                            try (InputStream inputStream = bodyPart.getInputStream();
                                 FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                                byte[] buffer = new byte[1024];
                                int bytesRead;
                                while ((bytesRead = inputStream.read(buffer)) != -1) {
                                    fileOutputStream.write(buffer, 0, bytesRead);
                                }
                                log.info(fileName);
                            }

                            // Read the Excel file
                            readExcelFile(tempFile.getAbsolutePath());
                        }
                    }
                }
            }
        }
        inbox.close(false);
        store.close();
    }

    private void readExcelFile(String filePath) throws IOException {
        FileInputStream fis = new FileInputStream(filePath);
        Workbook workbook = new XSSFWorkbook(fis);
        Sheet sheet = workbook.getSheetAt(0);
        Row headerRow = sheet.getRow(0);
        int resultIndex = -1;
        int folderIndex = -1;
        for (Cell cell : headerRow) {
            String header = cell.getStringCellValue();
            if ("结果".equals(header)) {
                resultIndex = cell.getColumnIndex();
            } else if ("提交文件夹".equals(header)) {
                folderIndex = cell.getColumnIndex();
            }
        }
        if (resultIndex == -1 || folderIndex == -1) {
            throw new IllegalArgumentException("Excel file does not contain the required headers.");
        }

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue; // Skip header row
            Cell resultCell = row.getCell(resultIndex);
            Cell folderCell = row.getCell(folderIndex);
            if (resultCell != null && folderCell != null) {
                String result = resultCell.getStringCellValue();
                String folder = folderCell.getStringCellValue();
                String substring = folder.substring(0, folder.length() - 4);
                RMap<String, String> booleanStringHashMap = redissonClient.getMap(substring);
                if ("审核通过".equals(result) && !booleanStringHashMap.isEmpty()) {
                    String cardNumber = booleanStringHashMap.get("card_number");
                    String cardHolderId = booleanStringHashMap.get("card_holder");
                    cardHolderService.updateStatus(Integer.valueOf(cardHolderId), 1);
                    userCardMapper.updateStatus(cardNumber);
                    UserCard oneByCardNumber = userCardMapper.getOneByCardNumber(cardNumber);
                    //  0 待处理 1 处理中 2 驳回请求 3 处理成功 4 处理失败
                    cardApplyOrderService.updateStatus(oneByCardNumber,3);
                    booleanStringHashMap.delete();
                } else {
                    if (!booleanStringHashMap.isEmpty()) {
                        String cardHolderId = booleanStringHashMap.get("card_holder");
                        String cardNumber = booleanStringHashMap.get("card_number");
                        UserCard oneByCardNumber = userCardMapper.getOneByCardNumber(cardNumber);
                        //  0 待处理 1 处理中 2 驳回请求 3 处理成功 4 处理失败
                        cardApplyOrderService.updateStatus(oneByCardNumber,4);
                        // 0未审核/ 1审核通过/ 2 已驳回
                        cardHolderService.updateStatus(Integer.valueOf(cardHolderId), 2);
                        booleanStringHashMap.delete();
                    }
                }
            }
        }
    }

    private static Session getSession() {
        Properties properties = new Properties();
        properties.put("mail.imap.host", IMAP_HOST);
        properties.put("mail.imap.port", "993");
        properties.put("mail.imap.ssl.enable", "true");

        return Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(USERNAME, PASSWORD);
            }
        });
    }

    public void checkCodeMessages() {
        System.setProperty("https.protocols", "TLSv1.2");
        Session session = getSession();
        try (Store store = session.getStore("imaps")) {
            store.connect(IMAP_HOST, USERNAME, PASSWORD);
            try (Folder inbox = store.getFolder("INBOX")) {
                inbox.open(Folder.READ_ONLY);
                Message[] messages = searchMessages(inbox);
                if (messages.length > 0) {
                    Message latestMessage = getLatestMessage(messages);
                    logMessageDetails(latestMessage);
                } else {
                    log.info("没有找到符合条件的邮件。");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Message[] searchMessages(Folder inbox) throws Exception {
        SearchTerm fromSender = new FromStringTerm(SENDER_EMAIL);
        Date today = new Date();
        SearchTerm receivedToday = new ReceivedDateTerm(ReceivedDateTerm.EQ, today);
        SearchTerm searchTerm = new AndTerm(fromSender, receivedToday);
        return inbox.search(searchTerm);
    }

    private Message getLatestMessage(Message[] messages) throws Exception {
        // 按日期排序，确保获取最新的邮件
        Arrays.sort(messages, (m1, m2) -> {
            try {
                return m2.getSentDate().compareTo(m1.getSentDate());
            } catch (MessagingException e) {
                return 0;
            }
        });
        return messages[0]; // 返回最新邮件
    }

    private void logMessageDetails(Message message) throws Exception {
        String emailContent = getEmailContent(message);
        extractAndLogCode(emailContent);
    }

    private void extractAndLogCode(String content) {
        Matcher matcher = Pattern.compile(CODE_REGEX).matcher(content);
        if (matcher.find()) {
            String code = matcher.group();
            RBucket<String> codeCache = redissonClient.getBucket("qxf_code");
            codeCache.set(code);
            codeCache.expire(Duration.ofMinutes(5));
            log.info("验证码{}", code);
        } else {
            log.info("未找到验证码。");
        }
    }

    private String getEmailContent(Message message) throws Exception {
        StringBuilder contentBuilder = new StringBuilder();
        if (message.isMimeType("text/plain") || message.isMimeType("text/html")) {
            contentBuilder.append(message.getContent().toString());
        } else if (message.getContent() instanceof MimeMultipart) {
            MimeMultipart multipart = (MimeMultipart) message.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                contentBuilder.append(getContentFromBodyPart(bodyPart));
            }
        }
        return contentBuilder.toString();
    }

    private String getContentFromBodyPart(BodyPart bodyPart) throws Exception {
        if (bodyPart.isMimeType("text/plain") || bodyPart.isMimeType("text/html")) {
            return getTextFromBodyPart(bodyPart);
        } else if (bodyPart.getContent() instanceof MimeMultipart) {
            MimeMultipart innerMultipart = (MimeMultipart) bodyPart.getContent();
            StringBuilder innerContent = new StringBuilder();
            for (int j = 0; j < innerMultipart.getCount(); j++) {
                BodyPart innerBodyPart = innerMultipart.getBodyPart(j);
                innerContent.append(getContentFromBodyPart(innerBodyPart));
            }
            return innerContent.toString();
        } else {
            System.out.println("不支持的BodyPart类型: " + bodyPart.getContentType());
            return "";
        }
    }

    private String getTextFromBodyPart(BodyPart bodyPart) throws Exception {
        StringBuilder textBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(bodyPart.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                textBuilder.append(line).append("\n");
            }
        }
        return textBuilder.toString();
    }
}