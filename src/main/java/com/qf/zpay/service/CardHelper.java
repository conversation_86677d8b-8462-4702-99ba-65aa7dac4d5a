package com.qf.zpay.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.constants.CardTransTypeEnum;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.common.CardProductDto;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.ChannelConfig;
import com.qf.zpay.service.pojo.RechargeMoneyAndFee;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.*;
import generator.mapper.ReqLogMapper;
import generator.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 09:36
 */
@Service
@Slf4j
public class CardHelper {

    private String ZNET = "ZNET";
    private String NOTICE_TYPE = "ZNET-CARD";

    @Autowired
    protected RedissonClient redis;

    @Autowired
    private ReqLogMapper reqLogMapper;

    @Autowired
    private CardActionLogService cardActionLogService;

    @Autowired
    UserService userService;

    @Autowired
    UserWalletService userWalletService;

    @Autowired
    UserCardManageService cardManageService;

    @Autowired
    UserCardService userCardService;

    @Autowired
    UserCardLogService userCardLogService;

    @Autowired
    UserWalletLogService userWalletLogService;

    @Autowired
    CardApplyOrderService cardApplyOrderService;

    @Autowired
    CardFactory cardFactory;

    @Resource
    private CardTransFeeLogService cardTransFeeLogService;
    @Resource
    private UserWalletAdvanceLogService userWalletAdvanceLogService;

    public Integer logReq2db(String channel, String url, Object header, String method, JSONObject body) {
        ReqLog reqLog = new ReqLog();
        reqLog.setChannel(channel);
        reqLog.setReqUrl(url);
        reqLog.setReqHeader(header.toString());
        reqLog.setReqMethod(method);
        if (body != null) {
            reqLog.setReqBody("");
        }
        reqLog.setReqTime(new Date());
        reqLogMapper.insert(reqLog);
        return reqLog.getId();
    }

    public void logReqOk(Integer id, Object header, Object body) {
        ReqLog reqLog = reqLogMapper.selectById(id);
        reqLog.setResBody(body.toString());
        reqLog.setResHeader(header.toString());
        reqLog.setResTime(new Date());
        reqLog.setStatus(1);
        reqLogMapper.updateById(reqLog);
    }

    /**
     * 未扣款金额
     *
     * @param userCard
     * @return
     */
    public BigDecimal computeDeductAmountV2(UserCard userCard, BigDecimal rechargeMoney) {
        // 计算向上游充值的钱
        // 能扣多少
        CardTransFeeLog cardTransFee = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                .orderByDesc(CardTransFeeLog::getCreateTime)
                .last("limit 1"));
        BigDecimal result = rechargeMoney;
        if (cardTransFee != null) {
            BigDecimal amount = cardTransFee.getAccruingAmounts();
            // 向上游充值的钱
            result = rechargeMoney.add(amount);
        }
        // =============================================================================================================

        UserWalletAdvanceLog advanceLog = userWalletAdvanceLogService.getOne(new LambdaQueryWrapper<UserWalletAdvanceLog>()
                .eq(UserWalletAdvanceLog::getCardId, userCard.getCardId())
                .orderByDesc(UserWalletAdvanceLog::getCreateTime)
                .last("limit 1"));
        if (advanceLog != null) {
            BigDecimal cardAmount = advanceLog.getAccruingAmounts();
            if (cardAmount.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal changeAmount = rechargeMoney;
                BigDecimal add = rechargeMoney.add(cardAmount);
                if (add.compareTo(BigDecimal.ZERO) >= 0) {
                    changeAmount = cardAmount.negate();
                }
                if (advanceLog.getAccruingAmounts().compareTo(BigDecimal.ZERO) < 0) {
                    if (cardTransFee != null) {
                        cardTransFee.setId(null);
                        cardTransFee.setChangeFee(result.compareTo(BigDecimal.ZERO) >= 0 ? cardTransFee.getAccruingAmounts().negate() : changeAmount);
                        cardTransFee.setAccruingAmounts(result.compareTo(BigDecimal.ZERO) >= 0 ? BigDecimal.ZERO : result);
                    }
                    advanceLog.setId(null);
                    advanceLog.setChangeFee(changeAmount);
                    advanceLog.setAccruingAmounts(add.compareTo(BigDecimal.ZERO) >= 0 ? BigDecimal.ZERO : add);
                    userWalletService.changeAmount(userCard.getUid(), changeAmount, userCard.getCardModel(), WalletTypeEnum.REFUND_THE_ADVANCE_FEE, advanceLog, cardTransFee);
                }
            }
        }
        return result;
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void openCardAfter(UserCard cardInfo) {
        Integer businessId = cardInfo.getUid();
        RLock lock = redis.getLock(String.format("bc_lock:%s", businessId));
        lock.lock(30, TimeUnit.SECONDS);
        try {
            CardProductDto cardProduct = cardManageService.getCardProductInfo(cardInfo.getCardManageId(), businessId);
            if (cardInfo.getUidChild() != null && cardInfo.getUidChild().equals(-1)) {
                // 集采卡片不收费
                cardProduct.setPlatformOpenCardFee(BigDecimal.ZERO);
                cardProduct.setCostOpenCardFee(BigDecimal.ZERO);
            }
            User userInfo = userService.getUserByBusinessId(businessId);
            UserWallet userWallet = userWalletService.getByBusinessId(businessId);
            CardModelEnum cardModel = cardInfo.getCardModel();
            // 1 扣减商户开卡费
            BigDecimal platformOpenCardFee = cardProduct.getPlatformOpenCardFee();
            BigDecimal businessOpenCardFee = cardProduct.getBusinessOpenCardFee();
            BigDecimal beforeMoney;
            String walletType; // 'main','store','usdt','token','share','physical','payment'
            switch (cardModel) {
                case RECHARGE:
                    walletType = "store";
                    beforeMoney = userWallet.getStoreWallet();
                    userWallet.setStoreWallet(userWallet.getStoreWallet().subtract(platformOpenCardFee));
                    break;
                case SHARE:
                    walletType = "share";
                    beforeMoney = userWallet.getShareWallet();
                    userWallet.setShareWallet(userWallet.getShareWallet().subtract(platformOpenCardFee));
                    break;
                case PHYSICAL:
                    walletType = "physical";
                    beforeMoney = userWallet.getPhysicalWallet();
                    userWallet.setPhysicalWallet(userWallet.getPhysicalWallet().subtract(platformOpenCardFee));
                    break;
                default:
                    throw new IllegalArgumentException("卡片模式错误");
            }
            userWalletService.updateById(userWallet);

            String orderId = ZHelperUtil.genOrderId("Z-CO");
            // 2 记录钱包日志
            // 31开卡费 32划转充值 33充值手续费 33销卡返回 34 销卡手续
            // 41开卡费收入 43充值手续费收入 45销卡费收入
            List<UserWalletLog> userWalletLogList = new ArrayList<>(2);
            BigDecimal afterMoney = beforeMoney.subtract(businessOpenCardFee);
            UserWalletLog openCardFeeLog = new UserWalletLog();
            openCardFeeLog.setUserId(userInfo.getId());
            openCardFeeLog.setBusinessId(userInfo.getBusinessId());
            openCardFeeLog.setAction(31);
            openCardFeeLog.setWalletType(walletType);
            openCardFeeLog.setChangeBefore(beforeMoney);
            openCardFeeLog.setChangeMoney(businessOpenCardFee.negate());
            openCardFeeLog.setChangeAfter(afterMoney);
            openCardFeeLog.setNote(String.format("扣减开卡费：%s", businessOpenCardFee));
            openCardFeeLog.setOrderId(orderId);
            userWalletLogList.add(openCardFeeLog);

            BigDecimal feeIn = businessOpenCardFee.subtract(platformOpenCardFee);
            UserWalletLog openCardFeeInLog = new UserWalletLog();
            openCardFeeInLog.setUserId(userInfo.getId());
            openCardFeeInLog.setBusinessId(userInfo.getBusinessId());
            openCardFeeInLog.setAction(41);
            openCardFeeInLog.setWalletType(walletType);
            openCardFeeInLog.setChangeBefore(afterMoney);
            openCardFeeInLog.setChangeMoney(feeIn);
            openCardFeeInLog.setChangeAfter(afterMoney.add(feeIn));
            openCardFeeInLog.setNote(String.format("开卡费收入：%s", feeIn));
            openCardFeeInLog.setOrderId(orderId);
            userWalletLogList.add(openCardFeeInLog);

            userWalletLogService.saveBatch(userWalletLogList);

            // 3 记录卡片日志
            UserCardLog userCardLog = new UserCardLog();
            userCardLog.setBusinessId(businessId);
            userCardLog.setNoticeType(NOTICE_TYPE);
            userCardLog.setAuthAmountCurrency("USD");
            userCardLog.setAuthAmount(businessOpenCardFee);
            userCardLog.setCardId(cardInfo.getCardId());
            userCardLog.setProductCode(cardInfo.getProductCode());
            userCardLog.setMaskCardNumber(cardInfo.getCardNumber());
            userCardLog.setCardModel(cardInfo.getCardModel());
            userCardLog.setStatus("Settled");
            userCardLog.setFundsDirection("Nochange");
            userCardLog.setTransactionType("OpenCardFee");
            userCardLog.setNote(String.format("开卡费：%s", businessOpenCardFee));
            userCardLog.setOrderId(orderId);
            userCardLog.setMerchantName(ZNET);
            userCardLog.setCreateDate(new Date());
            userCardLog.setCreateTime(new Date());
            userCardLog.setAvailableCredit(cardInfo.getAmount());
            userCardLogService.save(userCardLog);

            // 4 记录卡片操作日志
            CardActionLog cardActionLog = new CardActionLog();
            cardActionLog.setType(1);
            cardActionLog.setMoney(businessOpenCardFee);
            cardActionLog.setCardId(cardInfo.getCardId());
            cardActionLog.setBusinessId(businessId);
            cardActionLog.setCostFee(cardProduct.getCostOpenCardFee());
            cardActionLog.setPlatformFee(platformOpenCardFee);
            cardActionLog.setPlatformEarn(platformOpenCardFee.subtract(cardProduct.getCostOpenCardFee()));
            cardActionLog.setBusinessFee(businessOpenCardFee);
            cardActionLog.setBusinessEarn(businessOpenCardFee.subtract(platformOpenCardFee));
            cardActionLog.setCreateDate(new Date());
            cardActionLog.setOrderId(orderId);
            cardActionLog.setCardModel(cardInfo.getCardModel());
            cardActionLog.setStatus("success");
            cardActionLogService.save(cardActionLog);

            if (CardModelEnum.SHARE.equals(cardModel)) {
                changeBusinessShareCardAmount(businessId, BigDecimal.ZERO.subtract(platformOpenCardFee), null);
            }

            if (ChannelConfig.MUST_APPLY.contains(cardInfo.getChannel().getCode())) {
                CardApplyOrder apply = new CardApplyOrder();
                apply.setBusinessId(businessId);
                apply.setOrderId(orderId);
                apply.setCardId(cardInfo.getCardId());
                apply.setCardNumber(cardInfo.getCardNumber());
                apply.setAction(1);
                apply.setFee(businessOpenCardFee);
                apply.setStatus(0);
                apply.setChannel(cardInfo.getChannel());
                cardApplyOrderService.save(apply);
            }
        } catch (Exception e) {
            log.error("开卡后操作失败: cardId: {}", cardInfo.getCardId());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void cardRechargeAfter(UserCard cardInfo, BigDecimal amount) {
        Integer businessId = cardInfo.getUid();
        RLock lock = redis.getLock(String.format("bc_lock:%s", businessId));
        lock.lock(30, TimeUnit.SECONDS);
        try {
            CardProductDto cardProduct = cardManageService.getCardProductInfo(cardInfo.getCardManageId(), businessId);
            User userInfo = userService.getUserByBusinessId(businessId);
            UserWallet userWallet = userWalletService.getByBusinessId(businessId);
            CardModelEnum cardModel = cardInfo.getCardModel();

            RechargeMoneyAndFee rechargeMoneyAndFee = calcRechargeMoney(cardProduct, amount);
            BigDecimal rechargeMoney = rechargeMoneyAndFee.getRechargeMoney();

            // 0 更新卡片余额
            BigDecimal realRechargeMoney = rechargeMoney;
            if (CardModelEnum.SHARE.equals(cardModel)) {
                cardInfo.setVirtualAmt(cardInfo.getVirtualAmt().add(rechargeMoney));

                if (cardInfo.getVirtualAmt().compareTo(userWallet.getShareWallet()) >= 0) {
                    realRechargeMoney = userWallet.getShareWallet().subtract(cardInfo.getAmount());
                }
            }
            cardInfo.setAmount(cardInfo.getAmount().add(realRechargeMoney));
            userCardService.updateById(cardInfo);

            // 1 扣减商户充值金额 + 充值手续费
            BigDecimal deductMoney = rechargeMoneyAndFee.getPlatformFee();
            if (!CardModelEnum.SHARE.equals(cardModel)) {
                deductMoney = amount;
            }
            deductMoney = deductMoney.subtract(rechargeMoneyAndFee.getBusinessEarn());
            BigDecimal beforeMoney;
            String walletType; // 'main','store','usdt','token','share','physical','payment'
            switch (cardModel) {
                case RECHARGE:
                    walletType = "store";
                    beforeMoney = userWallet.getStoreWallet();
                    userWallet.setStoreWallet(userWallet.getStoreWallet().subtract(deductMoney));
                    break;
                case SHARE:
                    walletType = "share";
                    beforeMoney = userWallet.getShareWallet();
                    userWallet.setShareWallet(userWallet.getShareWallet().subtract(deductMoney));
                    break;
                case PHYSICAL:
                    walletType = "physical";
                    beforeMoney = userWallet.getPhysicalWallet();
                    userWallet.setPhysicalWallet(userWallet.getPhysicalWallet().subtract(deductMoney));
                    break;
                default:
                    throw new IllegalArgumentException("卡片模式错误");
            }
            userWalletService.updateById(userWallet);

            // 2 写入钱包日志
            // 充值额度
            String orderId = ZHelperUtil.genOrderId("Z-CR");
            List<UserWalletLog> walletLogList = new ArrayList<>(3);
            BigDecimal afterMoney = beforeMoney;
            if (!CardModelEnum.SHARE.equals(cardModel)) {
                afterMoney = afterMoney.subtract(rechargeMoney);
                UserWalletLog rechargeLog = new UserWalletLog();
                rechargeLog.setUserId(userInfo.getId());
                rechargeLog.setBusinessId(userInfo.getBusinessId());
                rechargeLog.setAction(32);
                rechargeLog.setWalletType(walletType);
                rechargeLog.setChangeBefore(beforeMoney);
                rechargeLog.setChangeMoney(BigDecimal.ZERO.subtract(rechargeMoney));
                rechargeLog.setChangeAfter(afterMoney);
                rechargeLog.setNote(String.format("划转充值到卡片：%s", rechargeMoney));
                rechargeLog.setOrderId(orderId);
                walletLogList.add(rechargeLog);
            }
            // 充值手续费
            beforeMoney = afterMoney;
            afterMoney = beforeMoney.subtract(rechargeMoneyAndFee.getBusinessFee());
            UserWalletLog rechargeFeeLog = new UserWalletLog();
            rechargeFeeLog.setUserId(userInfo.getId());
            rechargeFeeLog.setBusinessId(userInfo.getBusinessId());
            rechargeFeeLog.setAction(33);
            rechargeFeeLog.setWalletType(walletType);
            rechargeFeeLog.setChangeBefore(beforeMoney);
            rechargeFeeLog.setChangeMoney(rechargeMoneyAndFee.getBusinessFee().negate());
            rechargeFeeLog.setChangeAfter(afterMoney);
            rechargeFeeLog.setNote(String.format("扣减充值手续费：%s", rechargeMoneyAndFee.getBusinessFee()));
            rechargeFeeLog.setOrderId(orderId);
            walletLogList.add(rechargeFeeLog);

            BigDecimal feeIn = rechargeMoneyAndFee.getBusinessEarn();
            UserWalletLog rechargeFeeInLog = new UserWalletLog();
            rechargeFeeInLog.setUserId(userInfo.getId());
            rechargeFeeInLog.setBusinessId(userInfo.getBusinessId());
            rechargeFeeInLog.setAction(43);
            rechargeFeeInLog.setWalletType(walletType);
            rechargeFeeInLog.setChangeBefore(afterMoney);
            rechargeFeeInLog.setChangeMoney(feeIn);
            rechargeFeeInLog.setChangeAfter(afterMoney.add(feeIn));
            rechargeFeeInLog.setNote(String.format("充值手续费收入：%s", feeIn));
            rechargeFeeInLog.setOrderId(orderId);
            walletLogList.add(rechargeFeeInLog);

            userWalletLogService.saveBatch(walletLogList);

            // 3 记录卡片日志
            List<UserCardLog> cardLogs = new ArrayList<>(2);
            UserCardLog rechargeLog = new UserCardLog();
            rechargeLog.setBusinessId(businessId);
            rechargeLog.setNoticeType(NOTICE_TYPE);
            rechargeLog.setAuthAmountCurrency("USD");
            rechargeLog.setAuthAmount(rechargeMoney);
            rechargeLog.setCardId(cardInfo.getCardId());
            rechargeLog.setProductCode(cardInfo.getProductCode());
            rechargeLog.setMaskCardNumber(cardInfo.getCardNumber());
            rechargeLog.setCardModel(cardInfo.getCardModel());
            rechargeLog.setStatus("Settled");
            rechargeLog.setFundsDirection("Income");
            rechargeLog.setTransactionType("CardRecharge");
            rechargeLog.setNote(String.format("充值金额：%s", rechargeMoney));
            rechargeLog.setOrderId(orderId);
            rechargeLog.setMerchantName(ZNET);
            rechargeLog.setCreateDate(new Date());
            rechargeLog.setCreateTime(new Date());
            rechargeLog.setAvailableCredit(cardInfo.getAmount());
            if (cardInfo.getCardModel().equals(CardModelEnum.SHARE)) {
                rechargeLog.setAvailableCredit(cardInfo.getVirtualAmt());
            }
            cardLogs.add(rechargeLog);

            UserCardLog feeLog = new UserCardLog();
            feeLog.setBusinessId(businessId);
            feeLog.setNoticeType(NOTICE_TYPE);
            feeLog.setAuthAmountCurrency("USD");
            feeLog.setAuthAmount(rechargeMoneyAndFee.getBusinessFee());
            feeLog.setCardId(cardInfo.getCardId());
            feeLog.setProductCode(cardInfo.getProductCode());
            feeLog.setMaskCardNumber(cardInfo.getCardNumber());
            feeLog.setCardModel(cardInfo.getCardModel());
            feeLog.setStatus("Settled");
            feeLog.setFundsDirection("Nochange");
            feeLog.setTransactionType("RechargeFee");
            feeLog.setNote(String.format("充值手续费：%s", rechargeMoneyAndFee.getBusinessFee()));
            feeLog.setOrderId(orderId);
            feeLog.setMerchantName(ZNET);
            feeLog.setCreateDate(new Date());
            feeLog.setCreateTime(new Date());
            feeLog.setAvailableCredit(cardInfo.getAmount());
            if (cardInfo.getCardModel().equals(CardModelEnum.SHARE)) {
                rechargeLog.setAvailableCredit(cardInfo.getVirtualAmt());
            }
            cardLogs.add(feeLog);
            userCardLogService.saveBatch(cardLogs);

            // 4 记录卡片操作日志
            BigDecimal costFee = amount.multiply(cardProduct.getCostChargeRate()).divide(BigDecimal.valueOf(100), 2, RoundingMode.CEILING);

            CardActionLog cardActionLog = new CardActionLog();
            cardActionLog.setType(2);
            cardActionLog.setMoney(amount);
            cardActionLog.setCardId(cardInfo.getCardId());
            cardActionLog.setBusinessId(cardInfo.getUid());
            cardActionLog.setCostRate(cardProduct.getCostChargeRate());
            cardActionLog.setCostFee(costFee);
            cardActionLog.setPlatformRate(cardProduct.getPlatformChargeRate());
            cardActionLog.setPlatformFee(rechargeMoneyAndFee.getPlatformFee());
            cardActionLog.setPlatformEarn(rechargeMoneyAndFee.getPlatformEarn());
            cardActionLog.setBusinessRate(cardProduct.getBusinessChargeRate());
            cardActionLog.setBusinessFee(rechargeMoneyAndFee.getBusinessFee());
            cardActionLog.setBusinessEarn(rechargeMoneyAndFee.getBusinessEarn());
            cardActionLog.setCreateDate(new Date());
            cardActionLog.setOrderId(orderId);
            cardActionLog.setCardModel(cardInfo.getCardModel());
            cardActionLog.setStatus("success");
            cardActionLogService.save(cardActionLog);

            if (ChannelConfig.MUST_APPLY.contains(cardInfo.getChannel().getCode())) {
                // 目前实体卡采用爬虫自动化进行充值，先不生成申请单
//                CardApplyOrder apply = new CardApplyOrder();
//                apply.setBusinessId(businessId);
//                apply.setOrderId(orderId);
//                apply.setCardId(cardInfo.getCardId());
//                apply.setCardNumber(cardInfo.getCardNumber());
//                apply.setAction(2);
//                apply.setAmount(amount);
//                apply.setFee(businessRechargeFee);
//                apply.setStatus(0);
//                apply.setChannel(cardInfo.getChannel());
//                cardApplyOrderService.save(apply);
            }

            if (CardModelEnum.SHARE.equals(cardModel)) {
                changeBusinessShareCardAmount(businessId, rechargeMoneyAndFee.getPlatformFee().negate(), null);
            }
        } catch (Exception e) {
            log.error("充值后操作失败: cardId: {}, amount: {}", cardInfo.getCardId(), amount);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Async
//    @Retryable(retryFor = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    @Transactional(rollbackFor = Exception.class)
    public void cardCancelAfter(UserCard cardInfo) {
        Integer businessId = cardInfo.getUid();
        RLock lock = redis.getLock(String.format("bc_lock:%s", businessId));
        lock.lock(30, TimeUnit.SECONDS);
        try {
            CardProductDto cardProduct = cardManageService.getCardProductInfo(cardInfo.getCardManageId(), cardInfo.getUid());
            User userInfo = userService.getUserByBusinessId(cardInfo.getUid());
            UserWallet userWallet = userWalletService.getByBusinessId(cardInfo.getUid());
            CardModelEnum cardModel = cardInfo.getCardModel();

            BigDecimal platformCancelCardFee = cardProduct.getPlatformCancelCardFee();
            BigDecimal businessCancelCardFee = cardProduct.getBusinessCancelCardFee();
            BigDecimal cardBalance = cardInfo.getAmount();

            // 0 更新卡片状态
            cardInfo.setCardStatus(CardStatusEnum.Cancel);
            if (cardBalance.compareTo(BigDecimal.ZERO) > 0) {
                cardInfo.setAmount(BigDecimal.ZERO);
            }
            cardInfo.setVirtualAmt(BigDecimal.ZERO);
            cardInfo.setCancelCardDate(new Date());
            cardInfo.setCancelCardTime(new Date());
            userCardService.updateById(cardInfo);

            // 1 扣减商户钱包余额 卡余额 - 平台销卡费
            BigDecimal deductMoney = BigDecimal.ZERO.subtract(platformCancelCardFee);
            if (!CardModelEnum.SHARE.equals(cardModel) && cardBalance.compareTo(BigDecimal.ZERO) > 0) {
                deductMoney = deductMoney.add(cardBalance);
            }
            String walletType;
            BigDecimal beforeMoney;
            switch (cardModel) {
                case RECHARGE:
                    walletType = "store";
                    beforeMoney = userWallet.getStoreWallet();
                    userWallet.setStoreWallet(userWallet.getStoreWallet().add(deductMoney));
                    break;
                case SHARE:
                    walletType = "share";
                    beforeMoney = userWallet.getShareWallet();
                    userWallet.setShareWallet(userWallet.getShareWallet().add(deductMoney));
                    break;
                case PHYSICAL:
                    walletType = "physical";
                    beforeMoney = userWallet.getPhysicalWallet();
                    userWallet.setPhysicalWallet(userWallet.getPhysicalWallet().add(deductMoney));
                    break;
                default:
                    throw new IllegalArgumentException("卡片模式错误");
            }
            userWalletService.updateById(userWallet);

            // 2 记录钱包变动日志
            List<UserWalletLog> walletLogs = new ArrayList<>(3);
            // 2.1 销卡费
            String orderId = ZHelperUtil.genOrderId("Z-CC");

            BigDecimal afterMoney = beforeMoney.subtract(businessCancelCardFee);
            UserWalletLog cancelFeeLog = new UserWalletLog();
            cancelFeeLog.setUserId(userInfo.getId());
            cancelFeeLog.setBusinessId(userInfo.getBusinessId());
            cancelFeeLog.setAction(35);
            cancelFeeLog.setWalletType(walletType);
            cancelFeeLog.setChangeBefore(beforeMoney);
            cancelFeeLog.setChangeMoney(businessCancelCardFee.negate());
            cancelFeeLog.setChangeAfter(afterMoney);
            cancelFeeLog.setNote(String.format("扣减销卡费：%s", businessCancelCardFee));
            cancelFeeLog.setOrderId(orderId);
            walletLogs.add(cancelFeeLog);

            BigDecimal feeIn = businessCancelCardFee.subtract(platformCancelCardFee);
            beforeMoney = afterMoney;
            afterMoney = beforeMoney.add(feeIn);
            UserWalletLog cancelFeeInLog = new UserWalletLog();
            cancelFeeInLog.setUserId(userInfo.getId());
            cancelFeeInLog.setBusinessId(userInfo.getBusinessId());
            cancelFeeInLog.setAction(45);
            cancelFeeInLog.setWalletType(walletType);
            cancelFeeInLog.setChangeBefore(afterMoney);
            cancelFeeInLog.setChangeMoney(feeIn);
            cancelFeeInLog.setChangeAfter(afterMoney);
            cancelFeeInLog.setNote(String.format("销卡费收入：%s", feeIn));
            cancelFeeInLog.setOrderId(orderId);
            walletLogs.add(cancelFeeInLog);

            // 2.2 卡余额
            if (!CardModelEnum.SHARE.equals(cardModel) && cardBalance.compareTo(BigDecimal.ZERO) > 0) {
                UserWalletLog cardBalanceLog = new UserWalletLog();
                cardBalanceLog.setUserId(userInfo.getId());
                cardBalanceLog.setBusinessId(userInfo.getBusinessId());
                cardBalanceLog.setAction(34);
                cardBalanceLog.setWalletType(walletType);
                cardBalanceLog.setChangeBefore(afterMoney);
                cardBalanceLog.setChangeMoney(cardBalance);
                cardBalanceLog.setChangeAfter(afterMoney.add(cardBalance));
                cardBalanceLog.setNote(String.format("销卡余额返回：%s", cardBalance));
                cardBalanceLog.setOrderId(orderId);
                walletLogs.add(cardBalanceLog);
            }
            userWalletLogService.saveBatch(walletLogs);

            // 记录卡片日志
            List<UserCardLog> cardLogList = new ArrayList<>(2);
            if (cardBalance.compareTo(BigDecimal.ZERO) > 0) {
                UserCardLog cardBalanceLog = new UserCardLog();
                cardBalanceLog.setBusinessId(userInfo.getBusinessId());
                cardBalanceLog.setNoticeType(NOTICE_TYPE);
                cardBalanceLog.setAuthAmountCurrency("USD");
                cardBalanceLog.setAuthAmount(cardBalance.subtract(businessCancelCardFee));
                cardBalanceLog.setCardId(cardInfo.getCardId());
                cardBalanceLog.setProductCode(cardInfo.getProductCode());
                cardBalanceLog.setMaskCardNumber(cardInfo.getCardNumber());
                cardBalanceLog.setCardModel(cardInfo.getCardModel());
                cardBalanceLog.setStatus("Settled");
                cardBalanceLog.setFundsDirection("Expenditure");
                cardBalanceLog.setTransactionType(CardTransTypeEnum.DiscardRechargeReturn.getCode());
                cardBalanceLog.setNote(String.format("销卡余额返回：%s", cardBalance.subtract(businessCancelCardFee)));
                cardBalanceLog.setOrderId(orderId);
                cardBalanceLog.setMerchantName(ZNET);
                cardBalanceLog.setCreateDate(new Date());
                cardBalanceLog.setCreateTime(new Date());
                cardBalanceLog.setAvailableCredit(BigDecimal.ZERO);
                cardLogList.add(cardBalanceLog);
            }

            UserCardLog cancelCardFeeLog = new UserCardLog();
            cancelCardFeeLog.setBusinessId(userInfo.getBusinessId());
            cancelCardFeeLog.setNoticeType(NOTICE_TYPE);
            cancelCardFeeLog.setAuthAmountCurrency("USD");
            cancelCardFeeLog.setAuthAmount(businessCancelCardFee);
            cancelCardFeeLog.setCardId(cardInfo.getCardId());
            cancelCardFeeLog.setProductCode(cardInfo.getProductCode());
            cancelCardFeeLog.setMaskCardNumber(cardInfo.getCardNumber());
            cancelCardFeeLog.setCardModel(cardInfo.getCardModel());
            cancelCardFeeLog.setStatus("Settled");
            cancelCardFeeLog.setFundsDirection("Expenditure");
            cancelCardFeeLog.setTransactionType(CardTransTypeEnum.CancelCardFee.getCode());
            cancelCardFeeLog.setNote(String.format("销卡费：%s", businessCancelCardFee));
            cancelCardFeeLog.setOrderId(orderId);
            cancelCardFeeLog.setMerchantName(ZNET);
            cancelCardFeeLog.setCreateDate(new Date());
            cancelCardFeeLog.setCreateTime(new Date());
            cancelCardFeeLog.setAvailableCredit(BigDecimal.ZERO);
            cardLogList.add(cancelCardFeeLog);

            userCardLogService.saveBatch(cardLogList);

            // 3 记录卡片操作日志
            CardActionLog cardActionLog = new CardActionLog();
            cardActionLog.setType(3);
            cardActionLog.setMoney(businessCancelCardFee);
            cardActionLog.setCardId(cardInfo.getCardId());
            cardActionLog.setBusinessId(cardInfo.getUid());
            cardActionLog.setCostFee(cardProduct.getCostCancelCardFee());
            cardActionLog.setPlatformFee(platformCancelCardFee);
            cardActionLog.setPlatformEarn(platformCancelCardFee.subtract(cardProduct.getCostCancelCardFee()));
            cardActionLog.setBusinessFee(cardProduct.getBusinessCancelCardFee());
            cardActionLog.setBusinessEarn(businessCancelCardFee.subtract(platformCancelCardFee));
            cardActionLog.setCreateDate(new Date());
            cardActionLog.setOrderId(orderId);
            cardActionLog.setCardModel(cardInfo.getCardModel());
            cardActionLog.setStatus("success");
            cardActionLogService.save(cardActionLog);

            if (CardModelEnum.SHARE.equals(cardModel)) {
                changeBusinessShareCardAmount(userInfo.getBusinessId(), businessCancelCardFee.negate(), null);
            }
        } catch (Exception e) {
            log.error("销卡后处理失败 cardId: {}", cardInfo.getCardId());
            throw e;
        } finally {
            lock.unlock();
        }
    }


    @Async
    @Transactional(rollbackFor = Exception.class)
    public void cardWithdrawAfter(UserCard cardInfo, BigDecimal amount) {
        Integer businessId = cardInfo.getUid();
        RLock lock = redis.getLock(String.format("bc_lock:%s:%s", businessId, cardInfo.getCardId()));
        lock.lock(30, TimeUnit.SECONDS);
        try {
            User userInfo = userService.getUserByBusinessId(businessId);
            UserWallet userWallet = userWalletService.getByBusinessId(businessId);
            CardModelEnum cardModel = cardInfo.getCardModel();

            BigDecimal withdrawMoney = amount;
            //

            // 0 更新卡片余额
            cardInfo.setAmount(cardInfo.getAmount().subtract(withdrawMoney));
            userCardService.updateById(cardInfo);

            // 1 增加商户钱包余额
            BigDecimal beforeMoney;
            String walletType;
            switch (cardModel) {
                case RECHARGE:
                    walletType = "store";
                    beforeMoney = userWallet.getStoreWallet();
                    userWallet.setStoreWallet(userWallet.getStoreWallet().add(withdrawMoney));
                    break;
                case PHYSICAL:
                    walletType = "physical";
                    beforeMoney = userWallet.getPhysicalWallet();
                    userWallet.setPhysicalWallet(userWallet.getPhysicalWallet().add(withdrawMoney));
                    break;
                default:
                    throw new IllegalArgumentException("卡片模式错误");
            }
            userWalletService.updateById(userWallet);

            // 2 写入钱包日志
            // 提现金额
            String orderId = ZHelperUtil.genOrderId("Z-CW");
            List<UserWalletLog> walletLogList = new ArrayList<>(3);
            BigDecimal afterMoney = beforeMoney;
            afterMoney = afterMoney.add(withdrawMoney);
            UserWalletLog rechargeLog = new UserWalletLog();
            rechargeLog.setUserId(userInfo.getId());
            rechargeLog.setBusinessId(userInfo.getBusinessId());
            rechargeLog.setAction(36);
            rechargeLog.setWalletType(walletType);
            rechargeLog.setChangeBefore(beforeMoney);
            rechargeLog.setChangeMoney(withdrawMoney);
            rechargeLog.setChangeAfter(afterMoney);
            rechargeLog.setNote(String.format("卡片提出：%s", withdrawMoney));
            rechargeLog.setOrderId(orderId);
            walletLogList.add(rechargeLog);
//            // 充值手续费
//            beforeMoney = afterMoney;
//            afterMoney = beforeMoney.subtract(rechargeMoneyAndFee.getBusinessFee());
//            UserWalletLog rechargeFeeLog = new UserWalletLog();
//            rechargeFeeLog.setUserId(userInfo.getId());
//            rechargeFeeLog.setBusinessId(userInfo.getBusinessId());
//            rechargeFeeLog.setAction(33);
//            rechargeFeeLog.setWalletType(walletType);
//            rechargeFeeLog.setChangeBefore(beforeMoney);
//            rechargeFeeLog.setChangeMoney(rechargeMoneyAndFee.getBusinessFee().negate());
//            rechargeFeeLog.setChangeAfter(afterMoney);
//            rechargeFeeLog.setNote(String.format("扣减充值手续费：%s", rechargeMoneyAndFee.getBusinessFee()));
//            rechargeFeeLog.setOrderId(orderId);
//            walletLogList.add(rechargeFeeLog);
//
//            BigDecimal feeIn = rechargeMoneyAndFee.getBusinessEarn();
//            UserWalletLog rechargeFeeInLog = new UserWalletLog();
//            rechargeFeeInLog.setUserId(userInfo.getId());
//            rechargeFeeInLog.setBusinessId(userInfo.getBusinessId());
//            rechargeFeeInLog.setAction(43);
//            rechargeFeeInLog.setWalletType(walletType);
//            rechargeFeeInLog.setChangeBefore(afterMoney);
//            rechargeFeeInLog.setChangeMoney(feeIn);
//            rechargeFeeInLog.setChangeAfter(afterMoney.add(feeIn));
//            rechargeFeeInLog.setNote(String.format("充值手续费收入：%s", feeIn));
//            rechargeFeeInLog.setOrderId(orderId);
//            walletLogList.add(rechargeFeeInLog);

            userWalletLogService.saveBatch(walletLogList);

            // 3 记录卡片日志
            List<UserCardLog> cardLogs = new ArrayList<>(2);
            UserCardLog wdLog = new UserCardLog();
            wdLog.setBusinessId(businessId);
            wdLog.setNoticeType(NOTICE_TYPE);
            wdLog.setAuthAmountCurrency("USD");
            wdLog.setAuthAmount(withdrawMoney);
            wdLog.setCardId(cardInfo.getCardId());
            wdLog.setProductCode(cardInfo.getProductCode());
            wdLog.setMaskCardNumber(cardInfo.getCardNumber());
            wdLog.setCardModel(cardInfo.getCardModel());
            wdLog.setStatus("Settled");
            wdLog.setFundsDirection("Expenditure");
            wdLog.setTransactionType("CardWithdraw");
            wdLog.setNote(String.format("提出金额：%s", withdrawMoney));
            wdLog.setOrderId(orderId);
            wdLog.setMerchantName(ZNET);
            wdLog.setCreateDate(new Date());
            wdLog.setCreateTime(new Date());
            wdLog.setAvailableCredit(cardInfo.getAmount());
            cardLogs.add(wdLog);

//            UserCardLog feeLog = new UserCardLog();
//            feeLog.setBusinessId(businessId);
//            feeLog.setNoticeType(NOTICE_TYPE);
//            feeLog.setAuthAmountCurrency("USD");
//            feeLog.setAuthAmount(rechargeMoneyAndFee.getBusinessFee());
//            feeLog.setCardId(cardInfo.getCardId());
//            feeLog.setProductCode(cardInfo.getProductCode());
//            feeLog.setMaskCardNumber(cardInfo.getCardNumber());
//            feeLog.setCardModel(cardInfo.getCardModel());
//            feeLog.setStatus("Settled");
//            feeLog.setFundsDirection("Nochange");
//            feeLog.setTransactionType("RechargeFee");
//            feeLog.setNote(String.format("充值手续费：%s", rechargeMoneyAndFee.getBusinessFee()));
//            feeLog.setOrderId(orderId);
//            feeLog.setMerchantName(ZNET);
//            feeLog.setCreateDate(new Date());
//            feeLog.setCreateTime(new Date());
//            feeLog.setAvailableCredit(cardInfo.getAmount());
//            cardLogs.add(feeLog);
            userCardLogService.saveBatch(cardLogs);

            // 4 记录卡片操作日志

            CardActionLog cardActionLog = new CardActionLog();
            cardActionLog.setType(4);
            cardActionLog.setMoney(amount);
            cardActionLog.setCardId(cardInfo.getCardId());
            cardActionLog.setBusinessId(cardInfo.getUid());
            cardActionLog.setCostRate(BigDecimal.ZERO);
            cardActionLog.setCostFee(BigDecimal.ZERO);
            cardActionLog.setPlatformRate(BigDecimal.ZERO);
            cardActionLog.setPlatformFee(BigDecimal.ZERO);
            cardActionLog.setPlatformEarn(BigDecimal.ZERO);
            cardActionLog.setBusinessRate(BigDecimal.ZERO);
            cardActionLog.setBusinessFee(BigDecimal.ZERO);
            cardActionLog.setBusinessEarn(BigDecimal.ZERO);
            cardActionLog.setCreateDate(new Date());
            cardActionLog.setOrderId(orderId);
            cardActionLog.setCardModel(cardInfo.getCardModel());
            cardActionLog.setStatus("success");
            cardActionLogService.save(cardActionLog);

        } catch (Exception e) {
            log.error("提现后操作失败: cardId: {}, amount: {}", cardInfo.getCardId(), amount);
            throw e;
        } finally {
            lock.unlock();
        }
    }


    /**
     * 修改商户伞下共享卡真实余额
     * 在商户共享卡钱包发生变化时触发（划转到共享钱包；共享卡开卡、充值、销卡）
     *
     * @param businessId  商户ID
     * @param changeMoney 变动金额 正数为增加，负数为减少
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void changeBusinessShareCardAmount(Integer businessId, BigDecimal changeMoney, String cardId) {
        if (changeMoney.equals(BigDecimal.ZERO)) {
            return;
        }
        try {
            UserWallet userWallet = userWalletService.getByBusinessId(businessId);
            BigDecimal shareBalance = userWallet.getShareWallet();
            List<UserCard> shareCardList = userCardService.getUserShareCardList(businessId);
            for (UserCard userCard : shareCardList) {
                if (userCard.getCardId().equals(cardId)) {
                    continue;
                }
                if (userCard.getCardStatus().equals(CardStatusEnum.Cancel)
                        || userCard.getCardStatus().equals(CardStatusEnum.Expired)) {
                    continue;
                }
                try {
                    BigDecimal rechargeMoney = BigDecimal.ZERO;

                    if (userCard.getVirtualAmt().compareTo(shareBalance) >= 0) {
                        rechargeMoney = shareBalance.subtract(userCard.getAmount());
                    } else {
                        // 账面 < 钱包
                        if (changeMoney.signum() == 1) {
                            // 正数，增加
                            rechargeMoney = userCard.getVirtualAmt().subtract(userCard.getAmount());
                        }
                    }

                    if (!rechargeMoney.equals(BigDecimal.ZERO)) {
                        cardFactory.getBuilder(userCard.getChannel());
                        Boolean res = cardFactory.recharge(userCard.getCardId(), rechargeMoney);
                        if (!res) {
                            log.error("修改卡余额失败 cardId: {}", userCard.getCardId());
                        }
                        userCard.setAmount(userCard.getAmount().add(rechargeMoney));
                        userCardService.updateById(userCard);
                    }

                } catch (Exception e) {
                    log.error("操作卡片失败 cardId: {}", userCard.getCardId());
                }
            }
        } catch (Exception e) {
            log.error("修改商户伞下共享卡真实余额失败 businessId: {}", businessId);
            throw e;
        }
    }


    public static UserCard filterCardInfo(UserCard cardInfo) {
        if (cardInfo.getStatus().equals(1)) {
            return cardInfo;
        } else {
            UserCard userCard = new UserCard();
            userCard.setCardModel(cardInfo.getCardModel());
            userCard.setCardScheme(cardInfo.getCardScheme());
            userCard.setCardId(cardInfo.getCardId());
            userCard.setStatus(cardInfo.getStatus());
            return userCard;
        }
    }


    public RechargeMoneyAndFee calcRechargeMoney(CardProductDto cardProduct, BigDecimal amount) {
        RechargeMoneyAndFee rechargeMoneyAndFee = new RechargeMoneyAndFee();
        rechargeMoneyAndFee.setMoney(amount);

        BigDecimal platformFeeRate = cardProduct.getPlatformChargeRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_EVEN);
        rechargeMoneyAndFee.setPlatformFeeRate(platformFeeRate);

        BigDecimal platformFee = amount.multiply(platformFeeRate).setScale(2, RoundingMode.CEILING);
        rechargeMoneyAndFee.setPlatformFee(platformFee);

        BigDecimal businessFeeRate = cardProduct.getBusinessChargeRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_EVEN);
        rechargeMoneyAndFee.setBusinessFeeRate(businessFeeRate);

        BigDecimal businessFee = amount.multiply(businessFeeRate).setScale(2, RoundingMode.CEILING);
        rechargeMoneyAndFee.setBusinessFee(businessFee);

        BigDecimal rechargeMoney = amount.subtract(businessFee);
        rechargeMoneyAndFee.setRechargeMoney(rechargeMoney);

        BigDecimal costFeeRate = cardProduct.getCostChargeRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_EVEN);
        rechargeMoneyAndFee.setCostFeeRate(costFeeRate);

        BigDecimal costFee = amount.multiply(costFeeRate).setScale(2, RoundingMode.CEILING);
        rechargeMoneyAndFee.setCostFee(costFee);

        rechargeMoneyAndFee.setBusinessEarn(businessFee.subtract(platformFee));

        rechargeMoneyAndFee.setPlatformEarn(platformFee.subtract(costFee));

        return rechargeMoneyAndFee;
    }


    public void deductCardMonthFee(UserCard userCard) {
        // 扣减月费

    }

}
