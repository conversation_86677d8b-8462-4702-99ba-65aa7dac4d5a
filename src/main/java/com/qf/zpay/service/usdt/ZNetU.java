package com.qf.zpay.service.usdt;

import cn.hutool.core.lang.Console;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.util.ZHelperUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.enums.ReadyState;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.net.URI;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;

import static javax.management.remote.JMXConnectorFactory.connect;

/**
 * <AUTHOR>
 * @updateTime 2024/7/17 18:04
 */
@Component
@Slf4j
public class ZNetU {

    @Value("${ZNetU.domain}")
    private String domain;

    @Value("${ZNetU.clientId}")
    private String clientId;


    private HashMap<String, String> headers = new HashMap<>();

    @Autowired
    ZHelperUtil zHelperUtil;

    private final RsaService rsaService;
    private final RedissonClient redis;
    private final ZNetUWebSocket wsClient;


    public ZNetU(RedissonClient redis, RsaService rsaService, ZNetUWebSocket wsClient) {
        this.redis = redis;
        this.rsaService = rsaService;
        this.wsClient = wsClient;
        headers.put("Content-Type", "application/json");
        getToken();
    }

    public JSONObject genAddress() {
        return sendReq("user/generate", null);
    }

    public boolean withdraw(Integer coinType, String address, BigDecimal amount, String flag) {
        JSONObject data = new JSONObject();
        data.putOnce("amount", amount.toString());
        data.putOnce("address", address);
        data.putOnce("coin_type", coinType);
        data.putOnce("user", flag);
        sendReq("user/withdraw", data);
        return true;
    }


    @PostConstruct
    private void listen() {
        wsClient.addHeader("token", headers.get("token"));
        wsClient.connect();
    }


    private void getToken() {
        String flag = this.getClass().getSimpleName().toLowerCase();
        RBucket<String> tokenBucket = redis.getBucket("token:" + flag);
        if (!tokenBucket.isExists()) {
            RSA rsa = rsaService.getRsaPair(flag);
            JWTPayload payload = new JWTPayload();
            payload.setPayload("uid", clientId);
            JWTSigner signer = JWTSignerUtil.rs256(rsa.getPrivateKey());

            Duration duration = Duration.ofDays(100);
            String token = JWT.create()
                    .setPayload("uid", clientId)
                    .setSigner(signer)
                    .setIssuedAt(new Date())
                    .setNotBefore(new Date())
                    .setExpiresAt(Date.from(new Date().toInstant().plus(duration)))
                    .sign();
            tokenBucket.set(token);
            tokenBucket.expire(duration);
        }
        headers.put("token", tokenBucket.get());
    }

    protected JSONObject sendReq(String path, JSONObject body) {
        String url = UrlBuilder.of(domain)
                .addPath(path)
                .addQuery("client_id", clientId)
                .build();
        JSONObject resObj = zHelperUtil.sendReq(url, Method.POST, headers, body, this.getClass().getSimpleName());
        if (!resObj.getInt("code").equals(200)) {
            log.error("ZNetU服务报错：请求：{}，响应：{}", body, resObj);
            Assert.fail(ResultCode.FAILED);
        }
        return resObj.getJSONObject("data");
    }

}
