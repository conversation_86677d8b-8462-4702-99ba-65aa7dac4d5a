package com.qf.zpay.service.usdt;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import generator.domain.CoinInOut;
import generator.service.CoinInOutService;
import generator.service.RechargeService;
import generator.service.UserWalletService;
import lombok.extern.log4j.Log4j2;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @updateTime 2024/7/18 17:08
 */
@Log4j2
@Component
public class ZNetUWebSocket extends WebSocketClient {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    private static final int MAX_RECONNECT_ATTEMPTS = 5;
    private int reconnectAttempts = 0;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();


    public ZNetUWebSocket(
            @Value("${ZNetU.wss}") String wss,
            @Value("${ZNetU.clientId}") String clientId
    ) {
        super(URI.create(wss + "?client_id=" + clientId));
    }

    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info(">>>> ZNetU WS 连接成功 ^_^  {}", this.getURI());
    }


    @Autowired
    UserWalletService userWalletService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    CoinInOutService coinInOutService;


    @Override
    public void onMessage(String s) {
        //"type":1,
        // "admin_id":1000000,
        // "data":
        // [{"cointype":195,
        // "tx":"1597f8e931e3fc23bc13cee34e537202dccf368b6a4a139b8a227a654f6bc56c",
        // "from":"TKsN5cgEbqYqtKXMwTaDKZjpsv5oUhyqs8",
        // "to":"TN99juNgWbH7yYY2gBKx6qC5Vqf6nTmGwS",
        // "amount":"100000000",
        // "decimal":6,"symbol":"USDT",
        // "contract":"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
        // "height":63283889,
        // "timestamp":1720496613000}]
        log.info(">>>> ZNetU WS 收到消息: {}", s);
        JSONObject obj = JSONUtil.parseObj(s);
        JSONArray dataList = obj.getJSONArray("data");

        JSONArray ackList = new JSONArray();
        for (Object item : dataList) {
            try {
                JSONObject data = JSONUtil.parseObj(item);
                switch (obj.getInt("type")) {
                    case 1:
                        // 充值成功
                        coinInOutService.rechargeNow(data);
                        break;
                    case 2:
                        // 提现被拒
                        break;
                    case 3:
                        // 提现成功
                        CoinInOut coinInOut = coinInOutService.getById(data.getInt("user_name"));
                        coinInOut.setStatus(9);
                        coinInOutService.updateById(coinInOut);
                        break;
                    default:
                        break;

                }

                ackList.put(data.getStr("tx"));
            } catch (Exception e) {
                log.error("处理失败：data: {}", item, e);
            }
        }
        JSONObject ret = new JSONObject();
        ret.putOnce("type", "confirm");
        ret.putOnce("tx", ackList);
        this.send(ret.toString());
    }

    @Override
    public void onClose(int i, String reason, boolean b) {
        String closeBy = b ? "远端关闭" : "本地关闭";
        log.error(">>>> ZNetU WS onClose === 由: {} 状态码:{} 原因: {} ", closeBy, i, reason);
        if (b) {
            if (List.of("prod", "prew").contains(activeProfile)) {
                this.attemptReconnect();
            }
        }
    }

    @Override
    public void onError(Exception e) {
        log.error(">>>> ZNetU WS onError: {}", e.getMessage(), e);
    }


    private void attemptReconnect() {
        executorService.submit(() -> {
            try {
                long backoffTime = (long) Math.pow(2, reconnectAttempts) * 5000;
                Thread.sleep(backoffTime);
                log.info("尝试第 {} 次重连，将在 {} 秒后重试", reconnectAttempts + 1, backoffTime / 1000);
                reconnectBlocking();
                log.info("已重连，开始检测连接状态");
                boolean isOpen = this.getConnection().isOpen();
                if (isOpen) {
                    reconnectAttempts = 0;
                    log.info("恭喜，重连成功 ^_^");
                } else {
                    throw new RuntimeException("连接未打开");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("重连尝试被中断: {}", e.getMessage());
            } catch (Exception e) {
                reconnectAttempts++;
                log.error("重连尝试失败: {}", e.getMessage());
//                if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                attemptReconnect();
//                } else {
//                    log.error("达到最大重连次数，无法重连。");
//                }
            }
        });
    }

}
