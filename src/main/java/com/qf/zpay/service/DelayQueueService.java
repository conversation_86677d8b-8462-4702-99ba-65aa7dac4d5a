package com.qf.zpay.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.ChannelConfig;
import generator.domain.CardTransFeeLog;
import generator.domain.UserCard;
import generator.domain.UserWalletAdvanceLog;
import generator.service.CardTransFeeLogService;
import generator.service.UserCardService;
import generator.service.UserWalletAdvanceLogService;
import generator.service.UserWalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Set;

@Service
@Slf4j
public class DelayQueueService {
    private static final String DELAY_QUEUE_KEY = "delay_queue";


    @Autowired
    CardFactory cardFactory;
    @Autowired
    private UserWalletService userWalletService;
    @Autowired
    private UserCardService userCardService;
    @Autowired
    private CardTransFeeLogService cardTransFeeLogService;
    @Autowired
    private UserWalletAdvanceLogService userWalletAdvanceLogService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public void addToDelayQueue(String message, long delayTime) {
        redisTemplate.opsForZSet().add(DELAY_QUEUE_KEY, message, System.currentTimeMillis() + delayTime);
    }


    public void pollAndProcessDelayedMessages() {
        Set<String> messages = redisTemplate.opsForZSet().rangeByScore(DELAY_QUEUE_KEY, 0, System.currentTimeMillis());
        for (String message : messages) {
            // 从延迟队列中删除已处理的消息
            redisTemplate.opsForZSet().remove(DELAY_QUEUE_KEY, message);
            // 处理消息
            processMessage(message);
        }
    }

    public void processMessage(String message) {
        log.info("【卡片解冻】卡片id：{}", message);
        // 拿到卡片
        UserCard userCard = userCardService.getByCardId(message);
        CardTransFeeLog cardTransFee = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                .eq(CardTransFeeLog::getCardId, message)
                .orderByDesc(CardTransFeeLog::getCreateTime)
                .last("limit 1"));
        BigDecimal amount = userCard.getAmount();
        if (cardTransFee != null && cardTransFee.getAccruingAmounts() != null && amount.compareTo(cardTransFee.getAccruingAmounts()) > 0) {
            BigDecimal accruingAmounts = cardTransFee.getAccruingAmounts();
            // 上游变动金额
            BigDecimal variableAmount = accruingAmounts;
            // 余额金额
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                variableAmount = amount.abs().add(accruingAmounts);
                accruingAmounts = variableAmount;
            }
            if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                variableAmount = userCard.getAmount().add(variableAmount);
            }
            if (variableAmount.compareTo(BigDecimal.ZERO) != 0) {
                cardFactory.getBuilder(userCard.getChannel());
                cardFactory.recharge(userCard.getCardId(), variableAmount);

                UserWalletAdvanceLog advanceLog = userWalletAdvanceLogService.getOne(new LambdaQueryWrapper<UserWalletAdvanceLog>()
                        .eq(UserWalletAdvanceLog::getCardId, userCard.getCardId())
                        .orderByDesc(UserWalletAdvanceLog::getCreateTime)
                        .last("limit 1"));
                BigDecimal b = amount.compareTo(BigDecimal.ZERO) < 0 ? amount : BigDecimal.ZERO;
                if (advanceLog != null) {
                    if (advanceLog.getAccruingAmounts().compareTo(BigDecimal.ZERO) < 0) {
                        if (cardTransFee != null) {
                            cardTransFee.setId(null);
                            cardTransFee.setAccruingAmounts(b);
                            cardTransFee.setChangeFee(accruingAmounts.negate());
                        }
                        advanceLog.setId(null);
                        advanceLog.setChangeFee(accruingAmounts.negate());
                        advanceLog.setAccruingAmounts(b);
                        userWalletService.changeAmount(userCard.getUid(), accruingAmounts.negate(), userCard.getCardModel(), WalletTypeEnum.REFUND_THE_ADVANCE_FEE, advanceLog, cardTransFee);
                    }
                }
            }
        }
    }
}