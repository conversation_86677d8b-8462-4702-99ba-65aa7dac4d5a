package com.qf.zpay.service;

import com.qf.zpay.constants.OccupationEnum;
import com.qf.zpay.util.FTPUtil;
import generator.domain.CardHolder;
import generator.domain.UserCard;
import generator.mapper.UserCardMapper;
import generator.service.CardApplyOrderService;
import generator.service.CardHolderService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FTPService {
    @Value("${file.upload-dir}")
    private String path;
    @Value("${ftp.userName}")
    private String userName;
    @Value("${ftp.password}")
    private String password;
    @Value("${ftp.port}")
    private int port;
    @Value("${ftp.host}")
    private String host;
    @Value("${ftp.upload}")
    private String upload;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    CardHolderService cardHolderService;
    @Autowired
    private UserCardMapper userCardMapper;
    @Autowired
    private CardApplyOrderService cardApplyOrderService;

    /**
     * 填充pdf转成图片
     *
     * @param cardHolder 用卡人kyc
     * @param userCard   卡片
     */
    public String operateWord(CardHolder cardHolder, UserCard userCard) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String formattedCreateTime = sdf.format(cardHolder.getCreateTime());
        Map<String, Object> data = getStringObjectMap(cardHolder, userCard);
        data.put("create_time", formattedCreateTime);

        Map<String, Object> map2 = new HashMap<>();
        map2.put("img_af_image", path + "/" + cardHolder.getSignatureImage());

        Map<String, Object> formMap = new HashMap<>();
        formMap.put("dataMap", data);
        formMap.put("imageMap", map2);
        formMap.put("sex", cardHolder.getSex());
        formMap.put("purpose", cardHolder.getPurpose());
        String path = this.path + "/" + cardHolder.getId().toString();
        String pdfPath = this.path + "/QQF_SQ_1.pdf";
        FTPUtil.fillPDFTemplate(formMap, userCard.getCardNumber(), pdfPath, path, this.path);
        return path;
    }

    /**
     * 填充实名 excel 文件
     *
     * @param cardHolder 实名信息
     * @param userCard   实名卡片
     * @param path       指定存储路径
     */
    public void excel(CardHolder cardHolder, UserCard userCard, String path) throws IOException {
        String templatePath = this.path + "/QQF_FTP_1.xlsx";
        Map<String, Object> data = getStringObjectMap(cardHolder, userCard);
        data.put("documentsType", cardHolder.getDocumentsType());
        data.put("realNameStatus", cardHolder.getRealNameStatus());
        data.put("sex", cardHolder.getSex());
        FTPUtil.fillExcelAndSave(templatePath, data, path);
    }

    /**
     * 图片处理
     *
     * @param cardHolder 实名信息
     * @param userCard   实名卡片
     * @param path       指定存储路径
     */
    public void renameAndMoveImages(CardHolder cardHolder, UserCard userCard, String path) throws IOException {
        FTPUtil.renameAndMoveImages(cardHolder.getCertificateImage(), path, userCard.getCardNumber());
    }

    private static Map<String, Object> getStringObjectMap(CardHolder cardHolder, UserCard userCard) {
        Map<String, Object> data = new HashMap<>();
        data.put("cardNumber", userCard.getCardNumber());
        data.put("englishName", cardHolder.getEnglishName());
        data.put("chineseName", cardHolder.getChineseName());
        data.put("nationCode", cardHolder.getNationCode());
        data.put("idNumber", cardHolder.getIdNumber());
        data.put("dataOfIssue", cardHolder.getDateOfIssue());
        data.put("dateOfExpiry", cardHolder.getDateOfExpiry());
        data.put("profession", cardHolder.getProfession());
        data.put("professionCh", cardHolder.getProfession());
        data.put("professionEn", OccupationEnum.getNameByCode(cardHolder.getProfession()));
        data.put("nationality", cardHolder.getNationality());
        data.put("phone", cardHolder.getPhone());
        data.put("birthday", cardHolder.getBirthday());
        data.put("email", cardHolder.getEmail());
        data.put("mailingAddress", cardHolder.getMailingAddress());
        data.put("purpose", cardHolder.getPurpose());
        return data;
    }

    public void upload(UserCard userCard, CardHolder cardHolder) throws Exception {
        // 填充申请表转为图片
        String path = operateWord(cardHolder, userCard);
        // 实名excel文件
        excel(cardHolder, userCard, path);
        // 身份证照片
        renameAndMoveImages(cardHolder, userCard, path);
        // 链接FTP
        FtpClient apacheFtpClient = new FtpClient(host, port, userName, password);
        //上传
        HashMap<String, String> stringStringHashMap = apacheFtpClient.uploadDirectoryToZip(upload, path);
        apacheFtpClient.close();
        // 设置到redisson
        String key = stringStringHashMap.get("file_name");
        RMap<String, String> booleanStringHashMap = redissonClient.getMap(key);
        booleanStringHashMap.put("card_number", userCard.getCardNumber());
        booleanStringHashMap.put("card_holder", cardHolder.getId().toString());
        log.info("上传返回结果" + stringStringHashMap);
        deleteFile(path);
    }

    public static void deleteFile(String path) {
        File file = new File(path);
        if (file.exists()) {
            if (file.isDirectory()) {
                // 删除目录下的所有文件
                File[] files = file.listFiles();
                if (files != null) {
                    for (File f : files) {
                        f.delete();
                    }
                }
                // 删除目录本身
                file.delete();
            } else {
                // 如果是文件,直接删除
                file.delete();
            }
        } else {
            log.error("文件或文件夹不存在: " + path);
        }
    }

    public void modifyState() throws Exception {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = yesterday.format(formatter);
        String remoteDirectory = upload + "/bak/" + dateString;
        // 链接FTP
        FtpClient apacheFtpClient = new FtpClient(host, port, userName, password);
        Map<String, List<String>> stringListMap = apacheFtpClient.checkLogFileNames(remoteDirectory);
        List<String> fail = stringListMap.get("fail");
        List<String> success = stringListMap.get("success");
        if (!fail.isEmpty()) {
            for (String fileName : fail) {
                String substring = fileName.substring(0, fileName.length() - 4);
                RMap<String, String> booleanStringHashMap = redissonClient.getMap(substring);
                if (!booleanStringHashMap.isEmpty()) {
                    String cardHolderId = booleanStringHashMap.get("card_holder");
                    String cardNumber = booleanStringHashMap.get("card_number");
                    // 0未审核/ 1审核通过/ 2 已驳回
                    UserCard oneByCardNumber = userCardMapper.getOneByCardNumber(cardNumber);
                    cardHolderService.updateStatus(Integer.valueOf(cardHolderId), 2);
                    //  0 待处理 1 处理中 2 驳回请求 3 处理成功 4 处理失败
                    cardApplyOrderService.updateStatus(oneByCardNumber, 4);
                    booleanStringHashMap.delete();
                }
            }
        }
        apacheFtpClient.close();
    }

}
