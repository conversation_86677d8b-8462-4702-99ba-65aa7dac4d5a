package com.qf.zpay.service.card.channel;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.card.CardBuilder;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * 光子易通道处理
 *
 * <AUTHOR>
 * @updateTime 2024/6/3 10:45
 * @doc <a href="https://api-doc.photonpay.com/api-zh.html#tag/%E5%8D%A1%E7%AE%A1%E7%90%86">...</a>
 */
@Log4j2
@Service
public class Photon2 extends CardBuilder {

    public static HashMap<String, String> statusSwitcher = new HashMap<>() {{
        put("normal", "Active"); // 活跃
        put("freezing", "Blocked"); // 锁定
        put("frozen", "Blocked");
        put("risk_frozen", "Blocked");
        put("system_frozen", "Blocked");
        put("unfreezing", "Blocked");
        put("canceling", "Cancel"); // 注销
        put("cancelled", "Cancel");
        put("expired", "Expired"); // 过期
    }};


    @Value("${card.channel.photon2.appId}")
    private String appId;

    @Value("${card.channel.photon2.appSecret}")
    private String appSecret;

    @Value("${card.channel.photon2.domain}")
    private String domain;

    private String token;

    private HashMap<String, String> headers = new HashMap<>();


    //    private final RedissonClient redis;
//    @Qualifier("CenterRedis")
    @Autowired
    RedissonClient redis;

    public Photon2(RedissonClient redis) {
        headers.put("Content-Type", "application/json");
        this.redis = redis;
    }

    @Override
    protected void init() {
        this.getToken();
    }


    protected JSONObject getCardType() {
        String path = "vcc/openApi/v4/getCardBin";
        return this.sendReq(path, Method.GET, null);
    }

    public JSONObject openCard(String cardBin, BigDecimal amount) {
        this.getToken();
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardBin", cardBin);
        reqData.put("cardType", "share");
        reqData.put("cardCurrency", "USD");
        reqData.put("requestId", genReqId());
        reqData.put("cardExpirationDate", 35);
        reqData.put("transactionLimitType", "limited");
        reqData.put("transactionLimit", 0);
        genSign(new JSONObject(reqData));
        String path = "vcc/openApi/v4/openCard";
        JSONObject res = this.sendReq(path, Method.POST, new JSONObject(reqData));
        return res.getJSONObject("cardDetail");
    }

    protected JSONObject getCardDetail(String cardId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        String path = "vcc/openApi/v4/getCardDetail";
        return this.sendReq(path, Method.GET, new JSONObject(reqData));
    }

    protected JSONObject getCardCvv(String cardId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        String path = "vcc/openApi/v4/getCvv";
        return this.sendReq(path, Method.GET, new JSONObject(reqData));
    }

    protected Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        reqData.put("maxOnPercent", 20000); // 单笔交易限额
        reqData.put("transactionLimit", param.get("amount")); // 交易限额变动数值
        reqData.put("transactionLimitChangeType", param.get("changeType")); // 交易限额变动类型
        reqData.put("transactionLimitType", "limited");
        reqData.put("requestId", genReqId());
        genSign(new JSONObject(reqData));
        String path = "vcc/openApi/v4/updateCard";
        JSONObject res = this.sendReq(path, Method.POST, new JSONObject(reqData));
        // res： {"matrixAccount":"","cardId":"*********************","memberId":"****************","cardType":"share","maxOnPercent":20000,"transactionLimitType":"limited","availableTransactionLimit":99.9,"totalTransactionLimit":100,"cardStatus":"normal","maskCardNo":"553437******9266","createdAt":"2024-06-24T14:46:29"}
        return res.getStr("cardId").equals(cardId);
    }

    protected Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        reqData.put("status", status.getCode());
        reqData.put("requestId", genReqId());
        genSign(new JSONObject(reqData));
        String path = "vcc/openApi/v4/freezeCard";
        JSONObject res = this.sendReq(path, Method.POST, new JSONObject(reqData));
        return res.getBool("status");
    }


    protected Boolean cancelCard(String cardId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        genSign(new JSONObject(reqData));
        String path = "vcc/openApi/v4/cancelCard";
        JSONObject res = this.sendReq(path, Method.POST, new JSONObject(reqData));
        return res.getBool("status");
    }


    protected JSONObject getReqRes(String reqId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("type", "apply_card");
        reqData.put("requestId", reqId);
        String path = "vcc/openApi/v4/getRequestResult";
        return this.sendReq(path, Method.GET, new JSONObject(reqData));
    }


    public Boolean recharge(String cardId, BigDecimal amount) {
        return true;
    }

    public void getToken() {
        RBucket<String> token = redis.getBucket("token:photon2");
        if (!token.isExists()) {
            RLock lock = redis.getLock("token_lock_photon2");
            lock.lock(10, TimeUnit.SECONDS);
            try {
                String path = "/oauth2/token/accessToken";
                String auth = Base64.encode(appId + "/" + appSecret);
                this.headers.put("Authorization", "basic " + auth);
                JSONObject body = this.sendReq(path, Method.POST, null);
                Duration duration = Duration.between(Instant.now(), Instant.ofEpochMilli(body.getLong("expiresIn")));
                duration = duration.minusMinutes(5);
                token.set(body.getStr("token"), duration);
            } catch (Exception e) {
                log.error("Photon2 获取 token 失败", e);
            } finally {
                lock.unlock();
            }
        }

        this.token = token.get();
        this.headers.put("X-PD-TOKEN", this.token);
    }

    @Autowired
    RsaService rsaService;

    protected String genSign(JSONObject body) {
        String bodyStr = JSONUtil.toJsonStr(body);
        String sign = rsaService.genSign("photon2", bodyStr, SignAlgorithm.MD5withRSA);
        this.headers.put("X-PD-SIGN", sign);
        return sign;
    }

    protected Boolean checkSign(String body, String sign) {
        return rsaService.checkSign("photon2", body, sign, SignAlgorithm.MD5withRSA);
    }

    protected JSONObject sendReq(String path, Method method, JSONObject body) {
        String url = UrlBuilder.of(domain).addPath(path).build();
        JSONObject resObj = this.sendNow(url, method, headers, body);
//            Console.error(resObj);
        if (!resObj.getStr("code").equals("0000")) {
            log.error("光子易接口报错：请求：{}，响应：{}", body, resObj);
            Assert.fail(ResultCode.UP_ERR);
        }
        if (resObj.getJSONObject("data") == null) {
            Boolean status = "succeed".equals(resObj.getStr("msg"));
            JSONObject data = new JSONObject();
            data.putOnce("status", status);
            resObj.putOnce("data", data);
        }
//            this.headers.clear();
        return resObj.getJSONObject("data");
    }
}
