package com.qf.zpay.service.card.channel;

import cn.hutool.json.JSONObject;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.GlobalcashService;
import com.qf.zpay.service.card.CardBuilder;
import generator.domain.UserCard;
import generator.mapper.UserCardMapper;
import generator.service.UserCardService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/7/10 18:40
 */
@Component
public class ZPhysical extends CardBuilder {
    @Autowired
    UserCardService userCardService;

    @Autowired
    UserCardMapper userCardMapper;
    @Resource
    private GlobalcashService globalcashService;
    @Override
    protected JSONObject openCard(String cardCode, BigDecimal amount) {
        //
        Integer cardManageId = amount.intValue();
        UserCard userCard = userCardService.getOneEmptyCard(cardCode, cardManageId);
        if (userCard == null) {
            Assert.fail(ResultCode.NO_CARDS);
        }
        return new JSONObject(userCard);
    }

    @Override
    protected JSONObject getCardDetail(String cardId) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        return new JSONObject(cardInfo);
    }

    @Override
    protected Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        return true;
    }

    @Override
    protected Boolean cancelCard(String cardId) {
        return true;
    }


    protected Boolean recharge(String cardId, BigDecimal amount) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        try {
            globalcashService.recharge(cardInfo.getCardNumber(), amount);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return true;
    }
}
