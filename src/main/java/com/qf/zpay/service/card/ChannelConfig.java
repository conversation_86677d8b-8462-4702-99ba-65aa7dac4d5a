package com.qf.zpay.service.card;

import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.constants.CardTransTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @updateTime 2024/6/19 14:28
 */
@Component
public final class ChannelConfig {

    /**
     * 支持的渠道列表
     */
    public final static Set<String> SUPPORT_CHANNEL = new HashSet<>(List.of(
            "photon", "photon2", "skyee", "ZPhysical", "GSalary"
    ));


    /**
     * 充值时需要传入总金额的渠道列表
     */
    public final static Set<String> RECHARGE_MUST_TOTAL_AMOUNT = new HashSet<>(List.of("skyee"));


    /**
     * 需要先通过申请单操作的渠道
     */
    public final static Set<String> MUST_APPLY = new HashSet<>(List.of("ZPhysical"));


    /**
     * 需要通过详情接口修正卡信息的渠道
     */
    public final static Set<String> FIX_BY_DETAIL = new HashSet<>(List.of("skyee", "GSalary", "Asinx"));

    private final static HashMap<String, HashMap<String, String>> STATUS_SWITCHER = new HashMap<>() {
        {
            put("photon", new HashMap<>() {{
                put("normal", "Active"); // 活跃
                put("freezing", "Blocked"); // 锁定
                put("frozen", "Blocked");
                put("risk_frozen", "Blocked");
                put("system_frozen", "Blocked");
                put("unfreezing", "Blocked");
                put("canceling", "Cancel"); // 注销
                put("cancelled", "Cancel");
                put("expired", "Expired"); // 过期
            }});
            put("photon2", new HashMap<>() {{
                put("normal", "Active"); // 活跃
                put("freezing", "Blocked"); // 锁定
                put("frozen", "Blocked");
                put("risk_frozen", "Blocked");
                put("system_frozen", "Blocked");
                put("unfreezing", "Blocked");
                put("canceling", "Cancel"); // 注销
                put("cancelled", "Cancel");
                put("expired", "Expired"); // 过期
            }});
            put("GSalary", new HashMap<>() {{
                put("ACTIVE", "Active"); // 活跃
                put("FREEZING", "Blocked"); // 锁定
                put("FROZEN", "Blocked");
                put("UNFREEZING", "Blocked");
                put("CANCELLED", "Cancel"); // 注销
                put("CANCELLING", "Cancel");
                put("EXPIRED", "Expired"); // 过期
            }});
            put("asinx", new HashMap<>() {{
                put("AUDITING", "Blocked");
                put("AUDIT_PASS", "Blocked");
                put("AUDIT_NOT_PASS", "Blocked");
                put("TBA", "Blocked");
                put("ACTIVE_PROCESSING", "Active");
                put("ACTIVE", "Active");
                put("INACTIVE", "Blocked");
                put("CLOSE", "Cancel"); // 注销
                put("CLOSE_PROCESSING", "Cancel");
                put("EXCHANGE_PROCESSING", "Blocked");
            }});
            put("asinxPhysical", new HashMap<>() {{
                put("AUDITING", "Blocked");
                put("AUDIT_PASS", "Blocked");
                put("AUDIT_NOT_PASS", "Blocked");
                put("TBA", "Blocked");
                put("ACTIVE_PROCESSING", "Active");
                put("ACTIVE", "Active");
                put("INACTIVE", "Blocked");
                put("CLOSE", "Cancel"); // 注销
                put("CLOSE_PROCESSING", "Cancel");
                put("EXCHANGE_PROCESSING", "Blocked");
            }});
            put("ZNet", new HashMap<>() {{
                put("Active", "Active"); // 活跃
                put("Blocked", "Blocked"); // 锁定
                put("Cancel", "Cancel"); // 注销
                put("Expired", "Expired"); // 过期
            }});
        }
    };


    public static CardStatusEnum switchStatus(String channel, String status) {
        return CardStatusEnum.valueOf(STATUS_SWITCHER.get(channel).get(status));
    }

    private final static HashMap<String, HashMap<String, String>> TRANSACTION_TYPE_SWITCHER = new HashMap<>() {
        {
            put("photon", new HashMap<>() {{
                put("auth", CardTransTypeEnum.Auth.getCode());
                put("corrective_auth", CardTransTypeEnum.CorrectiveAuth.getCode());
                put("verification", CardTransTypeEnum.Verification.getCode());
                put("void", CardTransTypeEnum.Void.getCode());
                put("refund", CardTransTypeEnum.Refund.getCode());
                put("corrective_refund", CardTransTypeEnum.CorrectiveRefund.getCode());
                put("service_fee", CardTransTypeEnum.ServiceFee.getCode());
                put("refund_reversal", CardTransTypeEnum.RefundReversal.getCode());
            }});
            put("photon2", new HashMap<>() {{
                put("auth", CardTransTypeEnum.Auth.getCode());
                put("corrective_auth", CardTransTypeEnum.CorrectiveAuth.getCode());
                put("verification", CardTransTypeEnum.Verification.getCode());
                put("void", CardTransTypeEnum.Void.getCode());
                put("refund", CardTransTypeEnum.Refund.getCode());
                put("corrective_refund", CardTransTypeEnum.CorrectiveRefund.getCode());
                put("service_fee", CardTransTypeEnum.ServiceFee.getCode());
                put("refund_reversal", CardTransTypeEnum.RefundReversal.getCode());
            }});
            put("GSalary", new HashMap<>() {{
                put("AUTH", CardTransTypeEnum.Auth.getCode());
                put("CORRECTIVE_AUTH", CardTransTypeEnum.CorrectiveRefund.getCode());
                put("VERIFICATION", CardTransTypeEnum.Verification.getCode());
                put("VOID", CardTransTypeEnum.CorrectiveAuth.getCode());
                put("REFUND", CardTransTypeEnum.Refund.getCode());
                put("CORRECTIVE_REFUND", CardTransTypeEnum.CorrectiveRefund.getCode());
                put("CORRECTIVE_REFUND_VOID", CardTransTypeEnum.CorrectiveRefundVoid.getCode());
                put("SERVICE_FEE", CardTransTypeEnum.ServiceFee.getCode());
                put("REFUND_REVERSAL", CardTransTypeEnum.RefundReversal.getCode());
            }});
            put("asinxPhysical", new HashMap<>() {{
                put("AUTH", CardTransTypeEnum.Auth.getCode());
                put("topup", CardTransTypeEnum.Auth.getCode());
                put("REFUND", CardTransTypeEnum.Refund.getCode());
                put("refund", CardTransTypeEnum.Refund.getCode());
                put("FEE", CardTransTypeEnum.ServiceFee.getCode());
                put("fee", CardTransTypeEnum.ServiceFee.getCode());
            }});
        }
    };


    public static CardTransTypeEnum transactionTypeSwitchStatus(String channel, String status) {
        return CardTransTypeEnum.valueOf(TRANSACTION_TYPE_SWITCHER.get(channel).get(status));
    }

}
