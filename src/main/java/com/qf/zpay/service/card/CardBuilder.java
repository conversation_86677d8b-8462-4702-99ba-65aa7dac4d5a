package com.qf.zpay.service.card;

import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.util.ZHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 此接口用户封装卡片处理方法，使得上游处理方法对齐
 *
 * <AUTHOR>
 * @updateTime 2024/6/3 10:34
 */
@Component
@Slf4j
public abstract class CardBuilder {

    @Autowired
    ZHelperUtil zHelper;

    /**
     * 上游初始化
     */
    protected void init() {
        // 可以不用实现
    }


    /**
     * 开卡
     *
     * @param cardCode 上游卡标识
     * @param amount   金额
     * @return 返回开卡结果
     */
    protected abstract JSONObject openCard(String cardCode, BigDecimal amount);


    /**
     * 卡片详情
     *
     * @param cardId 卡ID
     * @return JSONObject 卡片详情
     */
    protected abstract JSONObject getCardDetail(String cardId);

    /**
     * 卡片CVV信息
     *
     * @param cardId 卡ID
     * @return JSONObject 卡片详情
     */
    protected JSONObject getCardCvv(String cardId) {
        // 有些上游卡片详情就返回了CVV信息
        return null;
    }


    /**
     * 修改卡片限额
     *
     * @param cardId 卡ID
     * @param param  修改参数 HashMap
     *               key: amount  BigDecimal 限额变动
     *               key: changeType 变动类型 increase 增加 decrease 减少
     * @return Boolean
     */
    protected Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        // 有些上游不需要
        return false;
    }


    /**
     * 修改卡片状态
     *
     * @param cardId 卡ID
     * @param status 状态 freeze 冻结 unfreeze 解冻
     * @return Boolean
     */
    protected abstract Boolean changeStatus(String cardId, CardStatusActionEnum status);


    /**
     * 注销卡片
     *
     * @param cardId 卡ID
     * @return Boolean
     */
    protected abstract Boolean cancelCard(String cardId);

    /**
     * 卡片充值
     *
     * @param cardId 卡ID
     * @param amount 充值金额
     * @return 返回充值结果
     */
    protected Boolean recharge(String cardId, BigDecimal amount) {
        // 看上游情况，实体卡可能使用
        return true;
    }

    /**
     * 修改卡片联系人
     *
     * @param cardId 卡ID
     * @param email  email
     * @return Boolean
     */
    protected Boolean updateContact(String cardId, String email) {
        // 部分卡片添加联系人信息用于接收验证码
        return true;
    }


    protected void getToken() {
    }


    protected String genSign(JSONObject body) {
        return null;
    }


    /**
     * 检查签名
     *
     * @param data 原始数据字符串
     * @param sign 签名串
     * @return Boolean
     */
    protected Boolean checkSign(String data, String sign) {
        return false;
    }


    /**
     * 发送请求并处理
     *
     * @param path   地址
     * @param method 方法
     * @param body   数据
     * @return Object
     */
    protected JSONObject sendReq(String path, Method method, JSONObject body) {
        return null;
    }

    /**
     * 发送请求封装
     *
     * @param url     请求地址
     * @param method  请求方法
     * @param headers 请求头
     * @param data    请求数据
     */
    protected JSONObject sendNow(String url, Method method, HashMap<String, String> headers, JSONObject data) {
        JSONObject res = zHelper.sendReq(url, method, headers, data, this.getClass().getSimpleName());
        if (res == null) {
            Assert.fail(ResultCode.FAILED);
        }
        return res;
    }

    protected String genReqId() {
        return ZHelperUtil.genOrderId("ZQ");
    }

    /**
     * 添加持卡人
     *
     * @param map 请求数据
     */
    protected JSONObject savaCardHolders(@RequestBody Map<String, Object> map) {
        return null;
    }

    /**
     * 添加 卡产品
     */
    protected JSONObject getCardProducts() {
        return null;
    }

}
