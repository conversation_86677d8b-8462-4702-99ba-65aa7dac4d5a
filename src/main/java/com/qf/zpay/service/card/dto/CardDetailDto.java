package com.qf.zpay.service.card.dto;

import com.qf.zpay.constants.CardStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/6/21 15:27
 */
@Data
public class CardDetailDto {

    private Integer id;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡组织
     */
    private String cardScheme;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     * 有效期
     */
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    private CardStatusEnum cardStatus;


    /**
     * 卡片申请时间
     */
    private Date applyTime;


}
