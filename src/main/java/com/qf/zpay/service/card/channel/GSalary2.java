package com.qf.zpay.service.card.channel;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.gsalary.sdk.GSalaryClient;
import com.gsalary.sdk.GSalaryConnectionConfig;
import com.gsalary.sdk.entity.RawGSalaryRequest;
import com.gsalary.sdk.entity.RequestMethod;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardBuilder;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import com.qf.zpay.util.ZHelperUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Log4j2
@Service
public class GSalary2 extends CardBuilder {

    @Value("${GSalary.url}")
    private String url;
    @Value("${GSalary.appId}")
    private String appId;

    @Autowired
    ZHelperUtil zHelper;

    @Value("${GSalary.card.holder}")
    private String holder;

    private GSalaryClient gSalaryClient;

    @Override
    protected void init() {
        String basePath = new File("").getAbsolutePath(); // 获取项目的根路径
        String privateKeyPath = basePath + "/cert/private/gsalary.pem";
        String publicKeyPath = basePath + "/cert/public/gsalary.pem";
        try {
            FileInputStream privateKeyStream = new FileInputStream(privateKeyPath);
            FileInputStream publicKeyStream = new FileInputStream(publicKeyPath);
            GSalaryConnectionConfig config = new GSalaryConnectionConfig()
                    .setAppid(this.appId)
                    .setClientPrivateKeyFromStream(privateKeyStream)
                    .setServerPublicKeyFromStream(publicKeyStream)
                    .setEndpoint(this.url);
            this.gSalaryClient = new GSalaryClient(config);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @param cardCode 上游卡标识
     * @param amount   金额
     * @return ;
     */
    @Override
    protected JSONObject openCard(String cardCode, BigDecimal amount) {
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
            reqMap.put("product_code", cardCode); // 卡产品编码
            reqMap.put("currency", "USD"); // 卡币种
            reqMap.put("card_holder_id", holder); // 注册的持卡人id
            reqMap.put("limit_per_day", 10000); // 每日交易限额
            reqMap.put("limit_per_month", 300000); // 每月交易限额
            reqMap.put("limit_per_transaction", 10000); // 单笔交易限额
            reqMap.put("init_balance", amount); // 初始额度

            // 申请开卡
            String path = "/v1/card_applies";
            JSONObject entries = sendReqGSalary(RequestMethod.POST, path, null, reqMap);
            String status = entries.getStr("status");
            if (status.equals("FAILED")) {
                throw new ApiException(ResultCode.OPEN_CARD_ERROR);
            }

            Thread.sleep(1500);

            JSONObject cardRes = getReqRes(entries.getStr("request_id"));
            JSONObject cardDetail = cardRes.getJSONObject("card_detail");

            JSONObject cardInfo = new JSONObject();
//            cardInfo.putOpt("cardId", cardDetail.getStr("card_id"));
//            cardInfo.putOpt("cardNo", cardDetail.getStr("pan"));
//            cardInfo.putOpt("cardScheme", cardDetail.getStr("brand_code"));
//            cardInfo.putOpt("cardCurrency", cardDetail.getStr("card_currency"));
//            cardInfo.putOpt("availableTransactionLimit", cardDetail.getStr("available_balance"));
//
//            cardInfo.putOpt("cvv", entries3.getStr("cvv"));
//            cardInfo.putOpt("expirationDate", entries3.getStr("expire_month") + "/" + entries3.getStr("expire_year"));
            return cardInfo;
        } catch (InterruptedException e) {
            log.error("开卡失败", e);
            throw new ApiException(ResultCode.UP_ERR);
        }
    }

    private JSONObject getReqRes(String requestId) {
        // 查询开卡结果
        String path2 = "/v1/card_applies/" + requestId;
        JSONObject entries2 = sendReqGSalary(RequestMethod.GET, path2, null, null);
        assert entries2 != null;
        JSONObject jsonObject = entries2.getJSONObject("card_detail");
        String cardId = jsonObject.getStr("card_id");
        String path3 = "/v1/cards/" + cardId + "/secure_info";
        return sendReqGSalary(RequestMethod.GET, path3, null, null);
    }

    public String openCard2(String cardCode, BigDecimal amount) {
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
            reqMap.put("product_code", cardCode); // 卡产品编码
            reqMap.put("currency", "USD"); // 卡币种
            reqMap.put("card_holder_id", holder); // 注册的持卡人id
            reqMap.put("limit_per_day", 10000); // 每日交易限额
            reqMap.put("limit_per_month", 300000); // 每月交易限额
            reqMap.put("limit_per_transaction", 10000); // 单笔交易限额
            reqMap.put("init_balance", amount); // 初始额度
            String path = "/v1/card_applies";
            JSONObject entries = zHelper.sendReqGSalary(path, reqMap);
            String status = entries.getStr("status");
            if (status.equals("FAILED")) {
                throw new ApiException(ResultCode.OPEN_CARD_ERROR);
            }
            return entries.getStr("request_id");
        } catch (JsonProcessingException e) {
            log.error("开卡异常{}", e.getMessage());
            throw new RuntimeException("开卡异常" + e.getMessage());
        }
    }

    /**
     * 获取卡详情
     *
     * @param cardId 卡ID ；
     * @return ；
     */
    @Override
    protected JSONObject getCardDetail(String cardId) {
        String path = "/v1/cards/" + cardId;
        JSONObject entries = sendReqGSalary(RequestMethod.GET, path, null, null);
        JSONObject entries1 = new JSONObject();
        entries1.putOpt("cardId", entries.getStr("card_id"));
        entries1.putOpt("cardScheme", entries.getStr("brand_code"));
        entries1.putOpt("cardCurrency", entries.getStr("card_currency"));
        entries1.putOpt("availableTransactionLimit", entries.getStr("available_balance"));
        entries1.putOpt("cardStatus", entries.getStr("status"));
        return entries1;
    }

    /**
     * @param cardId 卡ID
     * @return ;
     */
    protected JSONObject getCardCvv(String cardId) {
        String path3 = "/v1/cards/" + cardId + "/secure_info";
        JSONObject entries3 = sendReqGSalary(RequestMethod.GET, path3, null, null);
        JSONObject entries = new JSONObject();
        entries.putOpt("cardNo", entries3.getStr("pan"));
        entries.putOpt("cvv", entries3.getStr("cvv"));
        entries.putOpt("expirationDate", entries3.getStr("expire_month") + "/" + entries3.getStr("expire_year"));
        return entries;
    }

    /**
     * @param cardId 卡ID
     * @param status 状态 freeze 冻结 unfreeze 解冻
     * @return ;
     */
    @Override
    protected Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        String path3 = "/v1/cards/" + cardId + "/freeze_status";
        Map<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("freeze", status.getCode().equals("freeze"));
        sendReqGSalary(RequestMethod.PUT, path3, null, stringObjectHashMap);
        return Boolean.TRUE;
    }

    @Override
    protected Boolean cancelCard(String cardId) {
        String path3 = "/v1/cards/" + cardId;
        sendReqGSalary(RequestMethod.DELETE, path3, null, null);
        return Boolean.TRUE;
    }

    /**
     * 添加持卡人
     */
    @Override
    protected JSONObject savaCardHolders(@RequestBody Map<String, Object> map) {
        String path = "/v1/card_holders";
        return sendReqGSalary(RequestMethod.POST, path, null, map);
    }

    /**
     *
     */
    @Override
    protected JSONObject getCardProducts() {
        String path = "/v1/card_support/products";
        return sendReqGSalary(RequestMethod.GET, path, null, null);
    }

    @Override
    protected Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        String path = "/v1/cards/balance_modifies";
        HashMap<String, Object> body = new HashMap<>();
        body.put("card_id", cardId);
        body.put("amount", param.get("amount"));
        body.put("type", param.get("changeType").toString().equals("increase") ? "INCREASE" : "DECREASE");
        body.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
        JSONObject entries = sendReqGSalary(RequestMethod.POST, path, null, body);
        if (entries.getStr("status").equals("FAILED")) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    private JSONObject sendReqGSalary(RequestMethod method, String path, Map<String, String> query, Map<String, Object> body) {
        Integer id = zHelper.logReq2db("GSalary", this.url + path, " ", method.toString(), new JSONObject(body));
        try {
            RawGSalaryRequest request = new RawGSalaryRequest(method, path, query, JSONUtil.toJsonStr(body));
            String res = this.gSalaryClient.request(request);
            zHelper.logReqOk(id, " ", res);
            JSONObject resObj = new JSONObject(res);
            JSONObject result = resObj.getJSONObject("result");
            String flagRes = result.getStr("result");
            if (!flagRes.equals("S")) {
                log.error("GSalary 接口报错：请求：{}，响应：{}", body, resObj);
                Assert.fail(ResultCode.UP_ERR);
//            throw new GSalaryException(resObj.getJSONObject("result").getStr("code"), resObj.getJSONObject("result").getStr("message"));
            }
            return resObj.isNull("data") ? null : resObj.getJSONObject("data");
        } catch (Exception e) {
            log.error("请求 GSalary 失败", e);
            throw new ApiException(ResultCode.UP_ERR);
        }
    }
}
