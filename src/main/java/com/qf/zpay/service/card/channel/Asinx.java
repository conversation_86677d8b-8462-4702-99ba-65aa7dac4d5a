package com.qf.zpay.service.card.channel;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.TreeMap;

/**
 * Asinx通道处理
 *
 * @doc <a href="https://25q4gdveuf.apifox.cn">...</a>
 */
@Log4j2
@Service
public class Asinx extends CardBuilder {

    @Value("${card.channel.asinx.appId}")
    private String appId;

    @Value("${card.channel.asinx.appSecret}")
    private String appSecret;

    @Value("${card.channel.asinx.domain}")
    private String domain;

    @Value("${card.channel.asinx.uid}")
    private String uid;

    private HashMap<String, String> headers = new HashMap<>();


    public Asinx() {
        headers.put("Content-Type", "application/json;charset=UTF-8");
    }

    @Override
    protected void init() {
    }

    @Override
    public JSONObject getCardProducts() {
        String path = "/bankcard/template/list";
        JSONObject body = new JSONObject();
        return sendReq(path, body);
    }

    @Override
    public JSONObject openCard(String cardBin, BigDecimal amount) {
        JSONObject body = new JSONObject();
        body.putOnce("bankcardId", cardBin);
        String path = "/bankcard/apply";
        JSONObject res = this.sendReq(path, body);

        String cardId = res.getStr("userBankcardId");
        return getCardDetail(cardId);
    }

    @Override
    public JSONObject getCardDetail(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/cardInfo";
        JSONObject detail = this.sendReq(path, body);
        JSONObject amount = getCardAmount(cardId);
        detail.putOnce("balance", amount.getBigDecimal("balance"));
        return detail;
    }

    private JSONObject getCardAmount(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/balance";
        return this.sendReq(path, body);
    }


    @Override
    public Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
//        BigDecimal amount = BigDecimal.valueOf((Double) param.get("amount"));
//        BigDecimal rechargeFee = BigDecimal.valueOf(0.015).multiply(amount);
//        BigDecimal total = amount.add(rechargeFee);
//        log.error("Asinx充值金额：{},{},{}", amount, rechargeFee, total);
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        body.putOnce("amount", 0);
        body.putOnce("targetAmount", param.get("amount"));
        String path = "/bankcard/recharge";
        JSONObject res = this.sendReq(path, body);
        JSONObject res2 = getReqRes(cardId, res.getStr("requestOrderId"));
        return "SUCCESS".equals(res2.getStr("status")) || "ACCEPT".equals(res2.getStr("status"));
    }


    @Override
    public Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        Boolean enable = status == CardStatusActionEnum.Unfreeze;
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        body.putOnce("enable", enable);
        String path = "/bankcard/update/status";
        JSONObject res = this.sendReq(path, body);
        return res.getBool("success");
    }


    @Override
    public Boolean cancelCard(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/close";
        JSONObject res = this.sendReq(path, body);
        JSONObject res2 = getReqRes(cardId, res.getStr("requestOrderId"));
        return "ACCEPT".equals(res2.getStr("status"));
    }


    public JSONObject getReqRes(String cardId, String reqId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("userBankcardId", cardId);
        reqData.put("requestOrderId", reqId);
        String path = "/bankcard/cardOrderInfo";
        return this.sendReq(path, new JSONObject(reqData));
    }


    protected String genSign(JSONObject body, String path) {
        JSONObject sortedObj = JSONUtil.parseObj(body.toBean(TreeMap.class));
        String content = path + sortedObj.toString();
        String base64String = Base64.encode(content);

        AES aes = SecureUtil.aes(HexUtil.decodeHex(appSecret));
        String encrypt = aes.encryptHex(base64String, CharsetUtil.CHARSET_UTF_8);
        return SecureUtil.md5(encrypt);
    }

    @Override
    protected JSONObject sendReq(String path, Method method, JSONObject body) {
        String url = UrlBuilder.of(domain).addPath(path).build();
        headers.put("appId", appId);
        headers.put("uId", uid);
        String sign = genSign(body, path);
        headers.put("sign", sign);

        JSONObject resObj = this.sendNow(url, method, headers, body);
//        Console.error("resObj", resObj);
        boolean hasErr = false;
        if (!resObj.getInt("code").equals(1)) {
            hasErr = true;
            if (resObj.getInt("code").equals(99997)) {
                hasErr = false;
            }
        }
        if (hasErr) {
            log.error("Asinx接口报错：请求：{}，响应：{}", body, resObj);
            Assert.fail(ResultCode.UP_ERR);
        }
        String result = resObj.getStr("result");
        if (result == null) {
            return resObj;
        }
        AES aes = SecureUtil.aes(HexUtil.decodeHex(appSecret));
        String dataStr = Base64.decodeStr(aes.decryptStr(result, CharsetUtil.CHARSET_UTF_8));
//        Console.error("dataStr", dataStr);
        if (resObj.containsKey("total")) {
            JSONObject data = new JSONObject();
            data.putOnce("total", resObj.getInt("total"));
            data.putOnce("data", JSONUtil.parseArray(dataStr));
            return data;
        } else {
            return JSONUtil.parseObj(dataStr);
        }
    }

    protected JSONObject sendReq(String path, JSONObject body) {
        return sendReq(path, Method.POST, body);
    }

}
