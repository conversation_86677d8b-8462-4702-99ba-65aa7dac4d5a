package com.qf.zpay.service.card.channel;

import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.card.CardBuilder;
import generator.domain.UserCard;
import generator.service.UserCardService;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Random;

/**
 * 光子易通道处理
 *
 * <AUTHOR>
 * @updateTime 2024/6/3 10:45
 * @doc <a href="https://api-doc.photonpay.com/api-zh.html#tag/%E5%8D%A1%E7%AE%A1%E7%90%86">...</a>
 */
@Log4j2
@Service
public class ZNet extends CardBuilder {


    @Autowired
    private UserCardService userCardService;


    private static final String[] cardTypes = {"Master", "VISA", "UnionPay"};
    private static int index = 0;
    public static HashMap<String, String> statusSwitcher = new HashMap<>() {{
        put("normal", "Active"); // 活跃
        put("freezing", "Blocked"); // 锁定
        put("frozen", "Blocked");
        put("risk_frozen", "Blocked");
        put("system_frozen", "Blocked");
        put("unfreezing", "Blocked");
        put("canceling", "Cancel"); // 注销
        put("cancelled", "Cancel");
        put("expired", "Expired"); // 过期
    }};


    private HashMap<String, String> headers = new HashMap<>();


    @Autowired
    RedissonClient redis;

    public ZNet(RedissonClient redis) {
        headers.put("Content-Type", "application/json");
        this.redis = redis;
    }

    @Override
    protected void init() {
        this.getToken();
    }


    protected JSONObject getCardType() {
        String path = "vcc/openApi/v4/getCardBin";
        return this.sendReq(path, Method.GET, null);
    }


    protected JSONObject openCard(String cardBin, BigDecimal amount) {
        Random random = new Random();
        int vcc = 100 + random.nextInt(900);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 加上三年
        LocalDateTime threeYearsLater = now.plusYears(3);
        // 创建日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间和三年后的时间
        String formattedThreeYearsLater = threeYearsLater.format(formatter);
        JSONObject res = new JSONObject();
        res.set("cardId", "ZC" + String.valueOf(System.currentTimeMillis()).substring(1, 13));
        res.set("cardNo", cardBin + String.valueOf(System.currentTimeMillis()).substring(1, 13));
        res.set("cardScheme", this.getNextCardType());
        res.set("cardCurrency", "USD");
        res.set("availableTransactionLimit", "0");
        res.set("cvv", vcc);
        res.set("expirationDate", formattedThreeYearsLater);
        return res;
    }

    protected JSONObject getCardDetail(String cardId) {
        JSONObject res = new JSONObject();
        UserCard one = userCardService.getOne(new LambdaQueryWrapper<UserCard>().eq(UserCard::getCardId, cardId));
        res.set("cardId", one.getCardId());
        res.set("cardNo", one.getCardNumber());
        res.set("cardScheme", one.getCardScheme());
        res.set("cardCurrency", "USD");
        res.set("availableTransactionLimit", one.getAmount());
        res.set("cvv", one.getCardCvv());
        res.set("expirationDate", one.getCardExpirationMmyy());
        res.set("cardStatus", one.getCardStatus());
        return res;
    }

    protected JSONObject getCardCvv(String cardId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("cardId", cardId);
        String path = "vcc/openApi/v4/getCvv";
        return this.sendReq(path, Method.GET, new JSONObject(reqData));
    }

    protected Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        return true;
    }

    protected Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        return true;
    }


    protected Boolean cancelCard(String cardId) {
        return true;
    }


    protected JSONObject getReqRes(String reqId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("type", "apply_card");
        reqData.put("requestId", reqId);
        String path = "vcc/openApi/v4/getRequestResult";
        return this.sendReq(path, Method.GET, new JSONObject(reqData));
    }


    public Boolean recharge(String cardId, BigDecimal amount) {
        return true;
    }


    @Autowired
    RsaService rsaService;

    protected String genSign(JSONObject body) {
        String bodyStr = JSONUtil.toJsonStr(body);
        String sign = rsaService.genSign("photon", bodyStr, SignAlgorithm.MD5withRSA);
        this.headers.put("X-PD-SIGN", sign);
        return sign;
    }

    @Override
    protected Boolean updateContact(String cardId, String email) {
        return true;
    }

    protected Boolean checkSign(String body, String sign) {
        return true;
    }


    public static String getNextCardType() {
        if (index >= cardTypes.length) {
            index = 0; // 重置索引或抛出异常，取决于你的需求
        }
        return cardTypes[index++];
    }
}
