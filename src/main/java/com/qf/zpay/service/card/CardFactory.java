package com.qf.zpay.service.card;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.dto.CardDetailDto;
import generator.domain.UserCardCategoryConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 卡片工厂用于调度上游开卡，并转换上游卡数据对外提供服务
 *
 * <AUTHOR>
 * @updateTime 2024/6/3 10:41
 */
@Service
@Slf4j
public class CardFactory {

    @Value("${card.test}")
    private Boolean cardTest;
    @Autowired
    private ApplicationContext appCtx;

    private CardBuilder cardBuilder;
    private CardChannelEnum channel;


    public void getBuilder(CardChannelEnum channel) {
        this.channel = channel;
        if (cardTest) {
            this.channel = CardChannelEnum.ZNet;
        }
        this.cardBuilder = appCtx.getBean(this.channel.getCode(), CardBuilder.class);
        this.cardBuilder.init();
    }


    /**
     * 开卡
     *
     * @param cardCode 卡片标识
     * @param amount   开卡时设置金额
     * @return CardInfoDto
     */
    public CardDetailDto openCard(String cardCode, BigDecimal amount) {
        CardDetailDto cardInfoDto = new CardDetailDto();
        JSONObject data;
        switch (this.channel) {
            case Photon, Photon2, GSalary, ZNet:
                data = cardBuilder.openCard(cardCode, amount);
                cardInfoDto.setCardId(data.getStr("cardId"));
                cardInfoDto.setCardNumber(data.getStr("cardNo"));
                cardInfoDto.setCardScheme(data.getStr("cardScheme"));
                cardInfoDto.setCardCurrency(data.getStr("cardCurrency"));
                cardInfoDto.setAmount(data.getBigDecimal("availableTransactionLimit"));
                cardInfoDto.setCardStatus(CardStatusEnum.Active);
                cardInfoDto.setCardCvv(data.getStr("cvv"));
                cardInfoDto.setCardExpirationMmyy(data.getStr("expirationDate"));
                cardInfoDto.setApplyTime(new Date());
                break;
            case Asinx:
                data = cardBuilder.openCard(cardCode, amount);
                cardInfoDto.setCardId(data.getStr("bankCardId"));
                cardInfoDto.setCardNumber(data.getStr("cardNo"));
                cardInfoDto.setAmount(BigDecimal.ZERO);
                cardInfoDto.setCardStatus(CardStatusEnum.Active);
                cardInfoDto.setCardCurrency(data.getStr("currency"));
                cardInfoDto.setCardCvv(data.getStr("cardCvv"));
                cardInfoDto.setCardExpirationMmyy(data.getStr("expiryDate"));
                cardInfoDto.setApplyTime(new Date());
                break;
            case ZPhysical, AsinxPhysical:
                // 手动实体卡
                data = cardBuilder.openCard(cardCode, amount);
                data.toBean(CardDetailDto.class);
                log.error("data: {}", data);
                break;
            default:
                Assert.fail(ResultCode.SYS_ERR);
                break;
        }
        return cardInfoDto;
    }

    /**
     * 卡片详情
     *
     * @param cardId 卡ID
     * @return CardInfoDto
     */
    public CardDetailDto getCardDetail(String cardId) {
        CardDetailDto cardInfoDto = new CardDetailDto();
        switch (this.channel) {
            case Photon, Photon2:
                JSONObject data = cardBuilder.getCardDetail(cardId);
                cardInfoDto.setCardId(data.getStr("cardId"));
                cardInfoDto.setCardNumber(data.getStr("cardNo"));
                cardInfoDto.setCardScheme(data.getStr("cardScheme"));
                cardInfoDto.setCardCurrency(data.getStr("cardCurrency"));
                cardInfoDto.setAmount(data.getBigDecimal("availableTransactionLimit"));
                cardInfoDto.setCardStatus(ChannelConfig.switchStatus(this.channel.getCode(), data.getStr("cardStatus")));

                JSONObject cardCvv = cardBuilder.getCardCvv(cardId);
                cardInfoDto.setCardCvv(cardCvv.getStr("cvv"));
                cardInfoDto.setCardExpirationMmyy(cardCvv.getStr("expirationDate"));
                break;
            case GSalary:
                JSONObject data2 = cardBuilder.getCardDetail(cardId);
                cardInfoDto.setCardId(data2.getStr("cardId"));
                cardInfoDto.setCardScheme(data2.getStr("cardScheme"));
                cardInfoDto.setCardCurrency(data2.getStr("cardCurrency"));
                cardInfoDto.setAmount(data2.getBigDecimal("availableTransactionLimit"));
                cardInfoDto.setCardStatus(ChannelConfig.switchStatus(this.channel.getCode(), data2.getStr("cardStatus")));

                JSONObject cardCvv2 = cardBuilder.getCardCvv(cardId);
                cardInfoDto.setCardNumber(cardCvv2.getStr("cardNo"));
                cardInfoDto.setCardCvv(cardCvv2.getStr("cvv"));
                cardInfoDto.setCardExpirationMmyy(cardCvv2.getStr("expirationDate"));
                break;
            case Asinx, AsinxPhysical:
                JSONObject data4 = cardBuilder.getCardDetail(cardId);
                cardInfoDto.setCardId(data4.getStr("bankCardId"));
                cardInfoDto.setCardCurrency(data4.getStr("currency"));
                cardInfoDto.setCardStatus(ChannelConfig.switchStatus(this.channel.getCode(), data4.getStr("userBankCardStatus")));
                cardInfoDto.setCardNumber(data4.getStr("cardNo"));
                cardInfoDto.setCardCvv(data4.getStr("cardCvv"));
                cardInfoDto.setCardExpirationMmyy(data4.getStr("expiryDate"));
                cardInfoDto.setAmount(data4.getBigDecimal("balance"));
                break;
            case ZNet:
                JSONObject data3 = cardBuilder.getCardDetail(cardId);
                cardInfoDto.setCardId(data3.getStr("cardId"));
                cardInfoDto.setCardNumber(data3.getStr("cardNo"));
                cardInfoDto.setCardScheme(data3.getStr("cardScheme"));
                cardInfoDto.setCardCurrency(data3.getStr("cardCurrency"));
                cardInfoDto.setAmount(data3.getBigDecimal("availableTransactionLimit"));
                cardInfoDto.setCardStatus(ChannelConfig.switchStatus(this.channel.getCode(), data3.getStr("cardStatus")));
                cardInfoDto.setCardCvv(data3.getStr("cvv"));
                cardInfoDto.setCardExpirationMmyy(data3.getStr("expirationDate"));
                break;
            case ZPhysical:
                cardInfoDto = cardBuilder.getCardDetail(cardId).toBean(CardDetailDto.class);
                break;
            default:
                Assert.fail(ResultCode.SYS_ERR);
                break;
        }
        return cardInfoDto;
    }


    /**
     * 卡片充值
     *
     * @param cardId 卡ID
     * @param amount 充值金额
     * @return Boolean
     */
    public Boolean recharge(String cardId, BigDecimal amount) {
        Boolean res = false;
        switch (this.channel) {
            case Photon, Photon2, GSalary, ZNet:
                HashMap<String, Object> param = new HashMap<>();
                param.put("amount", amount.abs());
                String changeType = amount.compareTo(BigDecimal.ZERO) > 0 ? "increase" : "decrease";
                param.put("changeType", changeType);
                res = cardBuilder.setCardLimit(cardId, param);
                break;
            case Asinx, AsinxPhysical:
                HashMap<String, Object> param2 = new HashMap<>();
                param2.put("amount", amount.abs());
                res = cardBuilder.setCardLimit(cardId, param2);
                break;
            case ZPhysical:
                res = cardBuilder.recharge(cardId, amount);
                break;
            default:
                Assert.fail(ResultCode.SYS_ERR);
                break;
        }
        return res;
    }


    /**
     * 更改卡片状态
     *
     * @param cardId 卡ID
     * @param status 操作状态
     * @return Boolean
     */
    public Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        checkBuilder();
        return cardBuilder.changeStatus(cardId, status);
    }


    /**
     * 注销卡片
     *
     * @param cardId 卡ID
     * @return Boolean
     */
    public Boolean cancelCard(String cardId) {
        checkBuilder();
        return cardBuilder.cancelCard(cardId);
    }

    public Boolean updateContact(String cardId, String email) {
        checkBuilder();
        return cardBuilder.updateContact(cardId, email);
    }


    /**
     * 检查签名
     *
     * @param data 原始数据字符串
     * @param sign 签名串
     * @return Boolean
     */
    public Boolean checkSign(String data, String sign) {
        checkBuilder();
        if (data.isEmpty() || sign.isEmpty()) {
            Assert.fail(ResultCode.PARAM_ERR);
        }
        return cardBuilder.checkSign(data, sign);
    }


    private void checkBuilder() {
        if (this.cardBuilder == null) {
            log.error("卡渠道未初始化");
            Assert.fail(ResultCode.SYS_ERR);
        }
    }

    /**
     * 添加持卡人；
     *
     * @param map 持卡人数据；
     */
    public JSONObject savaCardHolders(Map<String, Object> map) {
        checkBuilder();
        return cardBuilder.savaCardHolders(map);
    }

    /**
     * 添加卡产品
     *
     * @return
     */
    public ArrayList<UserCardCategoryConfig> getCardProducts() {
        ArrayList<UserCardCategoryConfig> list = new ArrayList<>();

        checkBuilder();
        JSONObject data = cardBuilder.getCardProducts();
        Console.error("Products: {}", data);
        switch (this.channel) {
            case GSalary:
                JSONArray products = data.getJSONArray("products");
                for (int i = 0; i < products.size(); i++) {
                    JSONObject jsonObject = products.getJSONObject(i);
                    UserCardCategoryConfig userCardCategoryConfig = new UserCardCategoryConfig();
                    userCardCategoryConfig.setCardCurrency(jsonObject.getStr("currency"));
                    userCardCategoryConfig.setCardScheme(jsonObject.getStr("brand_code"));
                    userCardCategoryConfig.setCardModel(jsonObject.getStr("card_type").equals("VIRTUAL") ? "share" : "physical");
                    userCardCategoryConfig.setProductCode(jsonObject.getStr("product_code"));
                    userCardCategoryConfig.setProductName(jsonObject.getStr("product_name"));
                    userCardCategoryConfig.setChannel(CardChannelEnum.GSalary);
                    userCardCategoryConfig.setSource(CardChannelEnum.GSalary.getCode());
                    list.add(userCardCategoryConfig);
                }
                break;
            case Asinx:
                JSONArray prod = data.getJSONArray("data");
                for (Object obj : prod) {
                    JSONObject item = (JSONObject) obj;
                    UserCardCategoryConfig userCardCategoryConfig = new UserCardCategoryConfig();
                    userCardCategoryConfig.setCardCurrency(item.getStr("ccy"));
                    userCardCategoryConfig.setCardScheme(item.getStr("bankCardType"));
                    userCardCategoryConfig.setCardModel(item.getStr("bankCardNature").equals("VIRTUAL") ? "share" : "physical");
                    userCardCategoryConfig.setProductCode(item.getStr("id"));
                    userCardCategoryConfig.setProductName(item.getStr("cardBin"));
                    userCardCategoryConfig.setChannel(CardChannelEnum.Asinx);
                    userCardCategoryConfig.setSource(CardChannelEnum.Asinx.getCode());
                    list.add(userCardCategoryConfig);
                }
                break;
            default:
                Assert.fail(ResultCode.SYS_ERR);
                break;
        }
        return list;
    }
}
