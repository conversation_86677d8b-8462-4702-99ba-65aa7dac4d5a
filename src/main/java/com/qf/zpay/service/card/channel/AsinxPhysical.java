package com.qf.zpay.service.card.channel;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.dto.req.UCardDto;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardBuilder;
import generator.domain.UserCard;
import generator.service.UserCardService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * Asinx实体卡通道处理
 *
 * @doc <a href="https://25q4gdveuf.apifox.cn">...</a>
 */
@Log4j2
@Service
public class AsinxPhysical extends CardBuilder {

    @Value("${card.channel.asinx.appId}")
    private String appId;

    @Value("${card.channel.asinx.appSecret}")
    private String appSecret;

    @Value("${card.channel.asinx.domain}")
    private String domain;

    @Value("${card.channel.asinx.uid}")
    private String uid;


    private HashMap<String, String> headers = new HashMap<>();

    @Autowired
    UserCardService userCardService;


    public AsinxPhysical() {
        headers.put("Content-Type", "application/json;charset=UTF-8");
    }

    @Override
    protected void init() {
    }

    @Override
    public JSONObject getCardProducts() {
        String path = "/bankcard/template/list";
        JSONObject body = new JSONObject();
        return sendReq(path, body);
    }

    @Override
    protected JSONObject openCard(String cardCode, BigDecimal amount) {
        //
        Integer cardManageId = amount.intValue();
        UserCard userCard = userCardService.getOneEmptyCard(cardCode, cardManageId);
        if (userCard == null) {
            Assert.fail(ResultCode.NO_CARDS);
        }
        userCard.setCardId(userCard.getOrderNo());
        return new JSONObject(userCard);
    }

    @Override
    public JSONObject getCardDetail(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/cardInfo";
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        JSONObject detail = this.sendReq(path, body);
        JSONObject amount = getCardAmount(cardId);
        detail.putOnce("balance", amount.getBigDecimal("balance"));
        return detail;
    }

    public JSONObject setBankcardPin(String cardId, String pin) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        body.putOnce("pin", pin);
        String path = "/bankcard/setPin";
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        JSONObject detail = this.sendReq(path, body);
        return detail;
    }

    public JSONObject getPin(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/getPin";
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        JSONObject detail = this.sendReq(path, body);
        return detail;
    }

    private JSONObject getCardAmount(String cardId) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        String path = "/bankcard/balance";
        return this.sendReq(path, body);
    }


    @Override
    public Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        body.putOnce("amount", 0);
        body.putOnce("targetAmount", param.get("amount"));
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        String path = "/bankcard/recharge";
        JSONObject res = this.sendReq(path, body);
        JSONObject res2 = getReqRes(cardId, res.getStr("requestOrderId"));
        return "SUCCESS".equals(res2.getStr("status")) || "ACCEPT".equals(res2.getStr("status"));
    }


    @Override
    public Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        Boolean enable = status == CardStatusActionEnum.Unfreeze;
        JSONObject body = new JSONObject();
        body.putOnce("userBankcardId", cardId);
        body.putOnce("enable", enable);
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        String path = "/bankcard/update/status";
        JSONObject res = this.sendReq(path, body);
        return res.getBool("success");
    }


    @Override
    public Boolean cancelCard(String cardId) {
        JSONObject body = new JSONObject();
        String upUid = userCardService.getUpUid(cardId);
        body.putOnce("uid", upUid);
        body.putOnce("userBankcardId", cardId);
        String path = "/bankcard/close";
        JSONObject res = this.sendReq(path, body);
        JSONObject res2 = getReqRes(cardId, res.getStr("requestOrderId"));
        return "ACCEPT".equals(res2.getStr("status"));
    }


    public Map<String, Object> uCardKYCAuth(UCardDto dto, String productId) {
        // 用户创建
        String uid = userRegister(dto.getMobilePrefix(), dto.getPhoneNumber(), dto.getEmail());
        log.info("【kyc用户】：uid：{}", uid);
        // kyc实名
        uCardKYCApply(dto, uid, productId);
        // 分配
        String userBankcardId = assignCard(dto.getCardNo(), uid, productId);
        Map<String, Object> result = new HashMap<>();
        result.put("uid", uid);
        result.put("cardId", userBankcardId);
        JSONObject jsonObject = kycStatusQuery(uid);
        log.info("【kyc状态】：{}", jsonObject.toString());
        return result;
    }

    public JSONObject kycStatusQuery(String uid) {
        JSONObject body = new JSONObject();
        String path = "/UCard/kyc/status";
        body.putOnce("uid", uid);
        return this.sendReq(path, body);
    }


    /**
     * user register,get user unique
     *
     * @param mobilePrefix mobile prefix
     * @param mobileNumber mobile number
     */
    public String userRegister(String mobilePrefix, String mobileNumber, String email) {
        JSONObject body = new JSONObject();
        body.putOnce("mobilePrefix", mobilePrefix);
        body.putOnce("mobileNumber", mobileNumber);
        body.putOnce("email", email);
        String path = "/user/register";
        JSONObject res = this.sendReq(path, body);
        return res.getStr("uid");
    }

    /**
     * user register,get user unique
     */
    public void uCardKYCApply(UCardDto dto, String uid, String productId) {
        JSONObject body = new JSONObject();

        Map<String, String> holderInfo = new HashMap<String, String>();
        holderInfo.put("email", dto.getEmail());
        holderInfo.put("first_name", dto.getFirstName());
        holderInfo.put("last_name", dto.getLastName());
        holderInfo.put("date_of_birth", dto.getDateOfBirth());
        holderInfo.put("country_code", dto.getCountryCode());
        holderInfo.put("phone_number", dto.getPhoneNumber());

//        Map<String, String> deliveryAddress = new HashMap<String, String>();
//        deliveryAddress.put("country", dto.getCountryCode());
//        deliveryAddress.put("city", dto.getCity());
//        deliveryAddress.put("state", dto.getState());
//        deliveryAddress.put("line1", dto.getLine1());
//        deliveryAddress.put("line2", dto.getLine2());
//        deliveryAddress.put("postal_code", dto.getPostalCode());
//        body.putOnce("deliveryAddress", deliveryAddress);
        body.putOnce("holderInfo", holderInfo);
        body.putOnce("attemptBankcardId", productId);
        body.putOnce("uid", uid);
        String path = "/UCard/kyc/apply";
        JSONObject res = this.sendReq(path, body);
        log.info("【kyc认证】：{}", res.toString());
    }

    /**
     * assign card
     *
     * @param
     */
    public String assignCard(String cardNo, String uid, String bankcardId) {
        JSONObject body = new JSONObject();
        body.putOnce("cardNumber", cardNo);
        body.putOnce("bankcardId", bankcardId);
        body.putOnce("autoActive", "true");
        body.putOnce("uid", uid);
        String path = "/UCard/assign";
        JSONObject res = this.sendReq(path, body);
        log.info("【kyc分配】：{}", res.toString());
        return res.getStr("userBankcardId");
    }

    public JSONObject getReqRes(String cardId, String reqId) {
        HashMap<String, Object> reqData = new HashMap<>();
        reqData.put("userBankcardId", cardId);
        reqData.put("requestOrderId", reqId);
        String upUid = userCardService.getUpUid(cardId);
        reqData.put("uid", upUid);
        String path = "/bankcard/cardOrderInfo";
        return this.sendReq(path, new JSONObject(reqData));
    }


    protected String genSign(JSONObject body, String path) {
        JSONObject sortedObj = JSONUtil.parseObj(body.toBean(TreeMap.class));
        String content = path + sortedObj.toString();
        String base64String = Base64.encode(content);

        AES aes = SecureUtil.aes(HexUtil.decodeHex(appSecret));
        String encrypt = aes.encryptHex(base64String, CharsetUtil.CHARSET_UTF_8);
        return SecureUtil.md5(encrypt);
    }

    @Override
    protected JSONObject sendReq(String path, Method method, JSONObject body) {
        String url = UrlBuilder.of(domain).addPath(path).build();
        headers.put("appId", appId);
        headers.put("uId", body.getStr("uid"));
        body.remove("uid");
        String sign = genSign(body, path);
        headers.put("sign", sign);

        JSONObject resObj = this.sendNow(url, method, headers, body);
//        Console.error("resObj", resObj);
        boolean hasErr = false;
        if (!resObj.getInt("code").equals(1)) {
            hasErr = true;
            if (resObj.getInt("code").equals(99997)) {
                hasErr = false;
            }
        }
        if (hasErr) {
            log.error("Asinx接口报错：请求：{}，响应：{}", body, resObj);
            Assert.fail(ResultCode.UP_ERR);
        }
        String result = resObj.getStr("result");
        if (result == null) {
            return resObj;
        }
        AES aes = SecureUtil.aes(HexUtil.decodeHex(appSecret));
        String dataStr = Base64.decodeStr(aes.decryptStr(result, CharsetUtil.CHARSET_UTF_8));
//        Console.error("dataStr", dataStr);
        if (resObj.containsKey("total")) {
            JSONObject data = new JSONObject();
            data.putOnce("total", resObj.getInt("total"));
            data.putOnce("data", JSONUtil.parseArray(dataStr));
            return data;
        } else {
            return JSONUtil.parseObj(dataStr);
        }
    }


    protected JSONObject sendReq(String path, JSONObject body) {
        return sendReq(path, Method.POST, body);
    }

}
