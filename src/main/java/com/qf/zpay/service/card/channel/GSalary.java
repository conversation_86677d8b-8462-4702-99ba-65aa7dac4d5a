package com.qf.zpay.service.card.channel;


import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.qf.zpay.constants.CardStatusActionEnum;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardBuilder;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import com.qf.zpay.util.ZHelperUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @updateTime 2024/8/20 18:11
 */
@Log4j2
@Service
public class GSalary extends CardBuilder {

    @Autowired
    ZHelperUtil zHelper;

    @Value("${GSalary.card.holder}")
    private String holder;

    /**
     * @param cardCode 上游卡标识
     * @param amount   金额
     * @return ;
     */
    @Override
    protected JSONObject openCard(String cardCode, BigDecimal amount) {
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
            reqMap.put("product_code", cardCode); // 卡产品编码
            reqMap.put("currency", "USD"); // 卡币种
            reqMap.put("card_holder_id", holder); // 注册的持卡人id
            reqMap.put("limit_per_day", 50000); // 每日交易限额
            reqMap.put("limit_per_month", 50000); // 每月交易限额
            reqMap.put("limit_per_transaction", 20000); // 单笔交易限额
            reqMap.put("init_balance", amount); // 初始额度
            String path = "/v1/card_applies";
            JSONObject entries = zHelper.sendReqGSalary(path, reqMap);
            String status = entries.getStr("status");
            if (status.equals("FAILED")) {
                throw new ApiException(ResultCode.OPEN_CARD_ERROR);
            }
            String requestId = entries.getStr("request_id");
            String path2 = "/v1/card_applies/" + requestId;
            Thread.sleep(1500);
            JSONObject entries2 = zHelper.sendReqGetGSalary(path2, null);
            JSONObject jsonObject = entries2.getJSONObject("card_detail");
            String cardId = jsonObject.getStr("card_id");
            String path3 = "/v1/cards/" + cardId + "/secure_info";
            JSONObject entries3 = zHelper.sendReqGetGSalary(path3, null);
            JSONObject entries1 = new JSONObject();
            entries1.putOpt("cardId", cardId);
            entries1.putOpt("cardNo", entries3.getStr("pan"));
            entries1.putOpt("cardScheme", jsonObject.getStr("brand_code"));
            entries1.putOpt("cardCurrency", jsonObject.getStr("card_currency"));
            entries1.putOpt("availableTransactionLimit", jsonObject.getStr("available_balance"));
            entries1.putOpt("cvv", entries3.getStr("cvv"));
            entries1.putOpt("expirationDate", entries3.getStr("expire_month") + "/" + entries3.getStr("expire_year"));
            return entries1;
        } catch (JsonProcessingException e) {
            log.error("开卡异常{}", e.getMessage());
            throw new RuntimeException("开卡异常" + e.getMessage());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public String openCard2(String cardCode, BigDecimal amount) {
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
            reqMap.put("product_code", cardCode); // 卡产品编码
            reqMap.put("currency", "USD"); // 卡币种
            reqMap.put("card_holder_id", holder); // 注册的持卡人id
            reqMap.put("limit_per_day", 50000); // 每日交易限额
            reqMap.put("limit_per_month", 50000); // 每月交易限额
            reqMap.put("limit_per_transaction", 20000); // 单笔交易限额
            reqMap.put("init_balance", amount); // 初始额度
            String path = "/v1/card_applies";
            JSONObject entries = zHelper.sendReqGSalary(path, reqMap);
            String status = entries.getStr("status");
            if (status.equals("FAILED")) {
                throw new ApiException(ResultCode.OPEN_CARD_ERROR);
            }
            return entries.getStr("request_id");
        } catch (JsonProcessingException e) {
            log.error("开卡异常{}", e.getMessage());
            throw new RuntimeException("开卡异常" + e.getMessage());
        }
    }


    public JSONObject findOpenCardResult(String requestId) {
        String path2 = "/v1/card_applies/" + requestId;
        JSONObject entries2 = zHelper.sendReqGetGSalary(path2, null);
        if (!entries2.containsKey("card_detail")) {
            return null;
        }
        JSONObject jsonObject = entries2.getJSONObject("card_detail");
        String cardId = jsonObject.getStr("card_id");
        String path3 = "/v1/cards/" + cardId + "/secure_info";
        JSONObject entries3 = zHelper.sendReqGetGSalary(path3, null);
        JSONObject entries1 = new JSONObject();
        entries1.putOpt("cardId", cardId);
        entries1.putOpt("cardNo", entries3.getStr("pan"));
        entries1.putOpt("cardScheme", jsonObject.getStr("brand_code"));
        entries1.putOpt("cardCurrency", jsonObject.getStr("card_currency"));
        entries1.putOpt("availableTransactionLimit", jsonObject.getStr("available_balance"));
        entries1.putOpt("cvv", entries3.getStr("cvv"));
        entries1.putOpt("expirationDate", entries3.getStr("expire_month") + "/" + entries3.getStr("expire_year"));
        return entries1;
    }


    /**
     * 获取卡详情
     *
     * @param cardId 卡ID ；
     * @return ；
     */
    @Override
    protected JSONObject getCardDetail(String cardId) {
        String path = "/v1/cards/" + cardId;
        JSONObject entries = zHelper.sendReqGetGSalary(path, null);
        JSONObject entries1 = new JSONObject();
        entries1.putOpt("cardId", entries.getStr("card_id"));
        entries1.putOpt("cardScheme", entries.getStr("brand_code"));
        entries1.putOpt("cardCurrency", entries.getStr("card_currency"));
        entries1.putOpt("availableTransactionLimit", entries.getStr("available_balance"));
        entries1.putOpt("cardStatus", entries.getStr("status"));
        return entries1;
    }

    /**
     * @param cardId 卡ID
     * @return ;
     */
    protected JSONObject getCardCvv(String cardId) {
        String path3 = "/v1/cards/" + cardId + "/secure_info";
        JSONObject entries3 = zHelper.sendReqGetGSalary(path3, null);
        JSONObject entries = new JSONObject();
        entries.putOpt("cardNo", entries3.getStr("pan"));
        entries.putOpt("cvv", entries3.getStr("cvv"));
        entries.putOpt("expirationDate", entries3.getStr("expire_month") + "/" + entries3.getStr("expire_year"));
        return entries;
    }

    /**
     * @param cardId 卡ID
     * @param status 状态 freeze 冻结 unfreeze 解冻
     * @return ;
     */
    @Override
    protected Boolean changeStatus(String cardId, CardStatusActionEnum status) {
        try {
            String path3 = "/v1/cards/" + cardId + "/freeze_status";
            Map<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("freeze", status.getCode().equals("freeze"));
            zHelper.sendReqPutGSalary(path3, stringObjectHashMap);
            return Boolean.TRUE;
        } catch (JsonProcessingException e) {
            log.error("更改卡片状态异常{}", e.getMessage());
            throw new RuntimeException("更改卡片状态异常" + e.getMessage());
        }
    }

    @Override
    protected Boolean cancelCard(String cardId) {
        String path3 = "/v1/cards/" + cardId;
        String request = zHelper.sendReqDleGSalary(path3, null);
        JSONObject jsonObject = new JSONObject(request);
        String result1 = jsonObject.getJSONObject("result").getStr("result");
        if (result1.equals("S")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 添加持卡人
     */
    @Override
    protected JSONObject savaCardHolders(@RequestBody Map<String, Object> map) {
        try {
            String path = "/v1/card_holders";
            return zHelper.sendReqGSalary(path, map);
        } catch (JsonProcessingException e) {
            log.error("添加持卡人异常{}", e.getMessage());
            throw new RuntimeException("添加持卡人异常" + e.getMessage());
        }
    }

    /**
     *
     */
    @Override
    protected JSONObject getCardProducts() {
        String path = "/v1/card_support/products";
        return zHelper.sendReqGetGSalary(path, null);
    }

    @Override
    protected Boolean setCardLimit(String cardId, HashMap<String, Object> param) {
        try {
            String path = "/v1/cards/balance_modifies";
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("card_id", cardId);
            stringObjectHashMap.put("amount", param.get("amount"));
            stringObjectHashMap.put("type", param.get("changeType").toString().equals("increase") ? "INCREASE" : "DECREASE");
            stringObjectHashMap.put("request_id", UniqueIdGeneratorUtil.generateUniqueId("Z-REQ"));
            JSONObject entries = zHelper.sendReqGSalary(path, stringObjectHashMap);
            if (entries.getStr("status").equals("FAILED")) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (JsonProcessingException e) {
            log.error("修改卡片限额异常{}", e.getMessage());
            throw new RuntimeException("修改卡片限额异常" + e.getMessage());
        }
    }

    @Override
    protected Boolean updateContact(String cardId, String email) {
        try {
            String path = "/v1/cards/" + cardId + "/contact";
            Map<String, Object> body = new HashMap<>();
            body.put("email", email);
            zHelper.sendReqPutGSalary(path, body);
            return Boolean.TRUE;
        } catch (JsonProcessingException e) {
            log.error("更改卡片联系人异常{}", e.getMessage());
            throw new RuntimeException("更改卡片状态异常" + e.getMessage());
        }
    }
}
