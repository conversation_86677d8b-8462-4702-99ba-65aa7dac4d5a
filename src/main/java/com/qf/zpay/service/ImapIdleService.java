package com.qf.zpay.service;

import com.qf.zpay.config.ImapConfig;
import com.sun.mail.imap.IMAPFolder;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.Session;
import jakarta.mail.Store;
import jakarta.mail.event.MessageCountAdapter;
import jakarta.mail.event.MessageCountEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @updateTime 2024/10/27 14:21
 */
@Slf4j
@Service
public class ImapIdleService {

    private ImapConfig config;

    public ImapIdleService(ImapConfig config) {
        this.config = config;
    }

    public void listening(ImapConfig config, Consumer<Message> consumer) {
        this.config = config;
        listening(consumer);
    }

    public void listening(Consumer<Message> consumer) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                Properties props = new Properties();
                props.put("mail.store.protocol", "imaps");
                props.put("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
                props.put("mail.imap.socketFactory.fallback", "false");
                Session session = Session.getInstance(props, null);
                Store store = session.getStore("imaps");
                store.connect(config.getHost(), config.getPort(), config.getUser(), config.getPass());
                log.info("IMAP connection successful {}", config);

                Folder folder = store.getFolder("INBOX");
                if (folder instanceof IMAPFolder imapFolder) {
                    log.info("IMAPFolder opened successfully");
                    imapFolder.open(Folder.READ_ONLY);
                    imapFolder.addMessageCountListener(new MessageCountAdapter() {
                        @Override
                        public void messagesAdded(MessageCountEvent event) {
                            Message[] messages = event.getMessages();
                            for (Message msg : messages) {
                                consumer.accept(msg);
                            }
                        }

                        @Override
                        public void messagesRemoved(MessageCountEvent event) {
                            // Handle message removal
                        }
                    });

                    ScheduledExecutorService noopScheduler = Executors.newSingleThreadScheduledExecutor();
                    noopScheduler.scheduleAtFixedRate(() -> sendNoop(imapFolder), 0, 30, TimeUnit.SECONDS);

                    while (true) {
                        try {
                            imapFolder.idle();
                        } catch (Exception e) {
                            log.error("IMAP IDLE failed: {}", e.getMessage());
                            break;
                        }
                    }
                    noopScheduler.shutdown();
                } else {
                    log.error("Failed to open IMAPFolder");
                }
            } catch (Exception e) {
                log.error("IMAP IDLE listening failed: {}", e.getMessage());
            }
        }, 0, 10, TimeUnit.SECONDS);
    }


    private void sendNoop(IMAPFolder imapFolder) {
        try {
            imapFolder.doCommand(p -> {
                p.simpleCommand("NOOP", null);
                return null;
            });
        } catch (Exception e) {
            log.error("Failed to send NOOP command: {}", e.getMessage());
        }
    }
}