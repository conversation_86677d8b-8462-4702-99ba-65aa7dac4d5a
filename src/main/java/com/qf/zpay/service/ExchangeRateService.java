package com.qf.zpay.service;

import com.qf.zpay.constants.CardTransTypeEnum;
import com.qf.zpay.util.DateUtil;
import com.qf.zpay.util.SeleniumService;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import generator.service.UserCardLogService;
import generator.service.UserCardService;
import lombok.extern.log4j.Log4j2;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;


@Log4j2
@Service
public class ExchangeRateService {

    public static final String EXCHANGE_RATE_KEY = "exchangeRates";

    @Autowired
    UserCardService userCardService;
    @Autowired
    UserCardLogService userCardLogService;

    @Autowired
    private RedissonClient redis;

    @Autowired
    GlobalcashService globalcashService;

    @Autowired
    SeleniumService seleniumService;

    /**
     * 模拟登录 获取cookies
     *
     * @throws InterruptedException 错误
     */
    public void Login() {
        RLock login = redis.getLock("Login");
        login.lock();
        ChromeDriver driver = null;
        try {
            HashMap<String, UserCardLog> stringUserCardLogHashMap = new HashMap<>();
            RBucket<String> codeCache2 = redis.getBucket("qxf_code");
            codeCache2.delete();
            // 设置并初始化 Chrome WebDriver
            driver = seleniumService.getInstance("globalcash");
            // 显式等待用户名输入框的出现，使用 Duration.ofSeconds
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(40));
            wait.until(ExpectedConditions.urlContains("/v4/cbc"));
            // 进一步确保页面加载完成，可以使用以下等待：
            wait.until(ExpectedConditions.jsReturnsValue("return document.readyState=='complete'"));
            driver.get("https://merchant.globalcash.cn/v4/cbc/trade");
            wait.until(ExpectedConditions.jsReturnsValue("return document.readyState=='complete'"));
            Thread.sleep(5000);
            // 定位按钮元素
            WebElement queryButton = driver.findElement(By.xpath("//button[span[text()='查询']]"));
            queryButton.click();
            Thread.sleep(10000);
            WebElement element = driver.findElement(By.cssSelector("div.el-pagination.is-background"));
            WebElement element1 = element.findElement(By.tagName("ul"));
            List<WebElement> li = element1.findElements(By.tagName("li"));
            extracted(driver, stringUserCardLogHashMap);
            for (int g = 1; g < li.size(); g++) {
                WebElement button = driver.findElement(By.cssSelector("button.btn-next"));
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", button);
                Thread.sleep(10000);
                WebElement tbodyWrapper = driver.findElement(By.cssSelector("div.el-table__body-wrapper.is-scrolling-left"));
                // 在该 <div> 内查找 <tbody> 元素
                WebElement tbody = tbodyWrapper.findElement(By.tagName("tbody"));
                // 获取所有的 <tr> 行元素
                List<WebElement> rows = tbody.findElements(By.tagName("tr"));
                for (WebElement row : rows) {
                    List<WebElement> cells = row.findElements(By.tagName("td"));
                    Map<Integer, String> rowData = new LinkedHashMap<>();
                    for (int i = 0; i < cells.size(); i++) {
                        String text = cells.get(i).getText();
                        rowData.put(i, text);
                    }

                    if (rowData.get(1).equals("卡号") && !rowData.get(17).equals("账户验证-1103") && !rowData.get(17).equals("代充值-0100")) {
                        Boolean orderIdExists = userCardLogService.isOrderIdExists(rowData.get(14));
                        if (orderIdExists) {
                            break;
                        }
                        addUserCardLog(rowData, stringUserCardLogHashMap);
                    }
                }
            }
//            log.error(stringUserCardLogHashMap);
            if (!stringUserCardLogHashMap.isEmpty()) {
                for (Map.Entry<String, UserCardLog> entry : stringUserCardLogHashMap.entrySet()) {
                    UserCard byCardNumber = userCardService.getByCardNumber(entry.getKey());
                    if (byCardNumber != null) {
                        UserCardLog value = entry.getValue();
                        byCardNumber.setAmount(value.getAvailableCredit());
                        userCardService.updateById(byCardNumber);
                    }
                }
            }

        } catch (Exception e) {
            log.error("采集卡片日志定时任务失败", e);
        } finally {
            driver.quit();
            if (login.isHeldByCurrentThread()) {
                login.unlock(); // 解锁
            }
        }

    }

    private void extracted(ChromeDriver driver, HashMap<String, UserCardLog> stringUserCardLogHashMap) {
        WebElement tbodyWrapper = driver.findElement(By.cssSelector("div.el-table__body-wrapper.is-scrolling-left"));
        // 在该 <div> 内查找 <tbody> 元素
        WebElement tbody = tbodyWrapper.findElement(By.tagName("tbody"));
        // 获取所有的 <tr> 行元素
        List<WebElement> rows = tbody.findElements(By.tagName("tr"));
        // 遍历每一行并将数据与表头关联
        for (WebElement row : rows) {
            List<WebElement> cells = row.findElements(By.tagName("td"));
            Map<Integer, String> rowData = new LinkedHashMap<>();
            for (int i = 0; i < cells.size(); i++) {
                String text = cells.get(i).getText();
                rowData.put(i, text);
            }
            if (rowData.get(1).equals("卡号") && !rowData.get(17).equals("账户验证-1103") && !rowData.get(17).equals("代充值-0100")) {
                addUserCardLog(rowData, stringUserCardLogHashMap);
            }
        }
    }

    /**
     *
     */
    private void addUserCardLog(Map<Integer, String> rowData, HashMap<String, UserCardLog> stringUserCardLogHashMap) {
        UserCard userCard = userCardService.getByCardNumber(rowData.get(0));
        log.info(rowData);
        if (!userCardLogService.isOrderIdExists(rowData.get(14))) {
            UserCardLog userCardLog = new UserCardLog();

            userCardLog.setOrderId(rowData.get(14));
            userCardLog.setAuthTime(DateUtil.getCurrentDateTime(rowData.get(3)));
            userCardLog.setTransAmountCurrency(rowData.get(29));
            userCardLog.setTransAmount(rowData.get(28).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(28)).abs());
            userCardLog.setAuthAmount(rowData.get(45).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(45)).abs());
            userCardLog.setSettledAmount(rowData.get(45).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(45)).abs());

            userCardLog.setMerchantName(rowData.get(11));
            userCardLog.setMerchantCountryCode(rowData.get(7));
            userCardLog.setStatus(rowData.get(20).equals("成功") ? "Settled" : "AuthFailure");
            userCardLog.setAuthAmountCurrency(rowData.get(44));
            Map<String, String> stringStringMap = statusSwitch(rowData.get(17));
            userCardLog.setFundsDirection(stringStringMap.get("fundsDirection"));
            userCardLog.setTransactionType(stringStringMap.get("transactionType"));
            userCardLog.setNoticeType(stringStringMap.get("notice_type"));
            if (rowData.get(18).equals("冲正") && rowData.get(20).equals("成功")) {
                userCardLog.setFundsDirection("Income");
                userCardLog.setTransactionType(CardTransTypeEnum.CorrectiveRefund.getCode());
            }
            userCardLog.setFailureReason(rowData.get(20).equals("成功") ? null : rowData.get(26));
            userCardLog.setAvailableCredit(rowData.get(30).isEmpty() ? BigDecimal.ZERO : new BigDecimal(rowData.get(30)).abs());
            userCardLog.setCreateTime(DateUtil.getCurrentDateTime(rowData.get(3)));
            userCardLog.setCreateDate(DateUtil.getCurrentDateTime(rowData.get(3)));
            if (userCard != null) {
                userCardLog.setBusinessId(userCard.getUid());
                userCardLog.setCardId(userCard.getCardId());
                userCardLog.setProductCode(userCard.getProductCode());
                userCardLog.setMaskCardNumber(userCard.getCardNumber());
                userCardLog.setCardModel(userCard.getCardModel());
                UserCardLog userCardLog1 = stringUserCardLogHashMap.get(userCard.getCardNumber());
                if (userCardLog1 == null && userCardLog.getAvailableCredit().compareTo(BigDecimal.ZERO) > 0) {
                    stringUserCardLogHashMap.put(userCard.getCardNumber(), userCardLog);
                }
            }
            userCardLogService.save(userCardLog);

        }
    }

    /**
     * 卡号脱敏
     *
     * @param cardNumber 卡号
     * @return 脱敏卡号
     */
    public static String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() <= 8) {
            return cardNumber;
        }
        int firstFourDigits = 4;
        int lastFourDigits = 4;
        int maskedDigits = cardNumber.length() - firstFourDigits - lastFourDigits;
        return cardNumber.substring(0, firstFourDigits) +
                "*".repeat(maskedDigits) +
                cardNumber.substring(cardNumber.length() - lastFourDigits);
    }

    public static Map<String, String> statusSwitch(String tradeType) {
        HashMap<String, String> hashMap = new HashMap<>();
        switch (tradeType) {
            case "账户验证-1103":
                hashMap.put("status", "c");
                break;
            case "账户消费-0200",
                    "ATM取款(UPI)-0208",
                    "便利店提款-0218",
                    "账户消费撤销-0930",
                    "授权清算-0202",
                    "消费(UPI)-0210",
                    "代缴费(UPI)-0209",
                    "消费撤销-0920",
                    "ATM取款授权结算(MC/visa)-0206",
                    "预授权完成(UPI)-0211",
                    "ATM余额查询-0702",
                    "预授权完成撤销-0924",
                    "代缴费撤销-0925",
                    "ATM取款授权(MC/Visa)-0205",
                    "自动授权撤销-0923",
                    "预授权-0213",
                    "消费授权撤销-0921",
                    "重新授权-0926",
                    "消费授权-0201",
                    "授权通知-0212",
                    "人工授权撤销-0922",
                    "部分撤销-0928":
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Expenditure");
                hashMap.put("transactionType", CardTransTypeEnum.Auth.getCode());
                hashMap.put("notice_type", "cardPay");
                break;
            case "代充值-0100",
                    "卡激活-0110",
                    "在线充值-0102",
                    "代充值撤销-0927",
                    "线下充值-0104",
                    "卡激活撤销-0310":
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Income");
                hashMap.put("transactionType", CardTransTypeEnum.CardRecharge.getCode());
                hashMap.put("notice_type", "cardRecharge");
                break;
            default:
                hashMap.put("status", "Settled");
                hashMap.put("fundsDirection", "Income");
                hashMap.put("transactionType", tradeType);
                hashMap.put("notice_type", "cardPay");
        }
        return hashMap;
    }

    /**
     * 获取USD到所有货币的汇率，异步存入Redis
     */
    @Async
    public void getRatesFromWeb() {
        try {
            // 定义URL
            String baseUrl = "https://www.google.com/finance/quote/USD-";
            // 货币列表
            List<String> currenciesList = new ArrayList<>(List.of("EUR", "GBP", "JPY", "CNY"));
            //获取map
            RMap<String, Double> exchangeRateMap = redis.getMap(EXCHANGE_RATE_KEY);
            // 加载页面
            for (String currency : currenciesList) {
                String url = baseUrl + currency;
                // 加载页面
                Document doc = Jsoup.connect(url).get();
                // 选取特定class的元素
                Elements elements = doc.select(".YMlKec.fxKbKc");
                // 遍历元素并存入map
                for (Element element : elements) {
                    exchangeRateMap.put(currency, Double.parseDouble(element.text()));
                }
            }
        } catch (IOException e) {
            log.error("An error occurred while fetching exchange rates from getRatesFromWeb()", e);
            throw new RuntimeException("Failed to fetch exchange rates.", e);
        }
    }
}
