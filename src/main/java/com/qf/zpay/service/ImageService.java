package com.qf.zpay.service;


import cn.hutool.json.JSONObject;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import generator.domain.CardHolder;
import generator.mapper.CardHolderMapper;
import generator.service.CardHolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ImageService {
    @Value("${file.upload-dir}")
    private String path;

    @Autowired
    CardHolderMapper cardHolderService;
    @Async
    @Retryable(retryFor = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void saveImageFromUrl(CardHolder userData) {
        try {
            // 获取全部图片地址
            List<String> imageUrls = new JSONObject(userData.getCertificateImage()).values().stream().map(Object::toString).collect(Collectors.toList());
            String signatureImageUrl = userData.getSignatureImage();
            imageUrls.add(signatureImageUrl);

            // 创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(4);

            // 并发下载图片
            List<CompletableFuture<String>> futures = imageUrls.stream()
                    .map(imageUrl -> CompletableFuture.supplyAsync(() -> {
                        try {
                            // 获取文件名
                            String imageFileName = getString(userData, imageUrl);
                            // 构建上传地址
                            Path uploadPath = Paths.get(path, userData.getBusinessId().toString(), userData.getId().toString(), imageFileName);
                            // 创建目录
                            Files.createDirectories(uploadPath.getParent());
                            // 写入图片
                            try (
                                 // 根据yrl创建字节流读取图片
                                 InputStream bis = new BufferedInputStream(new URL(imageUrl).openStream());
                                 // 创建字节写入流
                                 OutputStream fos = new BufferedOutputStream(Files.newOutputStream(uploadPath, StandardOpenOption.CREATE, StandardOpenOption.SYNC))) {
                                byte[] buffer = new byte[8192];
                                int len;
                                while ((len = bis.read(buffer)) > -1) {
                                    fos.write(buffer, 0, len);
                                }
                            }
                            return "/"+Paths.get(userData.getBusinessId().toString(), userData.getId().toString(), imageFileName).toString().replace("\\", "/");
                        } catch (IOException e) {
                            log.error(imageUrls.toString());
                            log.error("图片写入失败: {}", e.getMessage());
                            return null;
                        }
                    }, executorService))
                    .toList();

            // 等待所有任务完成并收集路径
            List<String> uploadedPaths = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()))
                    .join();

            // 拼接图片路径
            String allImagePaths = uploadedPaths.stream()
                    .filter(path -> !path.equals(uploadedPaths.get(uploadedPaths.size() - 1)))
                    .collect(Collectors.joining("|"));
            String signatureImagePath = uploadedPaths.get(uploadedPaths.size() - 1);
            // 保存图片路径到数据库
            userData.setCertificateImage(allImagePaths);
            userData.setSignatureImage(signatureImagePath);
            cardHolderService.updateById(userData);
            // 关闭 ExecutorService
            executorService.shutdown();
        } catch (Exception e) {
            log.error("图片抓取失败: {}", e.getMessage());
        }
    }


    private static String getString(CardHolder userData, String imageUrl) {
        JSONObject entries = new JSONObject(userData.getCertificateImage());
        String targetKey = null;
        if (userData.getSignatureImage().equals(imageUrl)){
            String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
            // 找到文件名中最后一个点的位置,获取后缀名
            int extensionIndex = fileName.lastIndexOf('.');
            // 提取后缀名
            String fileExtension = fileName.substring(extensionIndex + 1);
            return "signature."+ fileExtension;
        }
        for (String key : entries.keySet()) {
            if (imageUrl.equals(entries.getStr(key))) {
                targetKey = key;
                break;
            }
        }
        return targetKey+".jpg";
    }

    /**
     * 上传图片
     * @param file 图片文件
     * @return url地址
     */
    public JsonResult<Object> uploadImage(MultipartFile file) {
        try {
            // 获取文件名
            String originalFilename = file.getOriginalFilename();
            assert originalFilename != null;
            // 找到文件名中最后一个点的位置,获取后缀名
            int dotIndex = originalFilename.lastIndexOf(".");
            // 提取后缀名
            String fileExtension = originalFilename.substring(dotIndex);
            String fileNameWithoutExtension = originalFilename.substring(0, dotIndex);
            // 文件名+当前时间戳+后缀名
            String newFileName = fileNameWithoutExtension + "_" + System.currentTimeMillis() + fileExtension;
            Path uploadPath = Paths.get(path,"2b", newFileName);
            // 创建文件目录
            Files.createDirectories(uploadPath.getParent());
            // 返回图片url
            String url = "/2b/"+ newFileName;
            // 写入数据
            Files.write(uploadPath, file.getBytes());
            return JsonResult.ok(url);
        } catch (IOException e) {
            throw new ApiException(ResultCode.PARAM_ERR);
        }

    }

    /**
     * 删除图片
     * @param imageUrl 图片地址
     * @return true
     * @throws IOException ；
     */
    public JsonResult<Object> deleteImage(String imageUrl) throws IOException {
        Path imagePath = Paths.get(path,imageUrl);
        // 检查文件是否存在
        if (Files.exists(imagePath) && Files.isRegularFile(imagePath)) {
            // 删除文件
            Files.delete(imagePath);
            return JsonResult.ok();
        } else {
            throw new ApiException(ResultCode.NOT_FIND);
        }
    }
}
