package com.qf.zpay.service;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Slf4j
public class FtpClient {

    private ChannelSftp sftpChannel;
    private Session session;

    /**
     * 实例化并连接到 SFTP 服务器
     *
     * @param host     SFTP 服务器地址
     * @param port     SFTP 服务器端口
     * @param user     SFTP 登录账户
     * @param password SFTP 登录密码
     * @throws Exception
     */
    public FtpClient(String host, int port, String user, String password) throws Exception {
        JSch jsch = new JSch();
        session = jsch.getSession(user, host, port);
        session.setPassword(password);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        sftpChannel = (ChannelSftp) session.openChannel("sftp");
        sftpChannel.connect();
    }

    /**
     * SFTP 上传文件
     *
     * @param remoteUploadDirectory   要上传的目录（SFTP 服务器上的目录）
     * @param localUploadFilePathName 本地上传文件的完整路径（本地路径）
     * @return 返回上传结果和信息
     */
    public HashMap<String, String> uploadFile(String remoteUploadDirectory, String localUploadFilePathName) throws Exception {
        FileInputStream fis = null;
        try {
            sftpChannel.cd(remoteUploadDirectory);
            fis = new FileInputStream(new File(localUploadFilePathName));
            sftpChannel.put(fis, new File(localUploadFilePathName).getName());
            log.info("上传成功: " + localUploadFilePathName);
            HashMap<String, String> booleanStringHashMap = new HashMap<>();
            booleanStringHashMap.put("true","上传成功");
            return booleanStringHashMap;
        } catch (Exception e) {
            log.error("SFTP 上传文件异常！", e);
            throw new Exception("上传文件异常: " + e.getMessage());
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("关闭流发生异常!", e);
            }
        }
    }

    /**
     * 下载
     * @param remoteDirectory FTP 文件夹
     * @return 文件目录
     * @throws Exception 错误
     */
    public Map<String, List<String>> checkLogFileNames(String remoteDirectory) throws Exception {
        Map<String, List<String>> result = new HashMap<>();
        result.put("fail", new ArrayList<>());
        result.put("success", new ArrayList<>());
        if (StringUtils.isBlank(remoteDirectory)) {
            return result;
        }
        sftpChannel.cd(remoteDirectory);
        Vector<ChannelSftp.LsEntry> files = sftpChannel.ls(".");
        for (ChannelSftp.LsEntry file : files) {
            if (!file.getAttrs().isDir() && file.getFilename().endsWith(".log")) {
                String fileName = file.getFilename();
                String content = readFileContent(remoteDirectory, fileName);
                if (content.contains("提交实名异常") && content.contains("实名失败")) {
                    result.get("fail").add(fileName);
                } else if (content.contains("提交实名成功")&& content.contains("实名请求成功")) {
                    result.get("success").add(fileName);
                }
            }
        }
        return result;
    }

    private String readFileContent(String remoteDirectory, String fileName) throws SftpException, IOException {
        StringBuilder content = new StringBuilder();
        InputStream inputStream = sftpChannel.get(remoteDirectory + "/" + fileName);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }


    /**
     * 将指定本地文件夹内的所有文件压缩成 .zip 文件后上传到 SFTP 服务器
     * @param remoteUploadDirectory 要上传的目录（SFTP 服务器上的目录）
     * @param localDirectoryPath    本地文件夹的完整路径
     * @return Pair<Boolean, String>   上传结果和信息
     */
    public HashMap<String, String> uploadDirectoryToZip(String remoteUploadDirectory, String localDirectoryPath) throws Exception {
        String zipFileName1 = generateZipFileName();
        String zipFileName = zipFileName1+".zip";
        String zipDirectoryPath = localDirectoryPath + "_zip/";
        String zipFilePath = zipDirectoryPath + zipFileName;
        // 创建临时 .zip 文件
        zipDirectory(localDirectoryPath, zipFilePath);
        // 上传 .zip 文件到 SFTP 服务器
        HashMap<String, String> booleanStringHashMap = uploadFile(remoteUploadDirectory, zipFilePath);
        booleanStringHashMap.put("file_name",zipFileName1);
        deleteFile(zipFilePath,zipDirectoryPath);
        return booleanStringHashMap;


    }
    public void deleteFile(String zipFilePath,String zipDirectoryPath){
        // 删除临时 .zip 文件
        File zipFile = new File(zipFilePath);
        File zipDirectory = new File(zipDirectoryPath);
        if (zipFile.exists()) {
            zipFile.delete();
        }
        // 如果上一级文件夹为空，则删除
        if (zipDirectory.exists() && zipDirectory.isDirectory()) {
            File[] files = zipDirectory.listFiles();
            if (files != null && files.length == 0) {
                zipDirectory.delete();
            }
        }
    }

    private static final int BUFFER_SIZE = 8192; // 增大缓冲区大小

    /**
     * 将指定目录压缩为ZIP文件，支持多线程并行处理
     * @param directoryPath 需要压缩的目录路径
     * @param zipFilePath   生成的ZIP文件保存路径
     * @throws IOException 如果出现IO错误
     */
    public static void zipDirectory(String directoryPath, String zipFilePath) throws IOException {
        // 创建目标ZIP文件的父目录
        File zipFile = new File(zipFilePath);
        File parentDir = zipFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 创建ZIP输出流并压缩文件
        try (ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(zipFilePath), BUFFER_SIZE))) {
            ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            Files.walk(Paths.get(directoryPath))
                    .filter(path -> !Files.isDirectory(path)) // 过滤掉目录，只处理文件
                    .forEach(path -> {
                        executor.submit(() -> {
                            try {
                                // 相对路径作为ZIP条目名称
                                String entryName = path.toString().substring(directoryPath.length() + 1).replace("\\", "/");
                                synchronized (zos) { // 同步ZIP条目的写入操作
                                    zos.putNextEntry(new ZipEntry(entryName));
                                    // 使用缓冲区提高复制性能
                                    try (BufferedInputStream bis = new BufferedInputStream(Files.newInputStream(path), BUFFER_SIZE)) {
                                        byte[] buffer = new byte[BUFFER_SIZE];
                                        int len;
                                        while ((len = bis.read(buffer)) > 0) {
                                            zos.write(buffer, 0, len);
                                        }
                                    }
                                    zos.closeEntry();
                                }
                            } catch (IOException e) {
                                log.error(e.getMessage());
                            }
                        });
                    });
            executor.shutdown();
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("线程中断", e);
        }
    }


    /**
     * 关闭 SFTP 连接
     */
    public void close() {
        try {
            if (sftpChannel != null && sftpChannel.isConnected()) {
                sftpChannel.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        } catch (Exception e) {
            log.error("关闭 SFTP 连接异常", e);
        }
    }

    private static final String COUNTER_FILE_PATH = "./storage/zip_counter.txt";

    public String generateZipFileName() throws IOException {
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = today.format(formatter);

        // 读取计数器文件，如果文件不存在则创建一个新的
        int counter = 1;
        Path counterPath = Paths.get(COUNTER_FILE_PATH);

        if (!Files.exists(counterPath)) {
            Files.createDirectories(counterPath.getParent()); // 确保目录存在
            Files.createFile(counterPath); // 创建计数器文件
        }

        List<String> lines = Files.readAllLines(counterPath);

        if (!lines.isEmpty()) {
            String[] parts = lines.get(0).split(",");
            if (parts.length == 2) {
                String lastDate = parts[0];
                try {
                    counter = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    counter = 1; // 如果计数器文件的内容无效，则重置计数器
                }

                // 如果日期与当前日期相同，则递增计数器
                if (lastDate.equals(dateString)) {
                    counter++;
                } else {
                    counter = 1; // 日期不同，重置计数器
                }
            }
        }

        // 更新计数器文件
        Files.write(counterPath, (dateString + "," + counter).getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        return dateString + "_B4240704USG1CP_" + counter;
    }
}
