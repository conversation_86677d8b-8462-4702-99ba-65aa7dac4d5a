package com.qf.zpay.service;

import cn.hutool.core.convert.NumberWithFormat;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.JWTValidator;
import com.qf.zpay.dto.common.BaseTokenDto;
import com.qf.zpay.dto.common.TokenDto;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import generator.domain.User;
import generator.mapper.UserMapper;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/6/3 21:57
 */
@Service
public class TokenService {

    @Autowired
    private RedissonClient redis;

    @Autowired
    private UserMapper userMapper;

    private final String JWT_KEY = "jwt:";
    private final String JWT_REF = "ref:";
    private final String JWT_SECRET = "W9:KqMwDdTH=A0FpPKgn";

    private final String AUTH_KEY = "auth:";
    private final String AUTH_REF = "aref:";
    private final String AUTH_SECRET = "WvP1jhsQMJ@Yj,tPz!Bx";

    /**
     * @param user 用户信息
     * @return TokenDto
     */
    public TokenDto genJwtToken(User user) {
        TokenDto tokenDto = new TokenDto();

        BaseTokenDto accessTokenDto = new BaseTokenDto();
        accessTokenDto.setExpire(System.currentTimeMillis() + 1000 * 60 * 60 * 2);
        final String accessToken = JWT.create()
                .setPayload("uid", user.getId())
                .setPayload("bid", user.getBusinessId())
                .setKey(JWT_SECRET.getBytes())
                .setIssuedAt(new Date())
                .setNotBefore(new Date())
                .setExpiresAt(new Date(accessTokenDto.getExpire()))
                .sign();
        accessTokenDto.setValue(accessToken);

        BaseTokenDto refreshTokenDto = new BaseTokenDto();
        String refreshToken = DigestUtil.md5Hex(user.getId() + JWT_SECRET + System.currentTimeMillis());
        refreshTokenDto.setValue(refreshToken);
        refreshTokenDto.setExpire(System.currentTimeMillis() + 1000 * 60 * 60 * 24);
//        redis.getBucket(JWT_KEY + user.getId()).set(accessToken, Duration.ofHours(2));

        RMap<String, Integer> refreshCache = redis.getMap(JWT_REF + refreshToken);
        refreshCache.put("uid", user.getId());
        refreshCache.put("bid", user.getBusinessId());
        refreshCache.expire(Duration.ofHours(24));

        tokenDto.setAccessToken(accessTokenDto);
        tokenDto.setRefreshToken(refreshTokenDto);

        return tokenDto;
    }


    public User validationJwtToken(String token) {
        try {
            final JWT jwt = JWTUtil.parseToken(token);

            jwt.setKey(JWT_SECRET.getBytes());
            if (!jwt.verify()) {
                Assert.unAuth();
            }
            JWTValidator.of(token).validateDate();
            NumberWithFormat uid = (NumberWithFormat) jwt.getPayload("uid");
            return userMapper.getOneById(uid.intValue());
        } catch (Exception e) {
            Assert.unAuth();
        }
        return null;
    }


    /**
     * 生成开放 API_TOKEN
     *
     * @return token
     */
    public TokenDto genAuthToken(User user) {
        BaseTokenDto accessTokenDto = new BaseTokenDto();
        accessTokenDto.setExpire(System.currentTimeMillis() + 1000 * 60 * 60 * 2);
        String accessToken = DigestUtil.md5Hex(user.getBusinessId() + AUTH_SECRET + System.currentTimeMillis());
        accessTokenDto.setValue(accessToken);

        RMap<String, Integer> tokenCache = redis.getMap(AUTH_KEY + accessToken);
        tokenCache.put("uid", user.getId());
        tokenCache.put("bid", user.getBusinessId());
        tokenCache.expire(Duration.ofHours(2));


        BaseTokenDto refreshTokenDto = new BaseTokenDto();
        String refreshToken = DigestUtil.md5Hex(user.getId() + AUTH_SECRET + System.currentTimeMillis());
        refreshTokenDto.setValue(refreshToken);
        refreshTokenDto.setExpire(System.currentTimeMillis() + 1000 * 60 * 60 * 4);

        RMap<String, Integer> refreshCache = redis.getMap(AUTH_REF + refreshToken);
        refreshCache.put("uid", user.getId());
        refreshCache.put("bid", user.getBusinessId());
        refreshCache.expire(Duration.ofHours(4));

        TokenDto tokenDto = new TokenDto();
        tokenDto.setAccessToken(accessTokenDto);
        tokenDto.setRefreshToken(refreshTokenDto);

        return tokenDto;
    }


    public User validationAuthToken(String authToken) {
        RMap<String, Integer> tokenCache = redis.getMap(AUTH_KEY + authToken);
        if (tokenCache.isEmpty()) {
            Assert.unAuth();
        }
        return userMapper.getOneById(tokenCache.get("uid"));
    }


    public TokenDto refreshAuthToken(String refreshToken) {
        RMap<String, Integer> refreshCache = redis.getMap(AUTH_REF + refreshToken);
        if (refreshCache.isEmpty()) {
            Assert.fail(ResultCode.REFRESH_EXPIRE);
        }
        User user = userMapper.getOneById(refreshCache.get("uid"));
        refreshCache.delete();
        return genAuthToken(user);
    }

    public TokenDto refreshJwtToken(String refreshToken) {
        RMap<String, Integer> refreshCache = redis.getMap(JWT_REF + refreshToken);
        if (refreshCache.isEmpty()) {
            Assert.fail(ResultCode.REFRESH_EXPIRE);
        }
        User user = userMapper.getOneById(refreshCache.get("uid"));
        refreshCache.delete();
        return genJwtToken(user);
    }


}
