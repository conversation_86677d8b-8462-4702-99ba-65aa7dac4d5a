package com.qf.zpay.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Strings;
import com.qf.zpay.constants.*;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.req.v2.PayeeDto;
import com.qf.zpay.dto.req.v2.PaymentDto;
import com.qf.zpay.dto.res.v2.PaymentVo;
import com.qf.zpay.dto.res.v2.QuoteDto;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.payment.GSalary;
import com.qf.zpay.util.DateUtil;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import generator.domain.*;
import generator.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/7/1 10:54
 * 此类用于处理 代收、付业务
 */
@Service
@Slf4j
public class PaymentService {

    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Autowired
    private GSalary gSalary;
    @Autowired
    private PaymentPayeeService paymentPayeeService;
    @Autowired
    private PaymentPayeeAccountService paymentPayeeAccountService;
    @Autowired
    private PaymentPayConfigService paymentPayConfigService;
    @Autowired
    private PaymentChannelService paymentChannelService;
    @Autowired
    UserWalletService userWalletService;
    @Autowired
    UserPaymentConfigService userPaymentConfigService;
    @Autowired
    PaymentUserConfigService paymentUserConfigService;
    @Autowired
    PaymentPayOrderService paymentPayOrderService;
    @Autowired
    UserWalletLogService userWalletLogService;
    @Autowired
    CountryCodeService countryCodeService;
    @Value("#{'${GSalary.payers.account}'.split(',')}")
    private List<String> payersAccount;

    @PostConstruct
    public void initPayersAccount() {
        try {
            RScoredSortedSet<String> payersAccountSet = redisson.getScoredSortedSet("payersAccount");
            if (payersAccountSet.size() <= 0) {
                for (String s : payersAccount) {
                    payersAccountSet.add(0, s);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getPayersAccount() {
        RScoredSortedSet<String> set = redisson.getScoredSortedSet("payersAccount");
        ScoredEntry<String> firstEntry = set.firstEntry();
        set.addScore(firstEntry.getValue(), 1);
        return firstEntry.getValue();
    }


    /**
     * 查询地区二字码
     *
     * @return list
     */
    public List<PaymentPayConfig> getCountryRegion(String country) {
        return paymentPayConfigService.availablePaymentMethods(country);

    }

    /**
     * 添加收款人
     */
    public void addPayee(PaymentPayeeDto payeeRequest) {
        PaymentPayee paymentPayee = new PaymentPayee();
        BeanUtils.copyProperties(payeeRequest, paymentPayee);
        if (paymentPayee.getSubjectType().equals(PayeeEnum.ENTERPRISE.getCode()) && paymentPayee.getAccountHolder() == null) {
            throw new ApiException(ResultCode.PAYMENT_PAY_ENTERPRISE);
        }
        paymentPayee.setCreateTime(DateUtil.getUTCDate());
        Map<String, Object> requestBody = payee(paymentPayee);
        requestBody.put("subject_type", payeeRequest.getSubjectType());
        String s = gSalary.savePayee(requestBody);
        paymentPayee.setPayeeId(s);
        paymentPayeeService.save(paymentPayee);
    }

    private Map<String, Object> payee(PaymentPayee payeeRequest) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("country", payeeRequest.getTwoLetterCode());
        requestBody.put("currency", payeeRequest.getCurrency());
        requestBody.put("first_name", payeeRequest.getFirstName());
        requestBody.put("last_name", payeeRequest.getLastName());
        requestBody.put("account_holder", payeeRequest.getAccountHolder());
        requestBody.put("account_type", payeeRequest.getAccountType());

        Map<String, Object> mobile = new HashMap<>();
        mobile.put("nation_code", payeeRequest.getNationCode());
        mobile.put("mobile", payeeRequest.getPhone());

        requestBody.put("mobile", mobile);
        requestBody.put("address", payeeRequest.getAddress());
        return requestBody;
    }

    private Map<String, Object> payeeBank(PaymentPayee payeeRequest) {
        Map<String, Object> requestBody = new HashMap<>();
        // 邮编Singapore
        requestBody.put("postcode", payeeRequest.getPostcode());
        requestBody.put("city", payeeRequest.getCity());
        requestBody.put("subject_type", payeeRequest.getSubjectType());

        requestBody.put("country", payeeRequest.getTwoLetterCode());
        requestBody.put("first_name", payeeRequest.getFirstName());
        requestBody.put("last_name", payeeRequest.getLastName());
        requestBody.put("account_holder", payeeRequest.getAccountHolder());
        requestBody.put("account_type", payeeRequest.getAccountType());

        Map<String, Object> mobile = new HashMap<>();
        mobile.put("nation_code", payeeRequest.getNationCode());
        mobile.put("mobile", payeeRequest.getPhone());

        requestBody.put("mobile", mobile);
        requestBody.put("address", payeeRequest.getAddress());
        return requestBody;
    }

    /**
     * 更新收款人
     *
     * @param paymentPayee ；
     */
    public void updatePayee(PaymentPayee paymentPayee) {
        paymentPayeeService.updateById(paymentPayee);
        Map<String, Object> payee = payee(paymentPayee);
        gSalary.renewalPayee(payee, paymentPayee.getPayeeId());
    }

    /**
     * 添加收款人账户
     *
     * @param dto    ；
     * @param user2；
     */
    public PaymentPayeeAccount saveAccount(PaymentPayeeAccount dto, User user2) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        if (!Strings.isNullOrEmpty(dto.getPaymentMethod())) {
            stringObjectHashMap.put("payment_method", dto.getPaymentMethod());
        }

        if (!Strings.isNullOrEmpty(dto.getAccountNo())) {
            stringObjectHashMap.put("account_no", dto.getAccountNo());
        }
        if (!Objects.isNull(dto.getHaveAccount())) {
            stringObjectHashMap.put("have_account", dto.getHaveAccount() == 1);
        }
        if (!Strings.isNullOrEmpty(dto.getFirstName())) {
            stringObjectHashMap.put("first_name", dto.getFirstName());
        }
        if (!Strings.isNullOrEmpty(dto.getMiddleName())) {
            stringObjectHashMap.put("middle_name", dto.getMiddleName());
        }
        if (!Strings.isNullOrEmpty(dto.getLastName())) {
            stringObjectHashMap.put("last_name", dto.getLastName());
        }
        if (!Strings.isNullOrEmpty(dto.getWalletAccountType())) {
            stringObjectHashMap.put("wallet_account_type", dto.getWalletAccountType());
        }
        if (!Strings.isNullOrEmpty(dto.getDocumentId())) {
            if (dto.getDocumentId().length() < 11 || dto.getDocumentId().length() > 14) {
                throw new ApiException(ResultCode.DOCUMENT_ERROR);
            }
            stringObjectHashMap.put("document_id", dto.getDocumentId());
        }
        if (!Strings.isNullOrEmpty(dto.getPhone())) {
            stringObjectHashMap.put("phone", dto.getPhone());
        }
        if (!Strings.isNullOrEmpty(dto.getAddress())) {
            stringObjectHashMap.put("address", dto.getAddress());
        }
        dto.setBusinessId(user2.getBusinessId());
        dto.setCreateTime(DateUtil.getUTCDate());
        JSONObject entries = gSalary.savePayeeAccount(stringObjectHashMap, dto.getPayeeId());
        dto.setStatus(entries.getStr("status"));
        dto.setAccountId(entries.getStr("account_id"));
        paymentPayeeAccountService.save(dto);
        return dto;
    }

    public JSONObject quotes(PayeeRequestDto dto, PaymentPayeeAccount byId) {
        JSONObject requestBody = getEntries(byId);
        requestBody.set("amount", dto.getAmount());
        requestBody.set("remark", dto.getRemark());
        if (null != (dto.getClearingNetwork())) {
            requestBody.set("clearing_network", dto.getClearingNetwork());
        }
        return gSalary.quotes(requestBody);
    }

    public JSONObject paymentOrder(String quoteId, String orderId) {
        JSONObject requestBody = new JSONObject();
        requestBody.set("quote_id", quoteId);
        requestBody.set("client_order_id", orderId);
        return gSalary.paymentOrder(requestBody);
    }

    /**
     * 锁汇
     */
    public PaymentPayOrder quote(PayeeRequestDto dto, User user) {
        if (dto.getAccountId() == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        // 获取收款人账号
        PaymentPayeeAccount payeeAccount = paymentPayeeAccountService.getByAccountId(dto.getAccountId());
        // 获取各渠道手续费配置
        PaymentChannel paymentChannel = paymentChannelService.getOne(new LambdaQueryWrapper<PaymentChannel>().eq(PaymentChannel::getCode, dto.getPaymentMethod())
                .eq(PaymentChannel::getState, 1));
        if (paymentChannel == null) {
            throw new ApiException(ResultCode.PAYMENT_USER_CONFIG_NOT_EXIST);
        }
        PaymentUserConfig paymentUserConfig = paymentUserConfigService.getOne(new LambdaQueryWrapper<PaymentUserConfig>().eq(PaymentUserConfig::getChannelId, paymentChannel.getId())
                .eq(PaymentUserConfig::getBusinessId, user.getBusinessId()).eq(PaymentUserConfig::getStatus, 1));
        if (paymentUserConfig == null) {
            throw new ApiException(ResultCode.PAYMENT_USER_CONFIG_NOT_EXIST);
        }
        // 申请锁汇
        JSONObject jsonObject = quotes(dto, payeeAccount);
        // 上游需支付金额（含手续费）
        JSONObject payAmount = jsonObject.getJSONObject("pay_amount");
        BigDecimal usdAmount = payAmount.getBigDecimal("amount").subtract(jsonObject.getJSONObject("surcharge").getBigDecimal("amount"));
        // 系统手续费USD（平台/商户）
        BigDecimal platformFee = calculatePlatformFee(paymentUserConfig, usdAmount);
        BigDecimal businessFee = calculateBusinessFee(paymentUserConfig, usdAmount);
        // 总扣款USD(上游汇率计算的U+商户手续费)
        BigDecimal totalCharge = usdAmount.add(businessFee);
        // 判断钱包余额
        // validateWalletBalance(payeeAccount.getBusinessId(), totalCharge);
        // 构建订单
        PaymentPayOrder paymentPayOrder = createPaymentPayOrderV2(paymentUserConfig, dto.getRemark(), platformFee, businessFee, totalCharge);
        paymentPayOrder.setPaymentAccount(payeeAccount.getAccountNo());
        PaymentPayee paymentPayee = paymentPayeeService.selectByPayeeId(payeeAccount.getPayeeId());
        // 更新订单上游返回数据
        updatePaymentPayOrderFromJson(paymentPayOrder, jsonObject, paymentPayee, user);
        paymentPayOrderService.save(paymentPayOrder);
        return paymentPayOrder;
    }

    @Autowired
    RedissonClient redis;

    @Transactional
    public PaymentPayOrder paymentOrder(String orderId, Integer id) {
        RLock lock = redis.getLock(String.format("bp_lock:%s", orderId));
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            if (!isLocked) {
                throw new ApiException(ResultCode.ORDER_IS_PROCESSING);
            }
            PaymentPayOrder paymentPayOrder = paymentPayOrderService.getByOrderNo(orderId);
            if (paymentPayOrder == null) {
                throw new ApiException(ResultCode.ORDER_NOT_NOT_EXIST);
            }
            // 判断是否超时（4分钟）
            Date createTime = paymentPayOrder.getCreateTime();
            // 将Date转换为Instant
            Instant createInstant = createTime.toInstant();
            // 获取当前时间的Instant
            Instant nowInstant = Instant.now();
            // 计算两个Instant之间的Duration
            long minutesBetween = Duration.between(createInstant, nowInstant).toMinutes();
            if (minutesBetween > 4) {
                throw new ApiException(ResultCode.ORDER_IS_TIME_OUT);
            }
            if (!"CREATED".equals(paymentPayOrder.getStatus())) {
                throw new ApiException(ResultCode.ORDER_IS_PROCESSING);
            }
            UserWallet userWallet = userWalletService.getByBusinessId(paymentPayOrder.getBusinessId());
            if (validateChange(userWallet.getPaymentWallet(), paymentPayOrder.getPaymentAmount())) {
                throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
            }
            // 执行扣款
            BigDecimal receivedAmount = paymentPayOrder.getCostTotalMoney().subtract(paymentPayOrder.getCostFee()).negate();
            BigDecimal feeRate = paymentPayOrder.getBusinessFeeTotal().negate();
            createUserWalletLog(id, paymentPayOrder, userWallet, WalletTypeEnum.COMMISSION.getCode(), receivedAmount, "代付金额");
            createUserWalletLog(id, paymentPayOrder, userWallet, WalletTypeEnum.COMMISSION_FEE.getCode(), feeRate, "代付手续费");
            // 更新扣款状态
            paymentPayOrder.setStatus(PayOrderStatusEnum.PAID.getCode());
            paymentPayOrder.setPaymentTime(new Date());
            paymentPayOrderService.updateById(paymentPayOrder);
            // 请求上游支付
            JSONObject entries = paymentOrder(paymentPayOrder.getQuoteId(), paymentPayOrder.getOrderId());
            updatePaymentPayOrder(paymentPayOrder, entries);
            paymentPayOrderService.updateById(paymentPayOrder);
            return paymentPayOrder;
        } catch (Exception e) {
            log.error("支付处理失败: orderId: {}    {}", orderId, e);
            throw new ApiException(ResultCode.ORDER_IS_ERROR);
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public PaymentPayOrder paymentOrderV2(String orderNo, Integer id) {
        PaymentPayOrder paymentPayOrder = paymentPayOrderService.getByOrderNo(orderNo);
        if (paymentPayOrder == null) {
            throw new ApiException(ResultCode.ORDER_NOT_NOT_EXIST);
        }
        UserWallet userWallet = userWalletService.getByBusinessId(paymentPayOrder.getBusinessId());
        if (validateChange(userWallet.getPaymentWallet(), paymentPayOrder.getPaymentAmount())) {
            throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
        }
//        JSONObject entries = paymentOrder(paymentPayOrder.getQuoteId(), paymentPayOrder.getOrderId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = sdf.format(new Date());
        JSONObject entries = new JSONObject();
        entries.set("create_time", System.currentTimeMillis());
        entries.set("status", "COMPLETED");
        entries.set("error_message", "");
        entries.set("order_id", "Z-PO" + format + "8553537");
        updatePaymentPayOrder(paymentPayOrder, entries);
        paymentPayOrderService.updateById(paymentPayOrder);
        String status = "COMPLETED";
        if (!status.equals(PayOrderStatusEnum.CANCELLED.getCode()) && !status.equals(PayOrderStatusEnum.FAILED.getCode()) && !status.equals(PayOrderStatusEnum.REJECTED.getCode())) {
            BigDecimal receivedAmount = paymentPayOrder.getCostTotalMoney().subtract(paymentPayOrder.getCostFee()).negate();
            BigDecimal feeRate = paymentPayOrder.getBusinessFeeTotal().negate();
            createUserWalletLog(id, paymentPayOrder, userWallet, WalletTypeEnum.COMMISSION.getCode(), receivedAmount, "代付金额");
            createUserWalletLog(id, paymentPayOrder, userWallet, WalletTypeEnum.COMMISSION_FEE.getCode(), feeRate, "代付手续费");
        }
        return paymentPayOrder;
    }

    private UserPaymentConfig getUserPaymentConfig(Integer businessId) {
        UserPaymentConfig userPaymentConfig = userPaymentConfigService.selectOne(businessId);
        if (userPaymentConfig.getFeeRate() != null && userPaymentConfig.getFeeRate().compareTo(BigDecimal.ZERO) > 0 && userPaymentConfig.getTotalFee() != null && userPaymentConfig.getTotalFee().compareTo(BigDecimal.ZERO) > 0) {
            throw new ApiException(ResultCode.PARAM_ERR);
        }
        return userPaymentConfig;
    }


    private JSONObject getEntries(PaymentPayeeAccount payeeAccount) {
        PaymentPayee paymentPayee = paymentPayeeService.getOne(new LambdaQueryWrapper<PaymentPayee>().eq(PaymentPayee::getPayeeId, payeeAccount.getPayeeId()));
        JSONObject requestBody = new JSONObject();
        requestBody.set("payee_account_id", payeeAccount.getAccountId());
        requestBody.set("payer_id", getPayersAccount());
        // 当收款人为个人类型，收款方式为银行账户，且收款国家为中国时，付款用途只允许SALARY
        requestBody.set("purpose", PaymentCategoryEnum.SOFTWARE_DEVELOPMENT_SERVICE.getCode());
        if ("INDIVIDUAL".equals(paymentPayee.getSubjectType()) &&
                "CN".equals(paymentPayee.getCountry()) &&
                "BANK_TRANSFER".equals(payeeAccount.getPaymentMethod())) {
            requestBody.set("purpose", PaymentCategoryEnum.SALARY.getCode());
        }
        requestBody.set("pay_currency", "USD");
        requestBody.set("amount_type", PaymentAmountTypeEnum.RECEIVE_AMOUNT);
        return requestBody;
    }

    private BigDecimal calculateFee(UserPaymentConfig config, BigDecimal usdAmount) {
        if (config == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        BigDecimal fee = new BigDecimal("0.00");
        if (config.getFeeRate() != null && config.getFeeRate().compareTo(BigDecimal.ZERO) > 0) {
            fee = calculateFee(usdAmount, config.getFeeRate());
        }
        if (config.getTotalFee() != null && config.getTotalFee().compareTo(BigDecimal.ZERO) > 0) {
            fee = config.getTotalFee();
        }
        return fee;
    }

    private BigDecimal calculateBusinessFee(PaymentUserConfig config, BigDecimal usdAmount) {
        if (config == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        BigDecimal fee = new BigDecimal("0.00");
        if (config.getBusinessFeeRate() != null && config.getBusinessFeeRate().compareTo(BigDecimal.ZERO) > 0) {
            fee = calculateFee(usdAmount, config.getBusinessFeeRate());
        }
        if (config.getBusinessFee() != null && config.getBusinessFee().compareTo(BigDecimal.ZERO) > 0) {
            fee = fee.add(config.getBusinessFee());
        }
        return fee;
    }

    private BigDecimal calculatePlatformFee(PaymentUserConfig config, BigDecimal usdAmount) {
        if (config == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        BigDecimal fee = new BigDecimal("0.00");
        if (config.getPlatformFeeRate() != null && config.getPlatformFeeRate().compareTo(BigDecimal.ZERO) > 0) {
            fee = calculateFee(usdAmount, config.getPlatformFeeRate());
        }
        if (config.getPlatformFee() != null && config.getPlatformFee().compareTo(BigDecimal.ZERO) > 0) {
            fee = fee.add(config.getPlatformFee());
        }
        return fee;
    }

    private void validateWalletBalance(Integer businessId, BigDecimal totalDeduction) throws ApiException {
        UserWallet userWallet = userWalletService.getByBusinessId(businessId);
        if (validateChange(userWallet.getPaymentWallet(), totalDeduction)) {
            throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
        }
    }

    public boolean validateChange(BigDecimal changeBefore, BigDecimal amount) {
        int compareResult = changeBefore.compareTo(amount);
        return compareResult < 0;
    }

//    private PaymentPayOrder createPaymentPayOrder(UserPaymentConfig config, String remark, BigDecimal fee, BigDecimal add) {
//        PaymentPayOrder paymentPayOrder = new PaymentPayOrder();
//        paymentPayOrder.setPaymentAmount(add);
//        paymentPayOrder.setRemark(remark);
//        paymentPayOrder.setFee(fee);
//        paymentPayOrder.setPaymentRate(config.getFeeRate() == null ? BigDecimal.ZERO : config.getFeeRate());
//        paymentPayOrder.setTotalFee(config.getTotalFee() == null ? BigDecimal.ZERO : config.getTotalFee());
//        return paymentPayOrder;
//    }


    /**
     * @param config
     * @param remark
     * @param platformFee 平台手续费用
     * @param businessFee 商户手续费用
     * @param add
     * @return
     */
    private PaymentPayOrder createPaymentPayOrderV2(PaymentUserConfig config, String remark, BigDecimal platformFee, BigDecimal businessFee, BigDecimal add) {
        PaymentPayOrder paymentPayOrder = new PaymentPayOrder();
        paymentPayOrder.setPaymentAmount(add);
        paymentPayOrder.setRemark(remark);
        paymentPayOrder.setPlatformFee(config.getPlatformFee() == null ? BigDecimal.ZERO : config.getPlatformFee());
        paymentPayOrder.setPlatformFeeRate(config.getPlatformFeeRate() == null ? BigDecimal.ZERO : config.getPlatformFeeRate());
        paymentPayOrder.setPlatformFeeTotal(platformFee);
        paymentPayOrder.setBusinessFee(config.getBusinessFee() == null ? BigDecimal.ZERO : config.getBusinessFee());
        paymentPayOrder.setBusinessFeeRate(config.getBusinessFeeRate() == null ? BigDecimal.ZERO : config.getBusinessFeeRate());
        paymentPayOrder.setBusinessFeeTotal(businessFee);
        paymentPayOrder.setStatus("CREATED");
        return paymentPayOrder;
    }

    private void updatePaymentPayOrderFromJson(PaymentPayOrder paymentPayOrder, JSONObject jsonObject, PaymentPayee payee, User user) {
        JSONObject payAmount = jsonObject.getJSONObject("receive_amount");
        JSONObject surcharge = jsonObject.getJSONObject("surcharge");
        JSONObject payAmount2 = jsonObject.getJSONObject("pay_amount");
        BigDecimal amount = payAmount2.getBigDecimal("amount");
        String currency = payAmount.getStr("currency");
        BigDecimal paymentRate = jsonObject.getBigDecimal("exchange_rate");
        paymentPayOrder.setBusinessId(user.getBusinessId());
        paymentPayOrder.setPayeeId(payee.getId());
        paymentPayOrder.setPaymentPurpose(payee.getPaymentPurpose());
        paymentPayOrder.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_PAYMENT_ORDER));
        paymentPayOrder.setPayer(user.getBusinessName());
        paymentPayOrder.setPaymentMethod(jsonObject.getStr("payment_method"));
        paymentPayOrder.setAmountReceived(payAmount.getBigDecimal("amount"));
        paymentPayOrder.setPaymentRate(paymentRate);
        paymentPayOrder.setCurrencyReceived(currency);
        paymentPayOrder.setCostTotalMoney(amount);
        paymentPayOrder.setCostFee(surcharge.getBigDecimal("amount"));
        paymentPayOrder.setCostPaymentRate(jsonObject.getBigDecimal("exchange_rate"));
        paymentPayOrder.setCreateTime(DateUtil.getUTCDate());
        paymentPayOrder.setCompletionTime(DateUtil.getUTCDate(jsonObject.getStr("expire_at")));
        paymentPayOrder.setQuoteId(jsonObject.getStr("quote_id"));
    }

    private void updatePaymentPayOrder(PaymentPayOrder paymentPayOrder, JSONObject jsonObject) {
//        paymentPayOrder.setPaymentTime(DateUtil.getUTCDate(jsonObject.getStr("create_time")));
        paymentPayOrder.setPaymentTime(new Date());
        paymentPayOrder.setStatus(jsonObject.getStr("status"));
        paymentPayOrder.setFailureReason(jsonObject.getStr("error_message"));
        paymentPayOrder.setUpstreamOrderId(jsonObject.getStr("order_id"));
    }


    @Transactional(rollbackFor = Exception.class)
    public void createUserWalletLog(Integer userId, PaymentPayOrder paymentPayOrder, UserWallet userWallet, Integer action, BigDecimal changeMoney, String note) {
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userId);
        userWalletLog.setBusinessId(paymentPayOrder.getBusinessId());
        userWalletLog.setAction(action);
        userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_PAYMENT);
        userWalletLog.setChangeBefore(userWallet.getPaymentWallet());
        userWalletLog.setChangeMoney(changeMoney);
        userWalletLog.setChangeAfter(userWallet.getPaymentWallet().add(changeMoney));
        userWalletLog.setNote(note);
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_PAYMENT));
        userWalletLog.setCreateTime(new Date());
        userWalletLogService.save(userWalletLog);

        userWallet.setPaymentWallet(userWallet.getPaymentWallet().add(changeMoney));
        userWalletService.updateById(userWallet);
    }

    public static BigDecimal calculateFee(BigDecimal money, BigDecimal withdrawRate) {
        // 将百分比转换为小数
        BigDecimal decimalRate = withdrawRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN);
        // 计算手续费
        return money.multiply(decimalRate).setScale(2, RoundingMode.HALF_EVEN);
    }

    public JSONObject getPayment() {
        return gSalary.getPayment();
    }

    public JSONObject modifyOrderStatus(String upstreamOrderId) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("order_id", upstreamOrderId);
        return gSalary.modifyOrderStatus(requestBody);
    }

    public JSONObject currentExchangeRate(String currency) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("buy_currency", currency);
        requestBody.put("sell_currency", "USD");
        return gSalary.currentExchangeRate(requestBody);
    }

    public List<JSONObject> availablePaymentMethods(String country) {
        List<JSONObject> jsonObjects = new ArrayList<>();
        JSONObject entries = gSalary.availablePaymentMethods();
        JSONArray paymentMethods = entries.getJSONArray("payment_methods");
        for (int i = 0; i < paymentMethods.size(); i++) {
            JSONObject jsonObject = paymentMethods.getJSONObject(i);
            if (jsonObject.getStr("code").equalsIgnoreCase("PAYPAL") ||
                    jsonObject.getStr("code").equalsIgnoreCase("GRABPAY") ||
                    jsonObject.getStr("code").equalsIgnoreCase("TOUCH_NGO") ||
                    jsonObject.getStr("code").equalsIgnoreCase("GCASH") ||
                    jsonObject.getStr("code").equalsIgnoreCase("ALIPAY")
            ) {
                if (jsonObject.isNull("country") || jsonObject.getStr("country").equals(country)) {
                    jsonObjects.add(jsonObject);
                }
            }
        }
        return jsonObjects;
    }

    public PayeeDto v2SavePayee(PayeeDto payeeRequest, User user2) {
        if (payeeRequest.getSubjectType().equals(PayeeEnum.ENTERPRISE.getCode()) && payeeRequest.getAccountHolder() == null) {
            throw new ApiException(ResultCode.PAYMENT_PAY_ENTERPRISE);
        }
        QueryWrapper<PaymentPayConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("two_letter_code", payeeRequest.getCountry());
        queryWrapper.eq("status", 1);
        PaymentPayConfig paymentPayConfig = paymentPayConfigService.getOne(queryWrapper);
        PaymentPayee paymentPayee = new PaymentPayee();
        paymentPayee.setBusinessId(user2.getBusinessId());
        paymentPayee.setPayConfigId(paymentPayConfig.getId());
        paymentPayee.setSubjectType(payeeRequest.getSubjectType());
        paymentPayee.setCountry(paymentPayConfig.getCountryRegion());
        paymentPayee.setCurrency(payeeRequest.getCurrency());
        paymentPayee.setFirstName(payeeRequest.getFirstName());
        paymentPayee.setLastName(payeeRequest.getLastName());
        paymentPayee.setAccountHolder(payeeRequest.getAccountHolder());
        paymentPayee.setNationCode(payeeRequest.getNationCode());
        paymentPayee.setPhone(payeeRequest.getPhone());
        paymentPayee.setAddress(payeeRequest.getAddress());
        paymentPayee.setTwoLetterCode(payeeRequest.getCountry());
        paymentPayee.setEnglishNameCountry(paymentPayConfig.getEnglishNameCountry());
        paymentPayee.setCreateTime(new Date());
        paymentPayee.setPaymentPurpose(payeeRequest.getPaymentPurpose());
        paymentPayeeService.save(paymentPayee);

        Map<String, Object> requestBody = payee(paymentPayee);
        requestBody.put("subject_type", payeeRequest.getSubjectType());
        String s = gSalary.savePayee(requestBody);
        paymentPayee.setPayeeId(s);
        paymentPayeeService.updateById(paymentPayee);
        payeeRequest.setPayeeId(s);
        return payeeRequest;
    }

    public PaymentVo payment(String orderId, User user) {
        PaymentPayOrder paymentPayOrder1;
        if ("prod".equals(activeProfile)) {
            paymentPayOrder1 = this.paymentOrder(orderId, user.getId());
        } else {
            paymentPayOrder1 = this.paymentOrderV2(orderId, user.getId());
        }

        PaymentPayee paymentPayee = paymentPayeeService.getById(paymentPayOrder1.getPayeeId());
        PaymentVo paymentDtoVo = new PaymentVo();
        paymentDtoVo.setOrderId(paymentPayOrder1.getOrderId());
        paymentDtoVo.setPaymentAccount(paymentPayOrder1.getPaymentAccount());
        paymentDtoVo.setStatus(paymentPayOrder1.getStatus());
        extracted(paymentDtoVo, paymentPayOrder1, paymentPayee);
        paymentDtoVo.setCreateTime(paymentPayOrder1.getCreateTime());
        paymentDtoVo.setRemark(paymentPayOrder1.getRemark());
        paymentDtoVo.setPaymentTime(paymentPayOrder1.getPaymentTime());
        paymentDtoVo.setPaymentRate(paymentPayOrder1.getCostPaymentRate());
        return paymentDtoVo;
    }

    private static void extracted(PaymentVo paymentDtoVo, PaymentPayOrder paymentPayOrder1, PaymentPayee paymentPayee) {
        paymentDtoVo.setPaymentMethod(paymentPayOrder1.getPaymentMethod());
        HashMap<String, Object> stringObjectHashMap = getStringObjectHashMap(paymentPayee.getCurrency(), paymentPayOrder1.getAmountReceived());
        paymentDtoVo.setAmountReceived(stringObjectHashMap);
        HashMap<String, Object> map = getObjectHashMap(paymentPayOrder1);
        paymentDtoVo.setPaymentAmount(map);
        HashMap<String, Object> map2 = getStringObjectHashMap(paymentPayOrder1);
        paymentDtoVo.setFee(map2);
    }

    public PaymentPayeeSaveDto updatePayeeV2(PaymentPayeeSaveDto dto) {
        if (dto.getPayeeId().isEmpty()) {
            throw new ApiException(ResultCode.PAYMENT_PAY_PAYEE_ID);
        }
        PaymentPayee paymentPayee = paymentPayeeService.updatePayeeV2(dto);
        Map<String, Object> payee = payeeBank(paymentPayee);
        payee.put("currencies", Arrays.asList(paymentPayee.getCurrency().split(",")));
        gSalary.renewalPayee(payee, paymentPayee.getPayeeId());
        return dto;
    }

    public QuoteDto getQuoteDto(PaymentPayOrder paymentPayOrder) {
        PaymentPayee paymentPayee = paymentPayeeService.getById(paymentPayOrder.getPayeeId());
        QuoteDto quoteDto = new QuoteDto();
        quoteDto.setQuoteNo(paymentPayOrder.getOrderId());
        quoteDto.setPaymentMethod(paymentPayOrder.getPaymentMethod());
        HashMap<String, Object> stringObjectHashMap = getStringObjectHashMap(paymentPayee.getCurrency(), paymentPayOrder.getAmountReceived());
        quoteDto.setAmountReceived(stringObjectHashMap);
        HashMap<String, Object> map = getObjectHashMap(paymentPayOrder);
        quoteDto.setPaymentAmount(map);
        HashMap<String, Object> map2 = getStringObjectHashMap(paymentPayOrder);
        quoteDto.setFee(map2);
        quoteDto.setPaymentRate(paymentPayOrder.getCostPaymentRate());
        quoteDto.setCreateTime(paymentPayOrder.getCreateTime());
        quoteDto.setRemark(paymentPayOrder.getRemark());
        quoteDto.setExpireAt(paymentPayOrder.getCompletionTime());
        return quoteDto;
    }

    public PaymentDto queryPayment(String orderId) {
        PaymentPayOrder paymentPayOrder = paymentPayOrderService.getByOrderNo(orderId);
        if (paymentPayOrder == null) {
            throw new ApiException(ResultCode.ORDER_NOT_NOT_EXIST);
        }
        PaymentPayee paymentPayee = paymentPayeeService.getById(paymentPayOrder.getPayeeId());
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setOrderId(paymentPayOrder.getOrderId());
        paymentDto.setStatus(paymentPayOrder.getStatus());
        paymentDto.setFailureReason(paymentPayOrder.getFailureReason());

        HashMap<String, Object> amountReceived = getStringObjectHashMap(paymentPayee.getCurrency(), paymentPayOrder.getAmountReceived());
        paymentDto.setAmountReceived(amountReceived);

        HashMap<String, Object> paymentAmount = getObjectHashMap(paymentPayOrder);
        paymentDto.setPaymentAmount(paymentAmount);

        HashMap<String, Object> fee = getStringObjectHashMap(paymentPayOrder);
        paymentDto.setFee(fee);

        paymentDto.setPaymentRate(paymentPayOrder.getCostPaymentRate());
        paymentDto.setRemark(paymentPayOrder.getRemark());
        paymentDto.setCreateTime(paymentPayOrder.getCreateTime());
        paymentDto.setPaymentTime(paymentPayOrder.getPaymentTime());
        paymentDto.setPaymentAccount(paymentPayOrder.getPaymentAccount());
        paymentDto.setPaymentMethod(paymentPayOrder.getPaymentMethod());
        return paymentDto;
    }

    private static HashMap<String, Object> getStringObjectHashMap(String paymentPayee, BigDecimal paymentPayOrder) {
        HashMap<String, Object> amountReceived = new HashMap<>();
        amountReceived.put("currency", paymentPayee);
        amountReceived.put("amount", paymentPayOrder);
        return amountReceived;
    }

    private static HashMap<String, Object> getObjectHashMap(PaymentPayOrder paymentPayOrder) {
        return getStringObjectHashMap("USD", paymentPayOrder.getPaymentAmount());
    }

    private static HashMap<String, Object> getStringObjectHashMap(PaymentPayOrder paymentPayOrder) {
        return getStringObjectHashMap("USD", paymentPayOrder.getBusinessFeeTotal());
    }

    public void deleteAccount(String accountId) {
        gSalary.deleteAccount(accountId);
        paymentPayeeAccountService.deleteAccount(accountId);
    }

    @Autowired
    RedissonClient redisson;

    public JSONArray availablePaymentMethods2() {
        RBucket<JSONArray> cache = redisson.getBucket("payment_methods");
        if (!cache.isExists()) {
            JSONObject entries = gSalary.availablePaymentMethods();
            JSONArray objects = entries.getJSONArray("payment_methods");
            HashSet<String> priorityCodes = new HashSet<>();
            priorityCodes.add("ALIPAY");
//            priorityCodes.add("PAYONEER");
            priorityCodes.add("TOUCH_NGO");
            priorityCodes.add("PAYPAL");
//            priorityCodes.add("GRABPAY");
            List<JSONObject> jsonList = new ArrayList<>();
            for (int i = 0; i < objects.size(); i++) {
                jsonList.add(objects.getJSONObject(i));
            }
            jsonList.sort(Comparator.comparingInt(o ->
                    priorityCodes.contains(o.getStr("code")) ? 0 : 1
            ));
            JSONArray paymentMethods = new JSONArray(jsonList);
            cache.set(paymentMethods);
        }
        return cache.get();
    }


    public List<PaymentChannel> availablePaymentMethods3(Integer bid) {
        List<PaymentUserConfig> paymentUserConfigs = paymentUserConfigService.list(new LambdaQueryWrapper<PaymentUserConfig>().eq(PaymentUserConfig::getBusinessId, bid).eq(PaymentUserConfig::getStatus, 1));
        List<Integer> channelIds = paymentUserConfigs.stream().map(p -> p.getChannelId()).collect(Collectors.toList());
        if (channelIds.isEmpty()) {
            return new ArrayList<>();
        }
        return paymentChannelService.listByIds(channelIds);
    }

    public QuoteDto applyQuote(ApplyQuoteDto dto, User user) {
        PaymentPayeeAccount payeeAccount = dto.getAccount();
        if (payeeAccount == null) {
            throw new ApiException(ResultCode.PARAM_MISS);
        }
        // 银行账户
        if (payeeAccount.getPaymentMethod().equals("BANK_TRANSFER")) {
            AccountRegistryDto accountRegistryDto = dto.getAccountRegistryDto();
            accountRegistryDto.setBusinessId(user.getBusinessId());
            PaymentPayeeAccount payeeAccount1 = this.saveAccountRegistry(accountRegistryDto);
//            JSONObject clearingNetworks = getClearingNetworks(payeeAccount1.getAccountId(), dto.getAmount());
            if (StringUtils.isEmpty(payeeAccount1.getStatus()) || "PENDING".equals(payeeAccount1.getStatus())) {
                try {
                    // 收款人账户当还是PENDING状态 休眠5秒
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            return getQuoteDto(dto, user, payeeAccount1, null);
        }
        // 电子钱包
        if (payeeAccount.getPaymentMethod().equals("ALIPAY")) {
            if (ObjectUtil.isNull(payeeAccount.getFirstName()) || ObjectUtil.isNull(payeeAccount.getLastName())) {
                throw new ApiException(ResultCode.PARAM_MISS);
            }
            PaymentPayee paymentPayee = paymentPayeeService.getPayeeByFirstNameAndLastName(payeeAccount.getFirstName(), payeeAccount.getLastName());
            if (paymentPayee == null) {
                paymentPayee = paymentPayeeService.getById(289);
                paymentPayee.setId(null);
                paymentPayee.setBusinessId(user.getBusinessId());
                paymentPayee.setFirstName(payeeAccount.getFirstName());
                paymentPayee.setLastName(payeeAccount.getLastName());
                paymentPayee.setCreateTime(new Date());
                Map<String, Object> requestBody = payee(paymentPayee);
                requestBody.put("subject_type", paymentPayee.getSubjectType());
                String s = gSalary.savePayee(requestBody);
                paymentPayee.setPayeeId(s);
                paymentPayeeService.save(paymentPayee);
            }
            payeeAccount.setPayeeId(paymentPayee.getPayeeId());
            payeeAccount.setLastName(null);
            payeeAccount.setFirstName(null);
        } else {
            PaymentPayee byAccountType = paymentPayeeService.getByAccountType(payeeAccount.getPaymentMethod(), user, payeeAccount.getAccountNo());
            if (byAccountType.getPayeeId().isEmpty() || byAccountType.getId() == null) {
                Map<String, Object> requestBody = payee(byAccountType);
                requestBody.put("subject_type", byAccountType.getSubjectType());
                String s = gSalary.savePayee(requestBody);
                byAccountType.setPayeeId(s);
                paymentPayeeService.save(byAccountType);
            }
            payeeAccount.setPayeeId(byAccountType.getPayeeId());
        }
        // 新增收款人收款账户（电子钱包）
        PaymentPayeeAccount payeeAccount1 = this.saveAccount(payeeAccount, user);
        return getQuoteDto(dto, user, payeeAccount1, null);
    }

    private QuoteDto getQuoteDto(ApplyQuoteDto dto, User user, PaymentPayeeAccount payeeAccount1, String clearingNetwork) {
        PayeeRequestDto payeeRequestDto = new PayeeRequestDto();
        payeeRequestDto.setAccountId(payeeAccount1.getAccountId());
//        payeeRequestDto.setRemark(payeeAccount1.getAccountNo() + " Account transaction");
        payeeRequestDto.setRemark(" Account transaction");
        payeeRequestDto.setAmount(dto.getAmount());
        payeeRequestDto.setPaymentMethod(payeeAccount1.getPaymentMethod());
        payeeRequestDto.setClearingNetwork(clearingNetwork);
        PaymentPayOrder quote = this.quote(payeeRequestDto, user);
        QuoteDto quoteDto = getQuoteDto(quote);
        quoteDto.setPaymentAccount(payeeAccount1.getAccountNo());
        return quoteDto;
    }


    public String saveBankPayee(PaymentPayeeSaveDto payeeRequest) {
        payeeRequest.setAccountType("BANK_ACCOUNT");
        payeeRequest.setCountry(payeeRequest.getTwoLetterCode());
        // 将支持的货币都创建出来
        PaymentPayee paymentPayee = new PaymentPayee();
        BeanUtils.copyProperties(payeeRequest, paymentPayee);
        if (paymentPayee.getSubjectType().equals(PayeeEnum.ENTERPRISE.getCode()) && paymentPayee.getAccountHolder() == null) {
            throw new ApiException(ResultCode.PAYMENT_PAY_ENTERPRISE);
        }
        paymentPayee.setCreateTime(DateUtil.getUTCDate());
        Map<String, Object> requestBody = payeeBank(paymentPayee);
        requestBody.put("currencies", Arrays.asList(payeeRequest.getCurrency().split(",")));
        String payeeId = gSalary.savePayee(requestBody);
        paymentPayee.setPayeeId(payeeId);
        paymentPayeeService.save(paymentPayee);
        return payeeId;
    }

    public Map<String, Object> getAccountRegisterFormat(String payeeId, String currency, String language) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("payment_method", "BANK_TRANSFER");
        requestBody.put("language", language);
        requestBody.put("currency", currency);
        JSONObject accountRegisterFormat = gSalary.getAccountRegisterFormat(requestBody, payeeId);
        // 国家
        String country = accountRegisterFormat.getStr("country");
        String currency1 = accountRegisterFormat.getStr("currency");
        JSONArray jsonArray = accountRegisterFormat.getJSONArray("fields");
        Map<String, Object> result = new HashMap<>();
        result.put("country", country);
        result.put("currency", currency1);
        result.put("product", "BANK_TRANSFER");
        JSONArray fields = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONObject field = new JSONObject();
            field.set("key", jsonObject.getStr("key"));
            field.set("label", jsonObject.getStr("label"));
            field.set("required", jsonObject.getStr("required"));
            fields.add(field);
        }
        result.put("field", fields);
        return result;
    }

    public JSONArray getPayoutCountries() {
        RBucket<JSONArray> cache = redisson.getBucket("payment_countries");
        if (!cache.isExists()) {
            List<CountryCode> countryCodes = countryCodeService.list();
            JSONObject entries = gSalary.getPayoutCountries();
            JSONArray jsonArray = entries.getJSONArray("countries");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String country = jsonObject.getStr("country");
                List<CountryCode> collect = countryCodes.stream().filter(c -> country.equals(c.getTwoWordsCode())).collect(Collectors.toList());
                if (!collect.isEmpty() && collect.size() > 0) {
                    CountryCode countryCode = collect.get(0);
                    jsonObject.set("country_zh_cn", countryCode.getCountryZhCn());
                    jsonObject.set("country_en", countryCode.getCountryEn());
                    jsonObject.set("phone_code", countryCode.getPhoneCode());
                }
            }
            cache.set(jsonArray);
        }
        return cache.get();
    }


    /**
     * 清算
     *
     * @param accountId 收款账户id
     * @param amount    金额
     * @return
     */
    public JSONObject getClearingNetworks(String accountId, BigDecimal amount) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("payee_account_id", accountId);
        requestBody.put("pay_currency", "USD");
        requestBody.put("amount", amount.toString());
        requestBody.put("amount_type", "RECEIVE_AMOUNT");
        requestBody.put("receive_currency", "USD");
        return gSalary.getClearingNetworks(requestBody);
    }


    public PaymentPayeeAccount saveAccountRegistry(AccountRegistryDto dto) {
        JSONArray formats = new JSONArray(dto.getFormatJson());
        String accountNumber = null;
        for (int i = 0; i < formats.size(); i++) {
            JSONObject format = formats.getJSONObject(i);
            if ("AccountNumber".equals(format.getStr("key"))) {
                accountNumber = format.getStr("value");
                break;
            }
        }
        PaymentPayeeAccount one = paymentPayeeAccountService.getOne(new LambdaQueryWrapper<PaymentPayeeAccount>()
                .eq(PaymentPayeeAccount::getBusinessId, dto.getBusinessId())
                .eq(!StringUtils.isEmpty(accountNumber), PaymentPayeeAccount::getAccountNo, accountNumber)
                .eq(PaymentPayeeAccount::getPayeeId, dto.getPayeeId()));
        if (null != one) {
            return one;
        }
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("payment_method", "BANK_TRANSFER");
        requestBody.put("currency", dto.getCurrency());
        requestBody.put("fields", formats);
        JSONObject entries = gSalary.saveAccountRegistry(requestBody, dto.getPayeeId());
        PaymentPayeeAccount payeeAccount = new PaymentPayeeAccount();
        payeeAccount.setPaymentMethod("BANK_TRANSFER");
        payeeAccount.setStatus(entries.getStr("status"));
        payeeAccount.setBusinessId(dto.getBusinessId());
        payeeAccount.setCreateTime(DateUtil.getUTCDate());
        payeeAccount.setAccountId(entries.getStr("account_id"));
        payeeAccount.setAccountNo(entries.getStr("account_no"));
        payeeAccount.setPayeeId(dto.getPayeeId());
        paymentPayeeAccountService.save(payeeAccount);
        return payeeAccount;
    }

    public JSONObject getAccounts(String payeeId) {
        return gSalary.getAccounts(null, payeeId);
    }

}
