package com.qf.zpay.service;

import cn.hutool.core.lang.Console;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.gson.Gson;
import com.qf.zpay.constants.*;
import com.qf.zpay.dto.common.CardProductDto;
import com.qf.zpay.dto.req.UCardDto;
import com.qf.zpay.dto.res.v2.PwdResDto;
import com.qf.zpay.dto.res.v2.UpdateContactResDto;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.ChannelConfig;
import com.qf.zpay.service.card.channel.AsinxPhysical;
import com.qf.zpay.service.card.channel.GSalary;
import com.qf.zpay.service.card.dto.CardDetailDto;
import com.qf.zpay.service.pojo.CardTransDto;
import com.qf.zpay.service.pojo.RechargeMoneyAndFee;
import com.qf.zpay.service.pojo.UserCardWithFee;
import com.qf.zpay.util.RandomNameGenerator;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.*;
import generator.mapper.UserCardLogMapper;
import generator.mapper.UserCardMapper;
import generator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static java.lang.Thread.sleep;


/**
 * <AUTHOR>
 * @updateTime 2024/6/3 10:37
 */
@Service
@Slf4j
public class CardService {

    @Autowired
    GSalary gSalary;
    @Autowired
    private UserCardMapper userCardMapper;

    @Autowired
    private UserCardService userCardService;

    @Autowired
    UserCardManageService cardManageService;

    @Autowired
    UserWalletService userWalletService;

    @Autowired
    UserCardFeeConfigService cardFeeConfigService;

    @Autowired
    CardApplyOrderService cardApplyOrderService;

    @Autowired
    UserCardCategoryConfigService userCardCategoryConfigService;

    @Autowired
    AppConfigService appConfigService;
    @Autowired
    CardFactory cardFactory;

    @Autowired
    private CardHelper cardHelper;
    @Autowired
    private DelayQueueService delayQueueService;
    @Autowired
    private UserCardLogMapper userCardLogMapper;


    @Autowired
    private UserCardLogService userCardLogService;


    @Autowired
    private CardTransFeeLogService cardTransFeeLogService;

    @Autowired
    private AsinxPhysical asinxPhysical;

    @Autowired
    private CardHolderService cardHolderService;

    @Autowired
    private CountryCodeService countryCodeService;

    @Autowired
    MailingAddressService mailingAddressService;

    @Transactional(rollbackFor = Exception.class)
    public UserCardWithFee openCard(User user, Integer cardManageId, BigDecimal amount) {
//        amount = amount.setScale(2, RoundingMode.DOWN);
        CardProductDto cardProduct = cardManageService.getAndCheckCardProductInfo(cardManageId, user.getBusinessId());
        if (!cardProduct.getCardStatus()) {
            Assert.fail(ResultCode.CARD_MANAGE_DISABLED);
        }
        if (!cardProduct.getIsAble()) {
            Assert.fail(ResultCode.CARD_PRODUCT_DISABLED);
        }

        Integer alreadyOpenedCardNum = cardManageService.getBusinessCardManageOpenCards(user.getBusinessId(), cardManageId);
        if (cardProduct.getTotalOpenCardNum().compareTo(alreadyOpenedCardNum) <= 0) {
            Assert.fail(ResultCode.CARD_OPEN_LIMIT);
        }

        // 判断是否有集采卡
        if (userCardService.totalCardBought(user.getBusinessId(), cardManageId) > 0) {
            cardProduct.setPlatformOpenCardFee(BigDecimal.ZERO);
        }
        if (cardProduct.getCardModel().equals(CardModelEnum.SHARE)) {
            checkBalance(user.getBusinessId(), cardProduct.getCardModel(), cardProduct.getBusinessOpenCardFee());
        } else {
            BigDecimal totalAmount = cardProduct.getBusinessOpenCardFee().add(cardProduct.getFirstRecharge());
            checkBalance(user.getBusinessId(), cardProduct.getCardModel(), totalAmount);
        }

        CardChannelEnum channel = cardProduct.getChannel();
        cardFactory.getBuilder(channel);
        CardDetailDto cardDetail;

        UserCard userCard = new UserCard();
        switch (channel) {
            case Photon, Photon2, ZNet:
                cardDetail = cardFactory.openCard(cardProduct.getProductCode(), BigDecimal.ZERO);
                break;
            case Asinx:
                UserCardCategoryConfig categoryConfig = userCardCategoryConfigService
                        .getOne(new LambdaQueryWrapper<UserCardCategoryConfig>()
                                .eq(UserCardCategoryConfig::getProductCode, cardProduct.getProductCode()));
                cardDetail = cardFactory.openCard(cardProduct.getProductCode(), BigDecimal.ZERO);
                cardDetail.setCardScheme(categoryConfig.getCardScheme());
                break;
            case GSalary:
                userCard = userCardService.getOneEmptyCard(cardProduct.getProductCode());
                if (userCard == null) {
                    Assert.fail(ResultCode.NO_CARDS);
                }
                cardDetail = new CardDetailDto();
                BeanUtils.copyProperties(userCard, cardDetail);
                break;
            case ZPhysical, AsinxPhysical:
                userCard = userCardService.getOneEmptyCardBought(user.getBusinessId(), cardManageId);
                if (userCard == null) {
                    userCard = userCardService.getOneEmptyCard(cardProduct.getProductCode(), cardManageId);
                }
                if (userCard == null) {
                    Assert.fail(ResultCode.NO_CARDS);
                }
                cardDetail = new CardDetailDto();
                BeanUtils.copyProperties(userCard, cardDetail);
                break;
            default:
                Assert.fail(ResultCode.CARD_CHANNEL_ERROR);
                cardDetail = null;
                break;
        }
        if (cardDetail.getCardId() == null) {
            Assert.fail(ResultCode.CARD_OPEN_FAIL);
        }
        // 将卡片分配给用户
        BeanUtils.copyProperties(cardDetail, userCard);
        userCard.setCardStatus(cardDetail.getCardStatus());
        userCard.setChannel(channel);
        userCard.setUid(user.getBusinessId());
        userCard.setProductCode(cardProduct.getProductCode());
        userCard.setCardModel(cardProduct.getCardModel());
        userCard.setCardManageId(cardProduct.getCardManageId());
        userCard.setOpenCardTime(new Date());
        userCard.setOpenCardDate(new Date());
        int status = ChannelConfig.MUST_APPLY.contains(channel.getCode()) ? 0 : 1;
        userCard.setStatus(status);
        userCard.setCardScheme(cardProduct.getCardScheme());
        userCardService.saveOrUpdate(userCard);
        // 异步执行开卡后操作
        cardHelper.openCardAfter(userCard);

        UserCardWithFee userCardWithFee = new UserCardWithFee();
        BeanUtils.copyProperties(CardHelper.filterCardInfo(userCard), userCardWithFee);
        userCardWithFee.setPlatformOpenCardFee(cardProduct.getPlatformOpenCardFee());
        userCardWithFee.setBusinessOpenCardFee(cardProduct.getBusinessOpenCardFee());

        // 如果有首充
        BigDecimal rechargeMoney = cardProduct.getFirstRecharge();
        if (rechargeMoney.compareTo(BigDecimal.ZERO) > 0) {
            // 执行充值
            try {
                sleep(1000);
                recharge(user, userCard.getCardId(), rechargeMoney);

                RechargeMoneyAndFee rechargeMoneyAndFee = cardHelper.calcRechargeMoney(cardProduct, rechargeMoney);
                userCardWithFee.setPlatformRechargeFee(rechargeMoneyAndFee.getPlatformFee());
                userCardWithFee.setBusinessRechargeFee(rechargeMoneyAndFee.getBusinessFee());
                userCardWithFee.setRechargeMoney(rechargeMoneyAndFee.getRechargeMoney());
            } catch (Exception e) {
                log.error("执行充值失败", e);
            }
        }

        return userCardWithFee;
    }

    /**
     * 检查商户卡片是否有效
     *
     * @param user        商户信息
     * @param cardId      卡片 ID
     * @param checkStatus 是否验证卡状态
     */
    public String checkBizCard(User user, String cardId, Boolean checkStatus) {
        if (user == null) {
            Assert.fail(ResultCode.PARAM_MISS);
        }
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        if (cardInfo == null) {
            cardInfo = userCardMapper.selectOne(new LambdaQueryWrapper<UserCard>().eq(UserCard::getOrderNo, cardId));
            if (cardInfo == null) {
                Assert.fail(ResultCode.USER_CARD_ISNULL);
            }
        }
        if (!cardInfo.getUid().equals(user.getBusinessId())) {
            Assert.fail(ResultCode.USER_CARD_ISNULL);
        }
        if (checkStatus) {
            if (cardInfo.getCardStatus().equals(CardStatusEnum.Blocked)) {
                Assert.fail(ResultCode.CARD_BLOCKED);
            }
            if (cardInfo.getCardStatus().equals(CardStatusEnum.Cancel)) {
                Assert.fail(ResultCode.CARD_DISABLE);
            }
        }
        return cardInfo.getCardId();
    }


    public UserCard cardInfo(String cardId) {
        return cardInfo(cardId, false);
    }

    public UserCard cardInfo(String cardId, Boolean fixForce) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        if (cardInfo == null) {
            Assert.fail(ResultCode.USER_CARD_ISNULL);
        }
        if (CardStatusEnum.Cancel.equals(cardInfo.getCardStatus())) {
            cardInfo.setUpAmount(cardInfo.getAmount());
            return cardInfo;
        }
        // 修正 cardInfo
        if (fixForce || ChannelConfig.FIX_BY_DETAIL.contains(cardInfo.getChannel().getCode())) {
            cardFactory.getBuilder(cardInfo.getChannel());
            CardDetailDto cardDetail = cardFactory.getCardDetail(cardId);
            cardInfo.setCardStatus(cardDetail.getCardStatus());
            if (cardDetail.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                cardInfo.setAmount(cardDetail.getAmount());
            }
            cardInfo.setUpAmount(cardDetail.getAmount());
            cardInfo.setCardCvv(cardDetail.getCardCvv());
            cardInfo.setCardNumber(cardDetail.getCardNumber());
            cardInfo.setCardExpirationMmyy(cardDetail.getCardExpirationMmyy());
            userCardService.updateById(cardInfo);
        }
        return cardInfo;
    }

    public UserCardWithFee recharge(User user, String cardId, BigDecimal amount) {
        amount = amount.setScale(2, RoundingMode.DOWN);
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            Assert.fail(ResultCode.TRANS_AMOUNT_LIMIT);
        }
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        CardModelEnum cardModel = cardInfo.getCardModel();
        CardProductDto cardProduct = cardManageService.getCardProductInfo(cardInfo.getCardManageId(), cardInfo.getUid());

        RechargeMoneyAndFee rechargeMoneyAndFee = cardHelper.calcRechargeMoney(cardProduct, amount);
        BigDecimal rechargeMoney = rechargeMoneyAndFee.getRechargeMoney();

        if (cardModel == CardModelEnum.SHARE) {
            UserWallet userWallet = checkBalance(user.getBusinessId(), cardInfo.getCardModel(), rechargeMoneyAndFee.getBusinessFee());
            BigDecimal cardAmtNow = cardInfo.getAmount().add(rechargeMoney);
            if (cardAmtNow.compareTo(userWallet.getShareWallet()) >= 0) {
                rechargeMoney = userWallet.getShareWallet().subtract(cardInfo.getAmount());
            }
        } else {
            checkBalance(user.getBusinessId(), cardInfo.getCardModel(), amount);
        }
        // 扣除未扣款的金额后可充值金额
        BigDecimal newRechargeMoney = cardHelper.computeDeductAmountV2(cardInfo, rechargeMoney);
        if (newRechargeMoney.compareTo(BigDecimal.ZERO) > 0) {
//        log.error("充值金额：{}，手续费：{}，实际到账：{}", amount, rechargeFee, newRechargeMoney);
            if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(cardInfo.getChannel().getCode())) {
                newRechargeMoney = cardInfo.getAmount().add(newRechargeMoney);
            }
            if (!newRechargeMoney.equals(BigDecimal.ZERO)) {
                cardFactory.getBuilder(cardInfo.getChannel());
                Boolean res = cardFactory.recharge(cardId, newRechargeMoney);
                if (!res) {
                    Assert.fail(ResultCode.CARD_RECHARGE_FAIL);
                }
            }
        }
        // 异步执行充值后操作
        cardHelper.cardRechargeAfter(cardInfo, amount);

        UserCardWithFee userCardWithFee = new UserCardWithFee();
        BeanUtils.copyProperties(cardInfo, userCardWithFee);

        userCardWithFee.setPlatformRechargeFee(rechargeMoneyAndFee.getPlatformFee());
        userCardWithFee.setBusinessRechargeFee(rechargeMoneyAndFee.getBusinessFee());
        userCardWithFee.setRechargeMoney(rechargeMoney);
        userCardWithFee.setAmount(cardInfo.getAmount().add(rechargeMoney));
        return userCardWithFee;
    }

    public UserCard changeStatus(String cardId, CardStatusActionEnum status) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        cardFactory.getBuilder(cardInfo.getChannel());
        CardStatusEnum cardStatusNow = switch (status) {
            case Freeze -> {
                if (!cardInfo.getCardStatus().equals(CardStatusEnum.Active)) {
                    Assert.fail(ResultCode.CARD_BLOCKED);
                }
                yield CardStatusEnum.Blocked;
            }
            case Unfreeze -> {
                if (cardInfo.getCardStatus().equals(CardStatusEnum.Active)) {
                    Assert.fail(ResultCode.CARD_ACTIVE);
                }
                yield CardStatusEnum.Active;
            }
            default -> {
                Assert.fail(ResultCode.SYS_ERR);
                yield null;
            }
        };
        Boolean res = cardFactory.changeStatus(cardId, status);
        if (!res) {
            Assert.fail(ResultCode.CARD_SET_FAIL);
        }
        if (CardStatusActionEnum.Unfreeze.equals(status)) {
            long minutesToMillis = 3L * 60 * 1000;
            delayQueueService.addToDelayQueue(cardId, minutesToMillis);
        }
        cardInfo.setCardStatus(cardStatusNow);
        userCardService.updateById(cardInfo);
        return cardInfo;
    }

    public UserCardWithFee cancelCard(String cardId) {
        UserCard cardInfo = cardInfo(cardId, true);

        cardFactory.getBuilder(cardInfo.getChannel());

        if (cardInfo.getCardModel().equals(CardModelEnum.PHYSICAL)) {
            Assert.fail(ResultCode.CARD_NOT_SUPPORT);
        }

        if (!cardInfo.getCardStatus().equals(CardStatusEnum.Active)) {
            Assert.fail(ResultCode.CARD_BLOCKED);
        }

        UserCardFeeConfig cardFeeConfig = cardFeeConfigService.getCardFeeConfig(cardInfo.getCardManageId(), cardInfo.getUid());
        checkBalance(cardInfo.getUid(), cardInfo.getCardModel(), cardFeeConfig.getBusinessCancelCardFee());

        Boolean res = cardFactory.cancelCard(cardId);
        if (!res) {
            Assert.fail(ResultCode.CARD_CANCEL_FAIL);
        }

        if (ChannelConfig.MUST_APPLY.contains(cardInfo.getChannel().getCode())) {
            CardApplyOrder apply = new CardApplyOrder();
            apply.setBusinessId(cardInfo.getUid());
            apply.setOrderId(ZHelperUtil.genOrderId("Z-CCA"));
            apply.setCardId(cardInfo.getCardId());
            apply.setCardNumber(cardInfo.getCardNumber());
            apply.setAction(8);
            apply.setFee(cardFeeConfig.getPlatformCancelCardFee());
            apply.setStatus(0);
            apply.setChannel(cardInfo.getChannel());
            cardApplyOrderService.save(apply);
        } else {
            // 异步执行注销后操作
            cardHelper.cardCancelAfter(cardInfo);
        }


        UserCardWithFee userCardWithFee = new UserCardWithFee();
        BeanUtils.copyProperties(cardInfo, userCardWithFee);
        userCardWithFee.setCardStatus(CardStatusEnum.Cancel);

        userCardWithFee.setPlatformCancelCardFee(cardFeeConfig.getPlatformCancelCardFee());
        userCardWithFee.setBusinessCancelCardFee(cardFeeConfig.getBusinessCancelCardFee());

        return userCardWithFee;
    }

    public UserCardWithFee withdraw(String cardId, BigDecimal amount) {
        amount = amount.setScale(2, RoundingMode.DOWN);
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            Assert.fail(ResultCode.TRANS_AMOUNT_LIMIT);
        }
        UserCard cardInfo = cardInfo(cardId, true);
        if (cardInfo.getCardModel().equals(CardModelEnum.SHARE)) {
            Assert.fail(ResultCode.CARD_NOT_SUPPORT);
        }
        if (cardInfo.getAmount().compareTo(amount) < 0) {
            Assert.fail(ResultCode.CARD_BALANCE_NOT_ENOUGH);
        }

//        CardProductDto cardProduct = cardManageService.getCardProductInfo(cardInfo.getCardManageId(), cardInfo.getUid());
//        RechargeMoneyAndFee rechargeMoneyAndFee = cardHelper.calcRechargeMoney(cardProduct, amount);
//        BigDecimal rechargeMoney = rechargeMoneyAndFee.getRechargeMoney();
        BigDecimal rechargeMoney = amount.negate();
        if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(cardInfo.getChannel().getCode())) {
            rechargeMoney = cardInfo.getAmount().add(rechargeMoney);
        }

        cardFactory.getBuilder(cardInfo.getChannel());
        Boolean res = cardFactory.recharge(cardId, rechargeMoney);
        if (!res) {
            Assert.fail(ResultCode.CARD_RECHARGE_FAIL);
        }

        // 异步执行提现后操作
        cardHelper.cardWithdrawAfter(cardInfo, amount);

        UserCardWithFee userCardWithFee = new UserCardWithFee();
        BeanUtils.copyProperties(cardInfo, userCardWithFee);

        userCardWithFee.setRechargeMoney(rechargeMoney);
        userCardWithFee.setAmount(cardInfo.getAmount().add(rechargeMoney));
        return userCardWithFee;
    }

    public UpdateContactResDto updateContact(String cardId, String email) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        List<String> needSave2Up = findAppConfigByKey("need_set_contact_up");
        List<String> needSave = findAppConfigByKey("need_set_contact");
        List<String> all = new ArrayList<>(needSave2Up);
        all.addAll(needSave);
        if (!all.contains(cardInfo.getProductCode())) {
            Assert.fail(ResultCode.CARD_NOT_SUPPORT);
        }
        cardInfo.setContact(email);
        userCardService.updateById(cardInfo);

        UpdateContactResDto contactResDto = new UpdateContactResDto();
        contactResDto.setCardId(cardId);
        contactResDto.setEmail(email);
        if (needSave2Up.contains(cardInfo.getProductCode())) {
            cardFactory.getBuilder(cardInfo.getChannel());
            Boolean res = cardFactory.updateContact(cardId, email);
            if (!res) {
                contactResDto.setEmail(null);
            }
        }
        return contactResDto;
    }


    public List<String> findAppConfigByKey(String appKey) {
        AppConfig appConfigs = appConfigService.selectByKey(appKey);
        return Arrays.asList(appConfigs.getValue().split(","));
    }

    public List<String> findAppConfig() {
        List<String> keys = Arrays.asList("need_set_contact_up", "need_set_contact");
        List<AppConfig> appConfigs = new ArrayList<>();
        for (String key : keys) {
            AppConfig appConfig = appConfigService.selectByKey(key);
            appConfigs.add(appConfig);
        }
        String values = appConfigs.stream().map(a -> a.getValue()).collect(Collectors.joining(","));
        return Arrays.asList(values.split(","));
    }

    public PwdResDto setBankcardPin(String cardId, String pwd) {
        UserCard cardInfo = userCardMapper.getOneByCardId(cardId);
        if (!"168".equals(cardInfo.getProductCode())) {
            Assert.fail(ResultCode.CARD_NOT_SUPPORT);
        }
        asinxPhysical.setBankcardPin(cardId, pwd);
        PwdResDto pwdResDto = new PwdResDto();
        pwdResDto.setCardId(cardId);
        pwdResDto.setPwd(pwd);
        return pwdResDto;
    }

    public void doCardMonthFeeByCardId(String cardId) {
        Date date = new Date();
        UserCard userCard = userCardService.getByCardId(cardId);
        BigDecimal surplus = BigDecimal.ZERO;
        UserCardFeeConfig userCardFeeConfig = cardFeeConfigService.getOne(new LambdaQueryWrapper<UserCardFeeConfig>()
                .eq(UserCardFeeConfig::getCardManageId, userCard.getCardManageId())
                .eq(UserCardFeeConfig::getBusinessId, userCard.getUid()));
        if (userCardFeeConfig == null) {
            log.info("【月费收取】卡片未找到卡段配置 cardId:{}  BusinessId：{}  ,CardManageId: {}", userCard.getCardId(), userCard.getUid(), userCard.getCardManageId());
            throw new RuntimeException("【月费收取】卡片未找到商户卡段配置");
        }
        BigDecimal cardMonthFee = userCardFeeConfig.getCardMonthFee();
        String orderId = ZHelperUtil.genOrderId("Z-CO");
        UserCardLog userCardLog = new UserCardLog();
        userCardLog.setOrderId(orderId);
        userCardLog.setBusinessId(userCard.getUid());
        userCardLog.setAuthTime(date);
        userCardLog.setTransAmountCurrency("USD");
        userCardLog.setTransAmount(cardMonthFee);
        userCardLog.setAuthAmountCurrency("USD");
        userCardLog.setAuthAmount(cardMonthFee);
        userCardLog.setNoticeType("cardPay");
        userCardLog.setCardId(userCard.getCardId());
        userCardLog.setProductCode(userCard.getProductCode());
        userCardLog.setProductName(userCard.getCardNickname());
        userCardLog.setCardModel(userCard.getCardModel());
        userCardLog.setCardAlias(userCard.getCardNickname());
        userCardLog.setTransactionType("ServiceFee");
        userCardLog.setFundsDirection("Expenditure");
        userCardLog.setAvailableCredit(userCard.getAmount());
        userCardLog.setCreateTime(date);
        userCardLog.setCreateDate(date);
        userCardLog.setMaskCardNumber(userCard.getCardNumber());
        userCardLog.setMaskCardNumber(userCard.getCardNumber());
        userCardLog.setMerchantName("ZNET");
        userCardLog.setStatus("Settled");

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar thisMonthFirstDateCal = Calendar.getInstance();
        thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime());
        // 本月末尾
        Calendar thisMonthEndDateCal = Calendar.getInstance();
        thisMonthEndDateCal.set(Calendar.DAY_OF_MONTH, thisMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
        String thisMonthEndTime = format.format(thisMonthEndDateCal.getTime());

        CardTransFeeLog cardTransFeeLog = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                .between(CardTransFeeLog::getCreateTime, (thisMonthFirstTime + " 00:00:00"), (thisMonthEndTime + " 23:59:59"))
                .last("limit 1"));
        // 余额

        CardTransDto dto = null;
        BigDecimal negate = null;
        try {
            dto = new CardTransDto();
            cardMonthFee = cardMonthFee.negate();
            dto.setChangeFee(cardMonthFee);
            if (cardMonthFee.compareTo(BigDecimal.ZERO) < 0) {
                if ("Active".equals(userCard.getCardStatus().getCode()) && userCard.getAmount().compareTo(cardMonthFee.abs()) >= 0) {
                    if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                        cardMonthFee = userCard.getAmount().add(cardMonthFee);
                    }
                    cardFactory.getBuilder(userCard.getChannel());
                    cardFactory.recharge(userCard.getCardId(), cardMonthFee);
                } else if (userCard.getAmount().compareTo(cardMonthFee.abs()) <= 0 ||
                        !"Active".equals(userCard.getCardStatus().getCode())) {
                    // 计算剩余待扣款金额
                    // 正常卡片正常返还上游月费
                    // 异常的卡片
                    surplus = cardMonthFee;
                    dto.setChangeFee(surplus);
                    if (cardTransFeeLog != null && !"Active".equals(userCard.getCardStatus().getCode())) {
                        dto.setChangeFee(cardMonthFee.subtract(cardTransFeeLog.getCostFee()));
                    }
                }
                userCard.setAmount(userCard.getAmount().add(cardMonthFee));
                userCardService.updateById(userCard);
                userCardLog.setStatus("Settled");
            }
            userCardLog.setAvailableCredit(userCard.getAmount());
            userCardLogMapper.insert(userCardLog);
            negate = cardMonthFee.negate();
        } catch (Exception e) {
            log.error("月费收取异常{}", e.getMessage());
            throw new RuntimeException("月费收取失败");
        }
        if (cardTransFeeLog == null) {
            cardTransFeeLog = new CardTransFeeLog();
            cardTransFeeLog.setMoney(dto.getAuthAmount());
            cardTransFeeLog.setCostFee(dto.getTotalFee());
            cardTransFeeLog.setCardModel(userCard.getCardModel());
        }
        CardTransFeeLog one = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                .isNotNull(CardTransFeeLog::getAccruingAmounts)
                .orderByDesc(CardTransFeeLog::getCreateTime)
                .last("limit 1"));
        cardTransFeeLog.setAccruingAmounts(dto.getChangeFee());
        if (one != null && one.getAccruingAmounts() != null) {
            cardTransFeeLog.setAccruingAmounts(one.getAccruingAmounts().add(dto.getChangeFee()));
        }
        cardTransFeeLog.setCardId(userCard.getCardId());
        cardTransFeeLog.setChangeFee(dto.getChangeFee());
        cardTransFeeLog.setBusinessId(userCard.getUid());
        cardTransFeeLog.setPlatformFee(negate);
        cardTransFeeLog.setCreateTime(date);
        cardTransFeeLog.setCreateDate(date);
        cardTransFeeLog.setStatus("success");
        cardTransFeeLog.setTransactionType(CardTransTypeEnum.ServiceFee.getCode());
        cardTransFeeLog.setPlatformFeeDetail("{\"card_month_fee\":" + negate + "}");
        cardTransFeeLogService.saveOrUpdate(cardTransFeeLog);
        if (surplus.compareTo(BigDecimal.ZERO) < 0) {
            UserWalletAdvanceLog advanceLog = new UserWalletAdvanceLog();
            BeanUtils.copyProperties(cardTransFeeLog, advanceLog);
            advanceLog.setId(null);
            userWalletService.changeAmount(userCard.getUid(), surplus, userCard.getCardModel(), WalletTypeEnum.ADVANCE_CARD_FEES, advanceLog, null);
        }
    }


    /**
     * 月费收取
     */
    public void doCardMonthFee() {
        Date date = new Date();
        // 所以活跃卡
        List<UserCard> activeCard = userCardService.list(new LambdaQueryWrapper<UserCard>()
                .and(i -> i.eq(UserCard::getCardStatus, "Active")
                        .or()
                        .eq(UserCard::getCardStatus, "Blocked"))
                .eq(UserCard::getCardModel, "recharge"));
        List<UserCardFeeConfig> userCardFeeConfigs = cardFeeConfigService.list();
        for (UserCard userCard : activeCard) {
            boolean amountState = userCard.getAmount().compareTo(BigDecimal.ZERO) < 0;
            BigDecimal surplus = BigDecimal.ZERO;
            List<UserCardFeeConfig> userCardFeeConfig = userCardFeeConfigs.stream().filter(cf -> cf.getCardManageId().equals(userCard.getCardManageId()) &&
                    cf.getBusinessId().equals(userCard.getUid())).toList();
            if (userCardFeeConfig.isEmpty()) {
                log.info("【月费收取】卡片未找到商户卡段配置 cardId:{}  BusinessId：{}  ,CardManageId: {}", userCard.getCardId(), userCard.getUid(), userCard.getCardManageId());
                continue;
            }
            BigDecimal cardMonthFee = userCardFeeConfig.getFirst().getCardMonthFee();
            String orderId = ZHelperUtil.genOrderId("Z-CO");
            UserCardLog userCardLog = new UserCardLog();
            userCardLog.setOrderId(orderId);
            userCardLog.setBusinessId(userCard.getUid());
            userCardLog.setAuthTime(date);
            userCardLog.setTransAmountCurrency("USD");
            userCardLog.setTransAmount(cardMonthFee);
            userCardLog.setAuthAmountCurrency("USD");
            userCardLog.setAuthAmount(cardMonthFee);
            userCardLog.setNoticeType("cardPay");
            userCardLog.setCardId(userCard.getCardId());
            userCardLog.setProductCode(userCard.getProductCode());
            userCardLog.setProductName(userCard.getCardNickname());
            userCardLog.setCardModel(userCard.getCardModel());
            userCardLog.setCardAlias(userCard.getCardNickname());
            userCardLog.setTransactionType("ServiceFee");
            userCardLog.setFundsDirection("Expenditure");
            userCardLog.setAvailableCredit(userCard.getAmount());
            userCardLog.setCreateTime(date);
            userCardLog.setCreateDate(date);
            userCardLog.setMaskCardNumber(userCard.getCardNumber());
            userCardLog.setMaskCardNumber(userCard.getCardNumber());
            userCardLog.setMerchantName("ZNET");
            userCardLog.setStatus("Settled");

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar thisMonthFirstDateCal = Calendar.getInstance();
            thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
            String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime());
            // 本月末尾
            Calendar thisMonthEndDateCal = Calendar.getInstance();
            thisMonthEndDateCal.set(Calendar.DAY_OF_MONTH, thisMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
            String thisMonthEndTime = format.format(thisMonthEndDateCal.getTime());

            CardTransFeeLog cardTransFeeLog = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                    .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                    .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                    .between(CardTransFeeLog::getCreateTime, (thisMonthFirstTime + " 00:00:00"), (thisMonthEndTime + " 23:59:59"))
                    .last("limit 1"));
            // 余额

            CardTransDto dto = null;
            BigDecimal negate = null;
            boolean ifReceivable = false;
            try {
                dto = new CardTransDto();
                cardMonthFee = cardMonthFee.negate();
                dto.setChangeFee(cardMonthFee);
                if (cardMonthFee.compareTo(BigDecimal.ZERO) < 0) {
                    if ("Active".equals(userCard.getCardStatus().getCode()) && userCard.getAmount().compareTo(cardMonthFee.abs()) >= 0) {
                        if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                            cardMonthFee = userCard.getAmount().add(cardMonthFee);
                        }
                        cardFactory.getBuilder(userCard.getChannel());
                        cardFactory.recharge(userCard.getCardId(), cardMonthFee);
                    } else if (userCard.getAmount().compareTo(cardMonthFee.abs()) <= 0 ||
                            !"Active".equals(userCard.getCardStatus().getCode())) {
                        ifReceivable = true;
                        // 计算剩余待扣款金额
                        // 正常卡片正常返还上游月费
                        // 异常的卡片
                        surplus = cardMonthFee;
                        dto.setChangeFee(surplus);
                        if (cardTransFeeLog != null && !"Active".equals(userCard.getCardStatus().getCode())) {
                            dto.setChangeFee(cardMonthFee.subtract(cardTransFeeLog.getCostFee()));
                        }
                    }
                    userCard.setAmount(userCard.getAmount().add(cardMonthFee));
                    userCardService.updateById(userCard);
                    userCardLog.setStatus("Settled");
                }
                userCardLog.setAvailableCredit(userCard.getAmount());
                userCardLogMapper.insert(userCardLog);
                negate = cardMonthFee.negate();
            } catch (Exception e) {
                log.error("月费收取异常, 卡ID: {} {}", userCard.getCardId(), e.getMessage());
                continue;
            }

            if (cardTransFeeLog == null) {
                cardTransFeeLog = new CardTransFeeLog();
                cardTransFeeLog.setMoney(dto.getAuthAmount());
                cardTransFeeLog.setCostFee(dto.getTotalFee());
                cardTransFeeLog.setCardModel(userCard.getCardModel());
            }
            CardTransFeeLog one = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                    .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                    .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                    .isNotNull(CardTransFeeLog::getAccruingAmounts)
                    .orderByDesc(CardTransFeeLog::getCreateTime)
                    .last("limit 1"));
            if (ifReceivable) {
                cardTransFeeLog.setAccruingAmounts(dto.getChangeFee());
                if (one != null && one.getAccruingAmounts() != null) {
                    cardTransFeeLog.setAccruingAmounts(one.getAccruingAmounts().add(dto.getChangeFee()));
                }
            } else {
                if (one != null) {
                    cardTransFeeLog.setAccruingAmounts(one.getAccruingAmounts() != null ? one.getAccruingAmounts() : BigDecimal.ZERO);
                }
            }
            cardTransFeeLog.setCardId(userCard.getCardId());
            cardTransFeeLog.setChangeFee(dto.getChangeFee());
            cardTransFeeLog.setBusinessId(userCard.getUid());
            cardTransFeeLog.setPlatformFee(negate);
            cardTransFeeLog.setCreateTime(date);
            cardTransFeeLog.setCreateDate(date);
            cardTransFeeLog.setStatus("success");
            cardTransFeeLog.setTransactionType(CardTransTypeEnum.ServiceFee.getCode());
            cardTransFeeLog.setPlatformFeeDetail("{\"card_month_fee\":" + negate + "}");
            cardTransFeeLogService.saveOrUpdate(cardTransFeeLog);
            if (surplus.compareTo(BigDecimal.ZERO) < 0) {
                UserWalletAdvanceLog advanceLog = new UserWalletAdvanceLog();
                BeanUtils.copyProperties(cardTransFeeLog, advanceLog);
                advanceLog.setId(null);
                userWalletService.changeAmount(userCard.getUid(), surplus, userCard.getCardModel(), WalletTypeEnum.ADVANCE_CARD_FEES, advanceLog, null);
            }
            ThreadUtil.sleep(10);
        }
    }

    /**
     * 手续费收取
     *
     * @param dto 上游返回参数
     */
    public Boolean doCardTrans(CardTransDto dto) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date authTime = dto.getAuthTime();
        UserCard userCard = userCardService.getByCardId(dto.getCardId());
        boolean amountState = userCard.getAmount().compareTo(BigDecimal.ZERO) <= 0;
        BigDecimal surplus = BigDecimal.ZERO;
        Long count = userCardLogMapper.selectCount(new LambdaQueryWrapper<UserCardLog>()
                .eq(UserCardLog::getCardId, dto.getCardId())
                .eq(UserCardLog::getBusinessId, userCard.getUid())
                .eq(UserCardLog::getOrderId, dto.getOrderId())
                .eq(UserCardLog::getTransAmount, dto.getTransAmount()));
        if (count > 0) {
            return false;
        }
        Date date = new Date();
        UserCardLog userCardLog = new UserCardLog();
        BeanUtils.copyProperties(dto, userCardLog);
        userCardLog.setBusinessId(userCard.getUid());
        userCardLog.setNoticeType("cardPay");
        userCardLog.setProductCode(userCard.getProductCode());
        userCardLog.setProductName(userCard.getCardNickname());
        userCardLog.setCardModel(userCard.getCardModel());
        userCardLog.setCardAlias(userCard.getCardNickname());
        userCardLog.setTransactionType(dto.getTransactionTypeEnum().getCode());
        userCardLog.setAvailableCredit(userCard.getAmount());
        userCardLog.setCreateTime(date);
        userCardLog.setCreateDate(date);
        userCardLog.setMaskCardNumber(userCard.getCardNumber());
        // 查询平台收费
        UserCardFeeConfig cardFeeConfig = cardFeeConfigService.getCardFeeConfig(userCard.getCardManageId(), userCard.getUid());
        // 平台的手续费（包含上游手续费）
        BigDecimal platformFee = new BigDecimal(0.00);
        // 该扣或者加 用户的金额
        BigDecimal rechargeMoney = BigDecimal.valueOf(0.00);
        switch (dto.getTransactionTypeEnum()) {
            case Auth, CorrectiveRefundVoid, RefundReversal:
                if (cardFeeConfig.getPreAuthFee().compareTo(BigDecimal.ZERO) >= 0) {
                    platformFee = (cardFeeConfig.getPreAuthFee()
                            .add(dto.getAuthAmount().multiply(cardFeeConfig.getCrossBroadFeeRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN)))
                            .add(dto.getAuthAmount().multiply(cardFeeConfig.getTransactionFeeRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN))))
                            .negate();
                    platformFee = platformFee.setScale(2, RoundingMode.UP);
                }
                rechargeMoney = (platformFee.negate().add(dto.getTotalFee())).negate(); // 可以使用减
                break;
            case Refund, Void, CorrectiveRefund, CorrectiveAuth:
                if (cardFeeConfig.getPreAuthFee().compareTo(BigDecimal.ZERO) >= 0) {
                    platformFee = (cardFeeConfig.getPreAuthFee()
                            .add(dto.getAuthAmount().multiply(cardFeeConfig.getCrossBroadFeeRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN)))
                            .add(dto.getAuthAmount().multiply(cardFeeConfig.getTransactionFeeRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN))));
                    platformFee = platformFee.setScale(2, RoundingMode.UP).subtract(cardFeeConfig.getRefundFee());
                }
                if (platformFee.compareTo(BigDecimal.ZERO) > 0 && dto.getTotalFee().compareTo(BigDecimal.ZERO) < 0) {
                    rechargeMoney = platformFee.add(dto.getTotalFee());
                } else if (platformFee.compareTo(BigDecimal.ZERO) < 0 && dto.getTotalFee().compareTo(BigDecimal.ZERO) < 0) {
                    rechargeMoney = platformFee.subtract(dto.getTotalFee());
                } else if (platformFee.compareTo(BigDecimal.ZERO) > 0 && dto.getTotalFee().compareTo(BigDecimal.ZERO) > 0) {
                    rechargeMoney = platformFee.subtract(dto.getTotalFee());
                } else if (platformFee.compareTo(BigDecimal.ZERO) < 0 && dto.getTotalFee().compareTo(BigDecimal.ZERO) > 0) {
                    rechargeMoney = platformFee.add(dto.getTotalFee());
                } else if (dto.getTotalFee().compareTo(BigDecimal.ZERO) == 0) {
                    rechargeMoney = platformFee;
                }
                break;
            case Verification:
                break;
            case ServiceFee:
                // increase" : "decrease
                BigDecimal transAmount = dto.getTransAmount().negate();
                BigDecimal increase = BigDecimal.valueOf(1).add(dto.getTransAmount());
                BigDecimal decrease = BigDecimal.valueOf(1).negate();
                if ("Active".equals(userCard.getCardStatus().getCode()) && dto.getUpAmount().compareTo(BigDecimal.ZERO) >= 0) {
                    if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                        increase = userCard.getAmount().add(increase);
                    }
                    cardFactory.getBuilder(userCard.getChannel());
                    cardFactory.recharge(dto.getCardId(), increase);

                    if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                        decrease = userCard.getAmount().add(decrease);
                    }
                    cardFactory.getBuilder(userCard.getChannel());
                    cardFactory.recharge(dto.getCardId(), decrease);
                }
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Calendar thisMonthFirstDateCal = Calendar.getInstance();
                thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
                String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime());
                // 本月末尾
                Calendar thisMonthEndDateCal = Calendar.getInstance();
                thisMonthEndDateCal.set(Calendar.DAY_OF_MONTH, thisMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
                String thisMonthEndTime = format.format(thisMonthEndDateCal.getTime());
                // 上游收款回调时补全本地数据
                CardTransFeeLog cardTransFeeLog = cardTransFeeLogService.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                        .eq(CardTransFeeLog::getCardId, userCard.getCardId())
                        .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                        .between(CardTransFeeLog::getCreateTime, thisMonthFirstTime + " 00:00:00", thisMonthEndTime + " 23:59:59")
                        .last("limit 1"));
                if (cardTransFeeLog == null) {
                    cardTransFeeLog = new CardTransFeeLog();
                    cardTransFeeLog.setCardId(dto.getCardId());
                    cardTransFeeLog.setBusinessId(userCard.getUid());
                }
                cardTransFeeLog.setAccruingAmounts(null);
                cardTransFeeLog.setAuthTime(authTime);
                cardTransFeeLog.setMoney(transAmount);
                cardTransFeeLog.setCostFee(transAmount);
                cardTransFeeLog.setPlatformEarn(cardFeeConfig.getCardMonthFee().add(transAmount));
                cardTransFeeLog.setPlatformFee(cardFeeConfig.getCardMonthFee());
                cardTransFeeLog.setOrderId(dto.getOrderId());
                cardTransFeeLog.setTransactionType(dto.getTransactionTypeEnum().getCode());
                cardTransFeeLog.setBusinessFeeDetail(dto.getBusinessFeeDetail());
                cardTransFeeLogService.saveOrUpdate(cardTransFeeLog);
                return true;
            default:
                throw new IllegalArgumentException("卡片交易类型错误");
        }
        // 扣商户
        UserWalletAdvanceLog advanceLog = new UserWalletAdvanceLog();
        BeanUtils.copyProperties(dto, advanceLog);
        // -0.58  -1.76
        BigDecimal upAmount = userCard.getAmount().compareTo(BigDecimal.ZERO) < 0 ? dto.getUpAmount().add(userCard.getAmount()) : dto.getUpAmount();
        // 判断是否为首次负数
        JSONObject businessFeeDetail = new JSONObject();
        boolean b = false;
        // 上游卡余额 <= 本次交易金额
        if (upAmount.abs().compareTo(dto.getAuthAmount()) <= 0 &&
                upAmount.compareTo(BigDecimal.ZERO) <= 0) {
            // 扣除商户
            surplus = dto.getUpAmount();
            businessFeeDetail.set("primaryNegative", surplus);
            // 上游
            advanceLog.setCostFee(surplus);
            userCard.setAmount(upAmount);
            b = true;
        } else if (upAmount.abs().compareTo(dto.getAuthAmount()) >= 0 &&
                upAmount.compareTo(BigDecimal.ZERO) <= 0) {
            // 余额为负数时，再次消费 此时垫付的就时 本金加手续费加平台费
            surplus = dto.getTotalFee().add(dto.getAuthAmount().compareTo(BigDecimal.ZERO) < 0 ? dto.getAuthAmount().negate() : dto.getAuthAmount());
            businessFeeDetail.set("multipleNegative", surplus);
            // 上游
            advanceLog.setCostFee(surplus);
            userCard.setAmount(userCard.getAmount().add(surplus));
        } else if ("AuthFailure".equals(dto.getStatus()) &&
                dto.getTotalFee().abs().compareTo(dto.getAuthAmount()) <= 0 &&
                dto.getUpAmount().compareTo(BigDecimal.ZERO) <= 0) {
            // 余额为负数时 失败只考虑手续费
            surplus = dto.getTotalFee();
            businessFeeDetail.set("cardError", surplus);
            // 上游
            advanceLog.setCostFee(surplus);
        }
        // 是否收费
        if (cardFeeConfig.getPreAuthFee().compareTo(BigDecimal.ZERO) < 0) {
            saveFee(dto, userCardLog, userCard);
            // 当上游金额不足时
            if ((upAmount.compareTo(BigDecimal.ZERO) < 0) ||
                    !"Active".equals(userCard.getCardStatus().getCode())) {
                if (surplus.compareTo(BigDecimal.ZERO) == 0) {
                    surplus = (dto.getTotalFee());
                }
                advanceLog.setCostFee(surplus);
                advanceLog.setChangeFee(surplus);
                advanceLog.setAccruingAmounts(surplus);
                if (b) {
                    userCard.setAmount(surplus);
                }
                userCardService.updateById(userCard);
            }
        } else {
            // Income:收入、Expenditure:支出、自己加的 Nochange不变化
            String fundsDirection = "Nochange";
            if (platformFee.compareTo(BigDecimal.valueOf(0.00)) == 0) {
                fundsDirection = "Nochange";
            } else if (platformFee.compareTo(BigDecimal.valueOf(0.00)) > 0) {
                fundsDirection = "Income";
            } else if (platformFee.compareTo(BigDecimal.valueOf(0.00)) < 0) {
                fundsDirection = "Expenditure";
                platformFee = platformFee.negate();
            }
            BigDecimal availableCredit = BigDecimal.ZERO;
            // 判断卡片状态
            if (platformFee.compareTo(BigDecimal.valueOf(0.00)) != 0 && rechargeMoney.compareTo(BigDecimal.ZERO) != 0) {
                // 正常
                if ((userCard.getAmount().compareTo(rechargeMoney.abs()) >= 0) &&
                        CardStatusEnum.Active.equals(userCard.getCardStatus())) {
                    if (ChannelConfig.RECHARGE_MUST_TOTAL_AMOUNT.contains(userCard.getChannel().getCode())) {
                        rechargeMoney = userCard.getAmount().add(rechargeMoney);
                    }
                    dto.setChangeFee(rechargeMoney.negate());
                    cardFactory.getBuilder(userCard.getChannel());
                    cardFactory.recharge(dto.getCardId(), rechargeMoney);
                    availableCredit = userCard.getAmount().add(rechargeMoney);
                } else if (userCard.getAmount().compareTo(rechargeMoney.abs()) <= 0 ||
                        !"Active".equals(userCard.getCardStatus().getCode())) {
                    // 异常  当前余额不足扣款以及足够扣款但是卡片状态为冻结状态的
                    // 上游 5
                    advanceLog.setCostFee(surplus);
                    businessFeeDetail.set("platformFee", rechargeMoney);
                    surplus = surplus.add(rechargeMoney);
                    dto.setChangeFee(rechargeMoney);
                    // 平台 3
                    advanceLog.setPlatformFee(rechargeMoney.negate());
                    // 变动 8
                    advanceLog.setChangeFee(surplus);
                    // 累计
                    advanceLog.setAccruingAmounts(surplus);
                    availableCredit = b ? (surplus) : userCard.getAmount().add(surplus);
                }
                userCard.setAmount(availableCredit);
                userCardService.updateById(userCard);
            }
            userCardLog.setAvailableCredit(availableCredit);
            userCardLogMapper.insert(userCardLog);
            // 手续费添加
            userCardLogService.transFeeSave(userCardLog, platformFee, fundsDirection);
            // 手续费详情日志tb_card_action_log
            dto.setPlatformFeeDetail(postTingJson(cardFeeConfig));
            cardTransFeeLogService.cardTransFeeLogSave(dto, userCard, platformFee, rechargeMoney);
        }
        if (surplus.compareTo(BigDecimal.ZERO) < 0) {
            advanceLog.setBusinessId(userCard.getUid());
            advanceLog.setBusinessFeeDetail(businessFeeDetail.toString());
            userWalletService.changeAmount(userCard.getUid(), surplus, userCard.getCardModel(), WalletTypeEnum.ADVANCE_CARD_FEES, advanceLog, null);
        }
        return true;
    }

    private void saveFee(CardTransDto dto, UserCardLog userCardLog, UserCard userCard) {
        userCardLog.setAvailableCredit(userCard.getAmount());
        userCardLogMapper.insert(userCardLog);
        userCardLog.setId(null);
        userCardLog.setTransAmount(null);
        userCardLog.setTransAmountCurrency(null);
        BigDecimal transFee = dto.getTotalFee().abs();
        userCardLog.setFundsDirection(getFundsDirection(dto.getTotalFee()));
        userCardLog.setSettledAmount(transFee);
        userCardLog.setAuthAmountCurrency(dto.getTotalFeeCurrency());
        userCardLog.setAuthAmount(transFee);
        if (CardTransTypeEnum.Refund.getCode().equals(userCardLog.getTransactionType())) {
            userCardLog.setTransactionType(CardTransTypeEnum.RefundTransFee.getCode());
        } else {
            userCardLog.setTransactionType(CardTransTypeEnum.TransFee.getCode());
        }
        userCardLog.setAvailableCredit(userCard.getAmount());
        userCardLogService.save(userCardLog);
    }

    public String getFundsDirection(BigDecimal fee) {
        // Income:收入、Expenditure:支出、自己加的 Nochange不变化
        String result = "Income";
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            result = "Income";
        } else if (fee.compareTo(BigDecimal.ZERO) < 0) {
            result = "Expenditure";
        } else if (fee.compareTo(BigDecimal.ZERO) == 0) {
            result = "Nochange";
        }
        return result;
    }


    private static String postTingJson(UserCardFeeConfig cardFeeConfig) {
        Map<String, Object> personMap = new HashMap<>();
        personMap.put("PreAuthFee", cardFeeConfig.getPreAuthFee());
        personMap.put("crossBroadFeeRate", cardFeeConfig.getCrossBroadFeeRate());
        personMap.put("transactionFeeRate", cardFeeConfig.getTransactionFeeRate());
        personMap.put("refundFee", cardFeeConfig.getRefundFee());
        Gson gson = new Gson();
        return gson.toJson(personMap);
    }


    private UserWallet checkBalance(Integer businessId, CardModelEnum cardModel, BigDecimal amount) {
        UserWallet userWallet = userWalletService.getByBusinessId(businessId);
        switch (cardModel) {
            case RECHARGE -> {
                if (userWallet.getStoreWallet().compareTo(amount) < 0) {
                    Assert.fail(ResultCode.STORE_WALLET_BALANCE_NOT_ENOUGH);
                }
            }
            case SHARE -> {
                if (userWallet.getShareWallet().compareTo(amount) < 0) {
                    Assert.fail(ResultCode.SHARE_WALLET_BALANCE_NOT_ENOUGH);
                }
            }
            case PHYSICAL -> {
                if (userWallet.getPhysicalWallet().compareTo(amount) < 0) {
                    Assert.fail(ResultCode.PHYSICAL_WALLET_BALANCE_NOT_ENOUGH);
                }
            }
            default -> Assert.fail(ResultCode.SYS_ERR);
        }
        return userWallet;
    }

    public boolean savaCardProducts(CardChannelEnum channel) {
        cardFactory.getBuilder(channel);
        ArrayList<UserCardCategoryConfig> list = cardFactory.getCardProducts();
        Console.error(list);
        for (UserCardCategoryConfig userCardCategoryConfig : list) {
            UserCardCategoryConfig one = userCardCategoryConfigService.getOne(new LambdaQueryWrapper<UserCardCategoryConfig>()
                    .eq(UserCardCategoryConfig::getProductCode, userCardCategoryConfig.getProductCode())
                    .eq(UserCardCategoryConfig::getChannel, channel)
            );
            if (one == null) {
                userCardCategoryConfigService.save(userCardCategoryConfig);
            }
        }
        return true;
    }

    public JSONObject savaCardHolders(Map<String, Object> map) {
        cardFactory.getBuilder(CardChannelEnum.GSalary);
        return cardFactory.savaCardHolders(map);
    }


    @Transactional(rollbackFor = Exception.class)
    public void bindCardUser(MailingAddress dto) {
        UserCard cardId1 = userCardMapper.getOneByCardId(dto.getCardId());
        if (!"asinxPhysical".equals(cardId1.getChannel().getCode())) {
            CardHolder cardHolder = cardHolderService.getById(dto.getHolderId());
            if (cardHolder == null) {
                throw new ApiException(ResultCode.KYC_ERROR);
            }
            cardId1.setHolderId(dto.getHolderId());
            userCardMapper.updateById(cardId1);
        }
        mailingAddressService.save(dto);
//        if (cardHolder.getStatus() == 1) {
//            try {
//                ftpService.upload(cardId1, cardHolder);
//            } catch (Exception e) {
//                log.error("FTP 上传失败：", e);
//            }
//        }
        UpdateWrapper<CardApplyOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("card_id", dto.getCardId());
        updateWrapper.set("status", 0);
        cardApplyOrderService.update(updateWrapper);
        if ("asinxPhysical".equals(cardId1.getChannel().getCode())) {
            CardProductDto cardProduct = cardManageService.getAndCheckCardProductInfo(cardId1.getCardManageId(), cardId1.getUid());
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(() -> {
                UCardDto uCardDto = new UCardDto();
                uCardDto.setCardNo(cardId1.getCardNumber());
                uCardDto.setMobilePrefix("86");
                uCardDto.setPhoneNumber("150" + RandomUtil.randomInt(10000000, 99999999));
                uCardDto.setEmail(UUID.randomUUID().toString().substring(0, 6) + "@gmail.com");
                String username = RandomNameGenerator.generateRandomName();
                uCardDto.setFirstName(username.split(" ")[0]);
                uCardDto.setLastName(username.split(" ")[1]);
                uCardDto.setDateOfBirth("2000-01-01");
                uCardDto.setCountryCode("CN");
                Map<String, Object> uMap = asinxPhysical.uCardKYCAuth(uCardDto, cardProduct.getProductCode());
                cardId1.setHolderId(Integer.valueOf(uMap.get("uid").toString()));
                userCardService.updateById(cardId1);
                cardId1.setCardId(uMap.get("cardId").toString());
                userCardMapper.updateById(cardId1);
            });
            executor.shutdown();
        }
    }

    public void preOpenCard() {
        QueryWrapper<UserCardCategoryConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("channel", CardChannelEnum.GSalary.getCode());
        queryWrapper.eq("state", 1);
        List<UserCardCategoryConfig> list = userCardCategoryConfigService.list(queryWrapper);
        a:
        for (UserCardCategoryConfig obj : list) {
            List<UserCard> UserCardList = userCardService.getProductCode(obj.getProductCode());
            for (int i = 0; i < 50 - UserCardList.size(); i++) {
                ThreadUtil.sleep(200);
                String requestId = null;
                try {
                    requestId = gSalary.openCard2(obj.getProductCode(), BigDecimal.ZERO);
                } catch (Exception e) {
                    log.info("预开卡异常--卡段：{} mas：{}", obj.getProductCode(), e.getMessage());
                    continue a;
                }
                UserCard userCard = new UserCard();
                userCard.setOrderNo(requestId);
                userCard.setProductCode(obj.getProductCode());
                userCardService.save(userCard);
            }
        }
        QueryWrapper<UserCard> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.isNull("open_card_time");
        queryWrapper1.isNull("uid");
        queryWrapper1.isNull("card_id");
        queryWrapper1.isNull("card_number");
        queryWrapper1.isNotNull("order_no");
        List<UserCard> userCards = userCardMapper.selectList(queryWrapper1);
        for (UserCard userCard : userCards) {
            JSONObject data = gSalary.findOpenCardResult(userCard.getOrderNo());
            if (null == data) {
                return;
            }
            userCard.setCardId(data.getStr("cardId"));
            userCard.setCardNumber(data.getStr("cardNo"));
            userCard.setCardCurrency(data.getStr("cardCurrency"));
            userCard.setAmount(data.getBigDecimal("availableTransactionLimit"));
            userCard.setCardStatus(CardStatusEnum.Active);
            userCard.setCardCvv(data.getStr("cvv"));
            userCard.setCardExpirationMmyy(data.getStr("expirationDate"));
            userCard.setChannel(CardChannelEnum.GSalary);
            userCard.setApplyTime(new Date());
            userCardService.updateById(userCard);
        }
    }
}
