package com.qf.zpay.service;

import cn.hutool.core.lang.Console;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.usdt.ZNetU;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.UserAddress;
import generator.service.UserAddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.AccessType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @updateTime 2024/7/17 18:00
 */
@Service
@Slf4j
public class UsdtService {

    @Autowired
    ZNetU zNetU;

    @Autowired
    UserAddressService userAddressService;

    @Async
    public void genUserAddress(Integer userId) {
        UserAddress userAddress = userAddressService.getBusinessAddress(userId);
        if (userAddress == null) {
            JSONObject res = zNetU.genAddress();
            userAddress = new UserAddress();
            userAddress.setUserId(userId);
            userAddress.setCreateAt(new Date());
            userAddress.setErcAddress(res.getStr("eth_address"));
//            userAddress.setBepAddress(res.getStr("eth_address"));
            userAddress.setTrcAddress(res.getStr("trx_address"));
            userAddressService.save(userAddress);
        }
    }

    @Async
    public void withdraw(Integer coinType, String address, BigDecimal amount, String flag) {
        zNetU.withdraw(coinType, address, amount, flag);
    }


}
