package com.qf.zpay.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.service.card.channel.GSalary;
import generator.domain.UserCard;
import generator.domain.UserCardCategoryConfig;
import generator.service.UserCardCategoryConfigService;
import generator.service.UserCardManageService;
import generator.service.UserCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class GSalaryOpenCardService {
    @Autowired
    UserCardService userCardService;
    @Autowired
    GSalary gSalary;
    @Autowired
    UserCardCategoryConfigService userCardCategoryConfigService;
    @Autowired
    UserCardManageService userCardManageService;
    private static final Integer NUMBER=50;
    public void openCard(){
        QueryWrapper<UserCardCategoryConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("channel", CardChannelEnum.GSalary);
        List<UserCardCategoryConfig> list = userCardCategoryConfigService.list(queryWrapper);
        list.forEach(obj->{

            List <UserCard> UserCardList =userCardService.getProductCode(obj.getProductCode());
            for (int i = 0; i < NUMBER-UserCardList.size(); i++) {
                String requestId = gSalary.openCard2(obj.getProductCode(), BigDecimal.ZERO);
                UserCard userCard = new UserCard();
                userCard.setOrderNo(requestId);
                userCard.setAmount(BigDecimal.ZERO);
                userCard.setVirtualAmt(BigDecimal.ZERO);
                userCard.setCardModel(obj.getCardModel().equals(CardModelEnum.SHARE.getCode()) ? CardModelEnum.SHARE : obj.getCardModel().equals(CardModelEnum.PHYSICAL.getCode()) ? CardModelEnum.PHYSICAL : CardModelEnum.RECHARGE);
                userCard.setCardManageId(0);
            }
        });
    }


}
