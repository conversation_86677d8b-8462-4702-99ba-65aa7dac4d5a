package com.qf.zpay.response;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @updateTime 2024/6/2 10:28
 */
@Setter
@Getter
@Component
public class JsonResult<T> {
    /**
     * 状态码
     */
    private long code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 数据封装
     */
    private T data;

    protected JsonResult() {
    }

    protected JsonResult(long code, String message) {
        this.code = code;
        this.message = message;
    }

    protected JsonResult(long code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功返回结果
     */
    public static <T> JsonResult<T> ok() {
        return new JsonResult<T>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
    }

    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     */
    public static <T> JsonResult<T> ok(T data) {
        return new JsonResult<T>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 失败返回结果
     */
    public static <T> JsonResult<T> ko() {
        return new JsonResult<T>(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage());
    }

    /**
     * 失败返回结果
     *
     * @param errorCode 错误码
     */
    public static <T> JsonResult<T> ko(IErrorCode errorCode) {
        return new JsonResult<T>(errorCode.getCode(), errorCode.getMessage());
    }

    /**
     * 失败返回结果
     *
     * @param errorCode 错误码
     */
    public static <T> JsonResult<T> ko(IErrorCode errorCode, T data) {
        return new JsonResult<T>(errorCode.getCode(), errorCode.getMessage(), data);
    }


    /**
     * 参数验证失败返回结果
     */
    public static <T> JsonResult<T> validateFailed() {
        return ko(ResultCode.VALIDATE_FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param data 错误详情
     */
    public static <T> JsonResult<T> validateFailed(T data) {
        return ko(ResultCode.PARAM_ERR, data);
    }

    /**
     * 未登录返回结果
     */
    public static <T> JsonResult<T> unauthorized() {
        return ko(ResultCode.UNAUTHORIZED);
    }

    /**
     * 未授权返回结果
     */
    public static <T> JsonResult<T> forbidden(T data) {
        return ko(ResultCode.FORBIDDEN, data);
    }


    /**
     * 未找到资源返回结果
     */
    public static <T> JsonResult<T> notFind() {
        return ko(ResultCode.NOT_FIND);
    }


    public static <T> JsonResult<T> serverErr() {
        return ko(ResultCode.SERVER_ERR);
    }



    public static <T> JsonResult<T> sysErr() {
        return ko(ResultCode.SYS_ERR);
    }

    public static<T> JsonResult<T> serverErr2(Integer code ,String e) {
        return new JsonResult<T>(code,e);
    }
}
