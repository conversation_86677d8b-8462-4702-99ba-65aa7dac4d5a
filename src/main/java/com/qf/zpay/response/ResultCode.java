package com.qf.zpay.response;

import com.qf.zpay.util.SpringContextUtil;
import lombok.Getter;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;


/**
 * 枚举了一些常用API操作码
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@Getter
public enum ResultCode implements IErrorCode {

    /**
     * 操作码
     */
    SUCCESS(200, "操作成功"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "未登录"),
    FORBIDDEN(403, "没有相关权限"),
    NOT_FIND(404, "资源不存在"),
    SERVER_ERR(500, "服务器异常"),
    FAILED(9000, "操作失败"),
    UP_ERR(9988, "上游处理失败"),
    SYS_ERR(9999, "系统异常"),


    /**
     * 业务码
     * 1xxx 用户相关
     * 2xxx 卡片相关
     * 3xxx 支付相关
     * 4xxx
     * 9xxx 通用
     */
    USER_NOT_EXIST(1100, "用户不存在"),
    USER_EXIST(1101, "用户已存在"),
    USER_PASSWORD_ERROR(1102, "密码错误"),
    USER_LOCKED(1103, "用户已被锁定"),
    USER_DISABLED(1104, "用户已被禁用"),

    BUSINESS_LOCK(1201, "商户已被锁定"),
    BUSINESS_DISABLED(1202, "商户已被禁用"),

    BUSINESS_RSA_ERR(1301, "商户密钥对错误"),
    BIZ_CODE_EXP(1302, "CODE 已过期"),

    BUSINESS_FEE_LIMIT(1401, "商户费率不能低于平台费率"),


    /**
     * 2xxx 卡片相关
     */
    USER_CARD_ISNULL(2100, "卡片不存在"),
    CARD_MANAGE_NOT_EXIST(2200, "卡片类型不存在"),
    CARD_MANAGE_DISABLED(2201, "此卡类型已暂停开卡"),
    CARD_CHANNEL_ERROR(2202, "卡片渠道错误"),
    CARD_PRODUCT_DISABLED(2203, "商户禁用了该卡片"),
    CARD_OPEN_LIMIT(2204, "已达可开卡数量上限"),
    // 卡片操作
    CARD_STATUS_SET_JUST_FREEZE(2300, "只能进行冻结操作"),
    CARD_ACTIVE(2301, "卡片是活跃状态"),
    CARD_BLOCKED(2302, "卡片是锁定状态"),
    CARD_DISABLE(2303, "卡片已注销或过期"),
    CARD_OPEN_FAIL(2304, "开卡失败"),
    CARD_SET_FAIL(2305, "卡片设置失败"),
    TRANS_AMOUNT_LIMIT(2306, "划转金额太少"),
    CARD_RECHARGE_FAIL(2307, "卡片充值失败"),
    CARD_CANCEL_FAIL(2308, "销卡失败"),
    NO_CARDS(2309, "没有可用的卡片"),
    OPEN_CARD_ERROR(2310, "开卡失败"),
    CARD_BALANCE_NOT_ENOUGH(2311, "卡片余额不足"),

    CARD_NOT_SUPPORT(2400, "卡片不支持该操作"),
    CARD_BIN_NOT_ENABLE(2401, "卡段不支持开启"),

    /**
     * 3xxx 支付、钱包相关
     */
    MAIN_WALLET_BALANCE_NOT_ENOUGH(3001, "主账户钱包余额不足"),
    STORE_WALLET_BALANCE_NOT_ENOUGH(3002, "储值钱包余额不足"),
    SHARE_WALLET_BALANCE_NOT_ENOUGH(3003, "共享钱包余额不足"),
    PHYSICAL_WALLET_BALANCE_NOT_ENOUGH(3004, "实体卡钱包余额不足"),
    PAYMENT_WALLET_BALANCE_NOT_ENOUGH(3005, "代付钱包余额不足"),
    USDT_WALLET_BALANCE_NOT_ENOUGH(3006, "USDT钱包余额不足"),
    TOKEN_WALLET_BALANCE_NOT_ENOUGH(3007, "代币钱包余额不足"),

    USER_WALLET_TRANSFER(3101, "非主账户钱包只能划转到主账户钱包"),
    USER_WALLET_TRANSFER_ERROR(3102, "主账户钱包不能划转到主账户钱包"),
    USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE(3103, "钱包余额不足"),
    USER_WALLET_THE_WITHDRAWAL_AMOUNT_IS_TOO_SMALL(3104, "提现金额过少"),

    /**
     * 9xxx 通用
     */
    VCODE_ERR(9001, "验证码错误"),
    REFRESH_EXPIRE(9002, "刷新令牌已过期"),
    IMAGE_ERROR(9003, "certificateImage 所有图片请以.jpg结尾"),
    KYC_ERROR(9004, "KYC用户信息不存在"),

    ENCRYPT_EXP(9100, "加密串已过期"),
    UNKNOWN_NOTIFICATION_TYPE(9105, "未知通知类型"),
    USER_CALLBACK_DATA_IS_EMPTY(9106, "回调数据为空 或 X-PD-SIGN 为空"),
    FAILED_TO_INSERT_A_CONSUMPTION_RECORD(9107, "插入消费记录失败"),

    PARAM_MISS(9400, "参数缺失"),
    PARAM_ERR(9401, "参数错误"),
    PHONE_ADDRESS(9402, "请填写 姓名/电话/地址"),

    FEE_ERR(9500, "费率设置错误"),

    /**
     * 代付10001
     */
    PAYMENT_PAY_CONFIG_PARAM(10001, "请遵循ISO-3166规范的2字符地区代码"),
    PAYMENT_PAY_ENTERPRISE(10002, "收款人类型为企业，公司名称不能为空"),
    PAYMENT_PAY_PAYEE_ID(10003, "请填写收款人PayeeId"),
    DOCUMENT_ERROR(10004, "收款人识别号，11位或14位数字"),
    ORDER_NOT_NOT_EXIST(10005, "订单不存在"),
    ORDER_IS_PROCESSING(10006, "订单正在处理中"),
    ORDER_IS_ERROR(10007, "订单支付失败"),
    ORDER_IS_TIME_OUT(10007, "订单超时"),
    /**
     * 汇率19001
     */
    SERVICE_CHARGE_IS_TOO_SMALL(19001, "手续费小于平台手续费"),
    RATE_IS_TOO_SMALL(19002, "费率小于平台费率"),
    PAYMENT_USER_CONFIG_NOT_EXIST(19003, "汇率配置不存在或被禁用"),
    ;


    private final long code;
    private final String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }


    public String getMessage() {
        MessageSource messageSource = SpringContextUtil.getBean(MessageSource.class);
        String i8nMessage;
        try {
            i8nMessage = messageSource.getMessage("Code." + this.name(), null, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            i8nMessage = this.message;
        }
        return i8nMessage;
    }


}
