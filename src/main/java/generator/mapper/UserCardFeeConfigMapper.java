package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.res.v1.UserCardFeeConfigWithCardManageDTO;
import org.apache.ibatis.annotations.Param;

import generator.domain.UserCardFeeConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_fee_config(卡片费率配置表)】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserCardFeeConfig
 */
public interface UserCardFeeConfigMapper extends BaseMapper<UserCardFeeConfig> {


    /**
     * 根据商户id查询UserCardFeeConfig表
     *
     * @param businessId
     * @return
     */
    List<UserCardFeeConfig> getAllByBusinessId(@Param("businessId") Integer businessId);

    /**
     * 根据商户id和卡片产品id查询UserCardFeeConfig表，根据cardManageId查询UserCardManage表的多表联查，根据dto返回全部字段
     * 获取商户的费率
     *
     * @param businessId
     * @param cardManageId
     * @return
     */
    UserCardFeeConfigWithCardManageDTO getRateByBusinessIdAndCardManageId(@Param("businessId") Integer businessId, @Param("cardManageId") Integer cardManageId);

    /**
     * 根据businessId查询UserCardFeeConfig表获得cardManageId，根据cardManageId查询UserCardManage表的多表联查，根据dto返回全部字段
     *
     * @param businessId
     * @return
     */
    List<UserCardFeeConfigWithCardManageDTO> getUsableCardProductListByBusinessId(@Param("businessId") Integer businessId);

    UserCardFeeConfig getByBusinessIdAndCardManageId(@Param("businessId") Integer businessId, @Param("cardManageId") Integer cardManageId);

    IPage<UserCardFeeConfigDto> inquireNumberOfAvailableCards(Page<UserCardFeeConfigDto> page, CardSettingsDto cardSettingsDto);

    List<CardsSlotDto> listOfCardsSlot(@Param("businessId")Integer businessId, @Param("cardModel")String cardModel);

    List<UserCardFeeConfigDto> numberOfAvailableCards(Integer businessId);

    IPage<UserCardFeeConfigVo> segmentSettingsList(Page<UserCardFeeConfigVo> objectPage, CardSettingsDto cardSettingsDto);

    List<UserCardFeeConfigDto> numberOfAvailableCards2(Integer businessId);

    List<UserCardFeeConfig> bankCard(String cardModel, String cardBelongTo, Integer businessId);
}




