package generator.mapper;

import org.apache.ibatis.annotations.Param;

import generator.domain.UserDevConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_dev_config】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserDevConfig
 */
public interface UserDevConfigMapper extends BaseMapper<UserDevConfig> {

    /**
     * 根据userId获取用户开发者配置
     *
     * @param userId userId
     * @return UserDevConfig
     */
    UserDevConfig getOneByUserId(@Param("userId") Integer userId);


    /**
     * 根据商户Id获取用户开发者配置
     *
     * @param businessId businessId
     * @return UserDevConfig
     */
    UserDevConfig getOneByBusinessId(@Param("businessId") Integer businessId);

    /**
     * 根据appId获取用户开发者配置
     *
     * @param appId appId
     * @return UserDevConfig
     */
    UserDevConfig getOneByAppId(@Param("appId") String appId);
}




