package generator.mapper;
import org.apache.ibatis.annotations.Param;

import generator.domain.UserWallet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【tb_user_wallet】的数据库操作Mapper
* @createDate 2024-06-18 14:32:59
* @Entity generator.domain.UserWallet
*/
public interface UserWalletMapper extends BaseMapper<UserWallet> {

    UserWallet getOneById(@Param("id") Integer id);

    UserWallet getOneByUserId(@Param("userId") Integer userId);

    UserWallet getOneByBusinessId(@Param("businessId") Integer businessId);
}




