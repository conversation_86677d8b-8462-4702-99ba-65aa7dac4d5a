package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CoinInOut;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import generator.domain.UserMoneyLog;

/**
* <AUTHOR>
* @description 针对表【tb_coin_in_out】的数据库操作Mapper
* @createDate 2024-07-19 14:59:27
* @Entity generator.domain.CoinInOut
*/
public interface CoinInOutMapper extends BaseMapper<CoinInOut> {

    IPage<CoinInOut> getUserMoneyLogList(Page<CoinInOut> objectPage, CardSettingsDto cardSettingsDto);
}




