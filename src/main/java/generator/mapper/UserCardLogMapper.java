package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardTransactionStatementDto;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserCardLogDto;
import generator.domain.UserCardLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_log】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserCardLog
 */
public interface UserCardLogMapper extends BaseMapper<UserCardLog> {


    BigDecimal queryCardAmount(@Param("formattedDate") String formattedDate, @Param("noticeType") String noticeType, @Param("businessId") Integer businessId, @Param("status") String status);

    IPage<UserCardLogDto> getCardTransactionDetails(Page<UserCardLogDto> page, CardSettingsDto cardSettingsDto);

    IPage<UserCardLogDto> getShareCardsDetails(Page<UserCardLogDto> page, CardSettingsDto cardSettingsDto);

    List<UserCardLog> getOrderIdDetails(String orderId);

    List<CardTransactionStatementDto> export(@Param("cardSettingsDto") CardSettingsDto cardSettingsDto);

    List<CardTransactionStatementDto> exportEn(@Param("cardSettingsDto") CardSettingsDto cardSettingsDto);
}




