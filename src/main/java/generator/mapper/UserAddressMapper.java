package generator.mapper;

import generator.domain.UserAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_address】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserAddress
 */
public interface UserAddressMapper extends BaseMapper<UserAddress> {

    UserAddress getOneByUserId(@Param("userId") Integer userId);
}




