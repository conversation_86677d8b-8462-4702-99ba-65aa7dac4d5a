package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.PayOrderDto;
import com.qf.zpay.dto.req.PaymentPayOrderDto;
import generator.domain.PaymentPayOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface PaymentPayOrderMapper extends BaseMapper<PaymentPayOrder> {
    IPage<PaymentPayOrderDto> payeePage(Page<PaymentPayOrderDto> page, PayOrderDto payOrderDto);

    List<PaymentPayOrderDto> export(@Param("payOrderDto") PayOrderDto payOrderDto);

    List<PaymentPayOrder> selectListByStatus();

    PaymentPayOrder selectByOrderId(String orderId);


    List<PaymentPayOrderDto> exportEn(@Param("payOrderDto") PayOrderDto payOrderDto);
    // 在这里定义一些特定的数据访问方法
}