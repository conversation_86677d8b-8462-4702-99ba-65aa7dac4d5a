package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserCardFeeConfigDto;
import generator.domain.Message;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【tb_message】的数据库操作Mapper
* @createDate 2024-06-05 10:16:39
* @Entity generator.domain.Message
*/
public interface MessageMapper extends BaseMapper<Message> {

    IPage<Message> getAnnouncementList(Page<UserCardFeeConfigDto> page, CardSettingsDto cardSettingsDto);
}




