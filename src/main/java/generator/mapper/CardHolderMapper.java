package generator.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardHolderRequestDto;
import generator.domain.CardHolder;

/**
* <AUTHOR>
* @description 针对表【tb_user_data】的数据库操作Mapper
* @createDate 2024-07-09 09:11:54
* @Entity generator.domain.CardHolder
*/
public interface CardHolderMapper extends BaseMapper<CardHolder> {


    IPage<CardHolder> pageList(Page<CardHolder> objectPage, CardHolderRequestDto dto);

    void updateStatus(Integer id, Integer i);
}
