package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.NumberOfTransactionsDto;
import generator.domain.UserWalletLog;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_wallet_log】的数据库操作Mapper
 * @createDate 2024-06-18 14:32:59
 * @Entity generator.domain.UserWalletLog
 */
public interface UserWalletLogMapper extends BaseMapper<UserWalletLog> {

    IPage<UserWalletLog> userWalletTransferDetails(Page<UserWalletLog> page, CardSettingsDto cardSettingsDto);


    List<UserWalletLog> userWalletTransferDetailsAll(CardSettingsDto cardSettingsDto);

    List<UserWalletLog> changeOfFunds(Integer businessId);

    List<NumberOfTransactionsDto> numberOfTransactions(Integer businessId);

    Integer notInCardIds(@Param("businessId") Integer businessId, @Param("cardIds") List<String> cardIds);

    List<UserWalletLog> findByWalletTypes(@Param("missingWalletTypes") List<String> walletTypes, @Param("businessId") Integer businessId, @Param("date") String createTime);

    BigDecimal queryCardAmount(String formattedDate, String walletType, Integer businessId, Integer code);

    BigDecimal queryCardMoneyAmount(String formattedDate, String walletType, Integer businessId, List<Integer> objects);

    BigDecimal queryCardMoneyAmount2(String walletType, Integer businessId, List<Integer> objects);

    BigDecimal queryCardAmount3(String walletType, Integer businessId, Integer code);


    UserWalletLog findChangeMoneyById(@Param("businessId") Integer businessId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("walletType") String walletType, @Param("code") String code);
}




