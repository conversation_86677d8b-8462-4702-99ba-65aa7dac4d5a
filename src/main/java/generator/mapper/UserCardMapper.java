package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.ListOfCardsDto;
import generator.domain.UserCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserCard
 */
public interface UserCardMapper extends BaseMapper<UserCard> {

    UserCard getOneById(@Param("id") Integer id);

    UserCard getOneByCardId(@Param("cardId") String cardId);

    UserCard getOneByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 根据卡id查询用user_card表
     *
     * @param cardId
     * @return
     */
    UserCard selectByCardId(@Param("cardId") String cardId);


    IPage<ListOfCardsDto> getListOfCards(Page<ListOfCardsDto> page, @Param("cardSettingsDto") CardSettingsDto cardSettingsDto);

    List<UserCard> getListOfCardsAll(@Param("cardSettingsDto") CardSettingsDto cardSettingsDto);


    List<Integer> getUserCardlist(String cardDuan, Integer businessId);

    /**
     * 商户共享卡列表
     *
     * @param businessId
     * @return List<UserCard>
     */
    List<UserCard> getUserShareCardList(@Param("businessId") Integer businessId);


    void updateStatus(String cardNumber);
}




