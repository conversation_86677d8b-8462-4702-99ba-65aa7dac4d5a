package generator.mapper;

import generator.domain.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user】的数据库操作Mapper
 * @createDate 2024-06-03 10:50:58
 * @Entity generator.domain.User
 */
public interface UserMapper extends BaseMapper<User> {


    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @return User 用户信息
     */
    User getOneByEmail(@Param("email") String email);

    List<User> selectAll();

    /**
     * 根据ID获取用户信息
     *
     * @param id ID
     * @return User 用户信息
     */
    User getOneById(@Param("id") Integer id);

    User getOneByBusinessId(@Param("businessId") Integer businessId);
}




