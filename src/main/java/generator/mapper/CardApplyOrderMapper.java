package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CardApplyOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【tb_card_apply_order】的数据库操作Mapper
* @createDate 2024-07-08 15:34:33
* @Entity generator.domain.CardApplyOrder
*/
public interface CardApplyOrderMapper extends BaseMapper<CardApplyOrder> {

    IPage<CardApplyOrder> cardApplyOrderList(Page<CardApplyOrder> objectPage, CardSettingsDto cardSettingsDto);
}




