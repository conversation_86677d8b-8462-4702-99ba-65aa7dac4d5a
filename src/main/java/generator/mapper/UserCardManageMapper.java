package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import generator.domain.UserCardManage;
import generator.vo.CardManageWithFeeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_manage】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:58
 * @Entity generator.domain.UserCardManage
 */
public interface UserCardManageMapper extends BaseMapper<UserCardManage> {

    List<CardManageWithFeeVo> getBusinessCardManageWithFee(Integer businessId);

    CardManageWithFeeVo getOneBusinessCardManageWithFee(Integer businessId, Integer cardManageId);

    Integer getBusinessCardManageOpenCards(Integer businessId, Integer cardManageId);

    List<Integer> getIdsByProductCodeList(List<String> productCodeList);

}




