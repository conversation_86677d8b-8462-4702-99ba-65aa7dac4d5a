package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qf.zpay.dto.res.v2.AdvanceFlowDto;
import com.qf.zpay.dto.res.v2.JournalAccountDto;
import generator.domain.UserWalletAdvanceLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserWalletAdvanceLog(卡片操作日志)】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:39
 * @Entity generator.domain.CardActionLog
 */
public interface UserWalletAdvanceLogMapper extends BaseMapper<UserWalletAdvanceLog> {

    AdvanceFlowDto advanceFlowStatistics(Integer businessId);

    List<UserWalletAdvanceLog> advancePage(@Param("dto") JournalAccountDto dto, @Param("businessId") Integer businessId);
}




