package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.BusinessDayFeeLog;
import generator.domain.BusinessDayFundLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusinessDayFeeLogMapper extends BaseMapper<BusinessDayFeeLog> {
    BusinessDayFeeLog queryBusinessIdAndCreateDate(@Param("businessId") Integer businessId, @Param("formattedDate") String formattedDate);

    IPage<BusinessDayFeeLog> getFlowDetails(Page<BusinessDayFeeLog> page, CardSettingsDto cardSettingsDto);

    List<BusinessDayFeeLog> flowOfFunds(Integer businessId);
}
