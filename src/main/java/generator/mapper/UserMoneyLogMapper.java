package generator.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.UserMoneyLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_user_money_log】的数据库操作Mapper
* @createDate 2024-06-05 10:16:58
* @Entity generator.domain.UserMoneyLog
*/
public interface UserMoneyLogMapper extends BaseMapper<UserMoneyLog> {

    BigDecimal queryCardMoneyAmount(@Param("formattedDate")String formattedDate,@Param("businessId") Integer businessId,@Param("subType") Integer subType);

    BigDecimal getDayChargeTotal(@Param("formattedDate")String formattedDate, @Param("businessId")Integer businessId, @Param("subTypes")List<Integer> subTypes);

    BigDecimal getDayMoneyAmount(@Param("businessId")Integer businessId, @Param("subTypes")List<Integer> subTypes);

    BigDecimal queryCardMoneyAmountSum(@Param("businessId")Integer businessId, @Param("subType")Integer subType);

    IPage<UserMoneyLog> getUserMoneyLogList(Page<UserMoneyLog> objectPage, CardSettingsDto cardSettingsDto);
}




