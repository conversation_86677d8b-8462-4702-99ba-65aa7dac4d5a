package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.CardActionLogDto;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CardActionLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_action_log(卡片操作日志)】的数据库操作Mapper
 * @createDate 2024-06-05 10:16:39
 * @Entity generator.domain.CardActionLog
 */
public interface CardActionLogMapper extends BaseMapper<CardActionLog> {

    IPage<CardActionLogDto> getCardHistory(Page<CardActionLogDto> page, CardSettingsDto cardSettingsDto);

    List<CardActionLogDto> exportCardHistory(@Param("cardSettingsDto") CardSettingsDto cardSettingsDto);

    List<CardActionLogDto> exportCardHistoryEn(@Param("cardSettingsDto") CardSettingsDto cardSettingsDto);

    List<CardActionLog> findListByModel(@Param("businessId") Integer businessId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("model") String model);

    String findTotalRevenue(@Param("businessId") Integer businessId, @Param("endTime") String endTime, @Param("model") String model);

    String findTotalRevenueAll(@Param("businessId") Integer businessId);

    List<CardActionLog> findExpendByModel(@Param("businessId") Integer businessId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("model") String model);

    CardActionLog findExpendAll(@Param("businessId") Integer businessId);

    List<CardActionLog> findExpendAllByType(@Param("businessId") Integer businessId, @Param("type") Integer type);
}




