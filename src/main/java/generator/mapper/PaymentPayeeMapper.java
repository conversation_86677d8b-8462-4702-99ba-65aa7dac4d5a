package generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qf.zpay.dto.req.PaymentPayeeVO;
import com.qf.zpay.dto.req.v2.PayeeDto;
import com.qf.zpay.dto.req.v2.queryPayeeDto;
import generator.domain.PaymentPayee;

import java.util.List;


/**
 * 支付收款人信息 Mapper 接口
 */
public interface PaymentPayeeMapper extends BaseMapper<PaymentPayee> {
    List<PaymentPayeeVO> getPayee(Integer businessId, String lastName, String phone, String accountType);

    PaymentPayee selectByPayeeId(String payeeId);

    IPage<PayeeDto> queryPayee(Page<PayeeDto> page, queryPayeeDto dto);

    PayeeDto queryPayeeByPayeeId(String payeeId);


    // 在这里定义一些特定的数据访问方法
}