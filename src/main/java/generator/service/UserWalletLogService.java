package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.NumberOfTransactionsDto;
import generator.domain.User;
import generator.domain.UserWalletLog;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_wallet_log】的数据库操作Service
 * @createDate 2024-06-18 14:32:59
 */
public interface UserWalletLogService extends IService<UserWalletLog> {

    BigDecimal queryCardMoneyAmount2(String o, Integer businessId, List<Integer> subTypes);

    /**
     * 用户钱包划转详情
     *
     * @param cardSettingsDto 筛选条件
     * @return 用户钱包划转详情分页数据
     */
    IPage<UserWalletLog> userWalletTransferDetails(CardSettingsDto cardSettingsDto);


    /**
     * 卡片交易详情导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    void exportUserWalletTransferDetails(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException;


    /**
     * 仪表盘1
     * 资金变化
     *
     * @param user2 商户
     * @return List
     * @throws ParseException；
     */
    List<Map.Entry<String, List<UserWalletLog>>> changeOfFunds(User user2) throws ParseException;

    /**
     * 交易笔数仪表盘
     *
     * @param user2 商户
     * @return HashMap
     */
    HashMap<Integer, List<NumberOfTransactionsDto>> numberOfTransactions(User user2);
}
