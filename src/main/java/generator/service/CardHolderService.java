package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardHolderRequestDto;
import generator.domain.User;
import generator.domain.CardHolder;


/**
 * <AUTHOR>
 * @description 针对表【tb_user_data】的数据库操作Service
 * @createDate 2024-07-09 09:11:54
 */
public interface CardHolderService extends IService<CardHolder> {

    /**
     * 用户kyc
     *
     * @param userData userData 用户kyc数据
     * @param user     商户
     * @return 用户kyc信息
     */
    CardHolder add(CardHolder userData, User user);

    /**
     * 用卡人列表
     *
     * @param dto 筛选条件
     * @return 用卡人分页列表
     */
    IPage<CardHolder> pageList(CardHolderRequestDto dto);

    void updateStatus(Integer integer, Integer i);
}
