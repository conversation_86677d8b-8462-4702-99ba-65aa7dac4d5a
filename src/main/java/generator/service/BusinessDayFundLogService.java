package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.BusinessDayFeeLog;
import generator.domain.BusinessDayFundLog;

import java.util.List;

public interface BusinessDayFundLogService extends IService<BusinessDayFundLog> {


    void addExpenditureLog();

    IPage<BusinessDayFundLog> getExpenditureTurnover(CardSettingsDto cardSettingsDto);

}
