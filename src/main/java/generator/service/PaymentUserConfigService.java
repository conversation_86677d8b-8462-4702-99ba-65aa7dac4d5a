package generator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import generator.domain.PaymentUserConfig;

public interface PaymentUserConfigService extends IService<PaymentUserConfig> {

    /**
     * 修改商户汇率
     *
     * @param dto
     * @return
     */
    PaymentUserConfig updateUserConfig(PaymentUserConfig dto);

    /**
     * 初始化渠道
     *
     * @param id 商户id
     */
    void initChannel(Integer id);
}