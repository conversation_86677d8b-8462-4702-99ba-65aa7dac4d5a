package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.res.v1.UserCardFeeConfigWithCardManageDTO;
import generator.domain.UserCardFeeConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_fee_config(卡片费率配置表)】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserCardFeeConfigService extends IService<UserCardFeeConfig> {

    /**
     * 根据商户ID获取可用的卡片产品列表
     *
     * @param businessId
     * @return
     */
    List<UserCardFeeConfigWithCardManageDTO> ableCardProductList(String businessId);


    /**
     * 根据商户ID和卡片产品ID获取商户的费率
     *
     * @param businessId
     * @param cardManageId
     * @return
     */
    UserCardFeeConfigWithCardManageDTO getRate(String businessId, String cardManageId);

    /**
     * 可用卡数量接口
     *
     * @param cardSettingsDto 筛选条件
     * @return userCardFeeConfigDtoIPage 可用卡数量详情
     */
    IPage<UserCardFeeConfigDto> inquireNumberOfAvailableCards(CardSettingsDto cardSettingsDto);

    /**
     * 卡段列表
     *
     * @param cardModel 卡段模式
     * @return 卡段列表分页数据
     */
    List<CardsSlotDto> listOfCardsSlot(Integer businessId, String cardModel);

    UserCardFeeConfig getAndCheckCardFeeConfig(Integer cardManageId, Integer businessId);

    UserCardFeeConfig getCardFeeConfig(Integer cardManageId, Integer businessId);

    /**
     * 剩余可用卡数量
     *
     * @param businessId 商户id
     * @return list
     */
    List<UserCardFeeConfigDto> numberOfAvailableCards(Integer businessId);

    /**
     * 可用卡段列表
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    IPage<UserCardFeeConfigDetailVo> segmentSettingsList(CardSettingsDto cardSettingsDto);

    /**
     * 获取卡配置，卡段开卡数
     *
     * @param businessId 商户id
     * @return 配置DTO
     */
    List<UserCardFeeConfigDto> numberOfAvailableCards2(Integer businessId);

    /**
     * @param cardModel    卡模式
     * @param cardBelongTo 卡归属
     * @return 开卡列表
     */
    List<UserCardFeeConfig> bankCard(String cardModel, String cardBelongTo, Integer businessId);

    /**
     * 编辑商户卡段费率
     *
     * @param businessId 商户id
     * @return
     */
    void updateBusinessRate(BusinessRateDto businessRateDto, Integer businessId);
}
