package generator.service;

import com.qf.zpay.dto.req.Code2TokenDto;
import com.qf.zpay.dto.req.SigninDto;
import com.qf.zpay.dto.req.UserInfoDto;
import com.qf.zpay.dto.req.UserPasswordDto;
import com.qf.zpay.response.JsonResult;
import generator.domain.User;
import com.baomidou.mybatisplus.extension.service.IService;
import generator.domain.UserWallet;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user】的数据库操作Service
 * @createDate 2024-06-03 10:50:58
 */
public interface UserService extends IService<User> {

    /**
     * 商户注册
     *
     * @param signinDto
     * @return
     */
    JsonResult signinByEmail(@Valid SigninDto signinDto);

    /**
     * 商户登录
     *
     * @param email    邮箱
     * @param password 密码
     * @param ip       IP
     * @return User 用户信息
     */
    User loginByEmail(String email, String password, String ip);


    User code2user(Code2TokenDto code2TokenDto);


    User getUserByBusinessId(Integer businessId);


    List<User> queryAllUser();

    /**
     * 验证二级密码
     *
     * @param id       用户id
     * @param password 二级密码
     * @return true or false
     */
    Boolean verify(Integer id, String password);

    /**
     * 修改密码
     *
     * @param user2           商户
     * @param userPasswordDto 密码数据dto
     * @return true
     */
    Boolean changePassword(User user2, UserPasswordDto userPasswordDto);

    /**
     * 修改二级密码
     *
     * @param user2           商户
     * @param userPasswordDto 密码数据dto
     * @return true
     */
    Boolean changeSecondaryPassword(User user2, UserPasswordDto userPasswordDto);

    /**
     * 获取商户钱包
     *
     * @param businessId 商户ID
     * @return UserWallet 商户钱包
     */
    UserWallet getWallet(Integer businessId);

    /**
     * 商户详情接口
     *
     * @param user2 商户
     * @return 商户详情
     */
    UserInfoDto userInfo(User user2);

    /**
     * 下载公钥
     *
     * @param user2    商户
     * @param response 响应数据
     */
    void writePemFile(User user2, HttpServletResponse response);


    /**
     * 重置密码
     *
     * @param signinDto
     * @return
     */
    JsonResult<Object> forgotPassword(@Valid SigninDto signinDto);
}
