package generator.service;

import generator.domain.UserDevConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_dev_config】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserDevConfigService extends IService<UserDevConfig> {

    /**
     * 生成用户开发者配置
     *
     * @param userId userId
     */
    @Async
    void genUserDevConfig(Integer userId);

}
