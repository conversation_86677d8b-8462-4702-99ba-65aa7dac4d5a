package generator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.PaymentPayeeSaveDto;
import com.qf.zpay.dto.req.PaymentPayeeVO;
import com.qf.zpay.dto.req.v2.PayeeDto;
import generator.domain.PaymentPayee;
import generator.domain.User;

import java.util.List;

/**
 * 支付收款人信息 Service 接口
 */
public interface PaymentPayeeService extends IService<PaymentPayee> {
    /**
     * 删除收款人
     *
     * @param id 收款人id
     */
    void deletePayee(Integer id);

    /**
     * 查询收款人
     *
     * @param lastName 姓
     * @return 收款人列表
     */
    List<PaymentPayeeVO> getPayee(Integer businessId, String lastName, String phone, String accountType);

    /**
     * 查询收款人
     *
     * @param payeeId 收款人payeeId
     * @return 收款人信息
     */
    PayeeDto queryPayeeByPayeeId(String payeeId);

    PaymentPayee selectByPayeeId(String payeeId);

    PaymentPayee updatePayeeV2(PaymentPayeeSaveDto dto);

    PaymentPayee getByAccountType(String paymentMethod, User user, String accountNo);

    PaymentPayee getPayeeByFirstNameAndLastName(String firstName, String lastName);
}