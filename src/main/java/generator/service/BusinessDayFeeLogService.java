package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.BusinessDayFeeLog;
import generator.domain.BusinessDayFundLog;

import java.util.List;

public interface BusinessDayFeeLogService extends IService<BusinessDayFeeLog> {
    /**
     * 用户今日资金流水统计定时任务接口
     */
    void addFundLog();

    IPage<BusinessDayFeeLog> getFlowDetails(CardSettingsDto cardSettingsDto);


    List<BusinessDayFeeLog> flowOfFunds(Integer businessId);
}
