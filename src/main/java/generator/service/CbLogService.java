package generator.service;

import cn.hutool.json.JSONObject;
import generator.domain.CbLog;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 针对表【tb_cb_log(回调记录)】的数据库操作Service
 * @createDate 2024-06-05 10:16:39
 */
public interface CbLogService extends IService<CbLog> {

    JSONObject photon(HttpServletRequest request, String body) throws IOException;

    void paymentPayOrder(HttpServletRequest request, String body);


    void asinx(HttpServletRequest request, HttpServletResponse response);
}
