package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserWalletTransferDto;
import com.qf.zpay.dto.req.WithdrawDto;
import generator.domain.CardTransFeeLog;
import generator.domain.CoinInOut;
import generator.domain.UserWallet;
import generator.domain.UserWalletAdvanceLog;
import jakarta.servlet.http.HttpServletRequest;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_wallet】的数据库操作Service
 * @createDate 2024-06-18 14:32:59
 */
public interface UserWalletService extends IService<UserWallet> {

    /**
     * 用户钱包详情
     *
     * @param businessId 商户id
     * @return UserWallet钱包详情
     */
    UserWallet getByBusinessId(Integer businessId);

    /**
     * 用户划转入口
     *
     * @param userWalletTransferDto userWalletTransferDto 划转请求数据dto
     * @param request               获取请求对象
     */
    void userWalletTransfer(UserWalletTransferDto userWalletTransferDto, HttpServletRequest request);

    /**
     * 初始化用户钱包 用户兼容老用户
     *
     * @param userId 用户id
     */
    void initWallet(Integer userId);

    /**
     * 用户提现
     *
     * @param withdrawDto 提现数据dto
     */
    void withdraw(WithdrawDto withdrawDto);

    /**
     * 充提记录
     *
     * @param cardSettingsDto 筛选条件
     * @return 充提记录分页数据
     */
    IPage<CoinInOut> getCardTransactionDetails(CardSettingsDto cardSettingsDto);

    /**
     * 金额变动
     *
     * @param businessId    商户id
     * @param amount        金额
     * @param cardModelEnum 卡片类型
     * @param action        操作类型
     */
    void changeAmount(Integer businessId, BigDecimal amount, CardModelEnum cardModelEnum, WalletTypeEnum action, UserWalletAdvanceLog advanceLog, CardTransFeeLog cardTransFee);

}
