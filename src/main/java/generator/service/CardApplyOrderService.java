package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CardApplyOrder;
import generator.domain.UserCard;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_apply_order】的数据库操作Service
 * @createDate 2024-07-08 15:34:33
 */
public interface CardApplyOrderService extends IService<CardApplyOrder> {

    /**
     * 卡片操作申请数据
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    IPage<CardApplyOrder> cardApplyOrderList(CardSettingsDto cardSettingsDto);

    void updateStatus(UserCard oneByCardNumber, Integer status);

    CardApplyOrder getOpenCardOneByCardNumber(String cardNumber);

    CardApplyOrder getOneByCardNumber(String cardNumber, Integer action);

}
