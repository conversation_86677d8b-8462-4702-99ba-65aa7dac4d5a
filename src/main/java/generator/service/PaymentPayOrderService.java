package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.PayOrderDto;
import com.qf.zpay.dto.req.PaymentPayOrderDto;
import com.qf.zpay.dto.res.v2.PaymentFlowDto;
import generator.domain.PaymentPayOrder;
import generator.domain.User;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Locale;


public interface PaymentPayOrderService extends IService<PaymentPayOrder> {

    /**
     * 订单分页
     *
     * @param payOrderDto 筛选条件
     * @return 订单分页数据
     */
    IPage<PaymentPayOrderDto> payeePage(PayOrderDto payOrderDto);

    /**
     * 订单导出
     *
     * @param payOrderDto 筛选条件
     * @param user2       商户
     * @param response    响应
     * @param locale      语言环境
     * @throws IOException ；
     */
    void export(PayOrderDto payOrderDto, User user2, HttpServletResponse response, Locale locale) throws IOException;

    /**
     * 退款接口
     *
     * @param order 订单
     */
    void refund(PaymentPayOrder order);

    PaymentPayOrder getByOrderNo(String orderNo);

    /**
     * 代付流水统计
     */
    void paymentPayOrderStatistics();


    PaymentFlowDto paymentFlowStatistics(Integer businessId);
}