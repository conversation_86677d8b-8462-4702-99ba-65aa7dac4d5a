package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.res.v2.AdvanceFlowDto;
import com.qf.zpay.dto.res.v2.JournalAccountDto;
import generator.domain.UserWalletAdvanceLog;

/**
 * <AUTHOR>
 * @description 针对表【UserWalletAdvanceLog(卡片操作日志)】的数据库操作Service
 * @createDate 2024-06-05 10:16:39
 */
public interface UserWalletAdvanceLogService extends IService<UserWalletAdvanceLog> {


    /**
     * 垫付流水统计
     *
     * @param businessId
     * @return
     */
    AdvanceFlowDto advanceFlowStatistics(Integer businessId);

    IPage<UserWalletAdvanceLog> advancePage(JournalAccountDto dto, Integer businessId);
}
