package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.ListOfCardsDetailDto;
import com.qf.zpay.dto.res.v1.UserCardAndFeeConfigAndManageDTO;
import generator.domain.UserCard;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserCardService extends IService<UserCard> {

    UserCardAndFeeConfigAndManageDTO getMemberCardDetail(String businessId, String cardId);

    /**
     * 卡片管理
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    IPage<ListOfCardsDetailDto> getListOfCards(CardSettingsDto cardSettingsDto);

    /**
     * 修改卡片昵称
     *
     * @param cardId   卡id
     * @param cardName 卡片昵称
     */
    void updateCardId(String cardId, String cardName);

    /**
     * id获取卡片
     *
     * @param cardId 卡id
     */
    UserCard getByCardId(String cardId);

    /**
     * 卡片禁用or启用接口
     *
     * @param cardManageId 卡片管理配置id
     * @param businessId   商户id
     */
    void setCardEnabledOrDisabled(Integer cardManageId, Integer businessId);


    List<Integer> getUserCardlist(String cardDuan, Integer businessId);

    /**
     * 卡片详情
     *
     * @param cardId 卡片id
     * @return 卡片数据详情
     */
    ListOfCardsDetailDto getByMemberCardDetail(Integer businessId, String cardId);

    /**
     * 商户共享卡列表
     *
     * @param businessId 商户id
     * @return List<UserCard>
     */
    List<UserCard> getUserShareCardList(@Param("businessId") Integer businessId);

    /**
     * 获取一张指定产品空卡
     *
     * @param productCode 产品编码
     * @return UserCard
     */
    UserCard getOneEmptyCard(String productCode);


    /**
     * 获取一张指定产品空卡
     *
     * @param productCode 产品编码
     * @param cardScheme  卡组织
     * @return UserCard
     */
    UserCard getOneEmptyCard(String productCode, CardSchemeEnum cardScheme);


    /**
     * 获取一张指定产品空卡
     *
     * @param productCode  产品编码
     * @param cardManageId 卡组织
     * @return UserCard
     */
    UserCard getOneEmptyCard(String productCode, Integer cardManageId);


    Long totalCardBought(Integer businessId, Integer cardManageId);

    /**
     * 获取一张商户集采空卡
     *
     * @param businessId   产品编码
     * @param cardManageId 卡组织
     * @return UserCard
     */
    UserCard getOneEmptyCardBought(Integer businessId, Integer cardManageId);


    /**
     * 商户卡片分页
     *
     * @param businessId 商户id
     * @param cardModel  卡片模式
     * @param cardScheme 卡组织
     * @param cardStatus 卡状态
     * @param page       页码
     * @param size       每页数量
     * @return IPage<UserCard>
     */
    IPage<UserCard> pageCardList(
            Integer businessId,
            String cardModel,
            String cardScheme,
            String cardStatus,
            Integer page,
            Integer size
    );


    UserCard getByCardNumber(String pan);

    List<UserCard> getProductCode(String productCode);

    UserCard getByCardNumberLike(String cardEnd);

    UserCard getByCardNumberLike(String carBin, String cardEnd);

    /**
     * 卡片导出
     *
     * @param cardSettingsDto
     * @param response
     * @param request
     * @throws IOException
     */
    void exportListOfCards(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException;


    String getUpUid(String cardId);

}
