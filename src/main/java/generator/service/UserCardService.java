package generator.service;


import com.mybatisflex.core.service.IService;
import com.zpay.admin.job.dto.StatCardNumDto;
import generator.domain.UserCard;

import java.util.List;

/**
 * 服务层。
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface UserCardService extends IService<UserCard> {

    UserCard getOneByCardId(String cardId);

    UserCard getOneByOrderNo(String cardId);

    List<UserCard> getCardIdByCardNo(String cardNo);


    StatCardNumDto statCardHistory(Integer businessId);

    StatCardNumDto statCardPlatformHistory();

}