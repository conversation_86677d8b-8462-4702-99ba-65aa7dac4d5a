package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PlatformDayEarnLogService;
import generator.domain.PlatformDayEarnLog;
import generator.mapper.PlatformDayEarnLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PlatformDayEarnLogServiceImpl extends ServiceImpl<PlatformDayEarnLogMapper, PlatformDayEarnLog> implements PlatformDayEarnLogService {

}