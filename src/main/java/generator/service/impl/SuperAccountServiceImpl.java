package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.SuperAccount;
import generator.domain.User;
import generator.mapper.UserMapper;
import generator.service.SuperAccountService;
import generator.mapper.SuperAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_super_account】的数据库操作Service实现
 * @createDate 2024-07-16 11:32:10
 */
@Service
public class SuperAccountServiceImpl extends ServiceImpl<SuperAccountMapper, SuperAccount>
        implements SuperAccountService {

    @Autowired
    SuperAccountMapper superAccountMapper;

    @Autowired
    UserMapper userMapper;

    public User login(String email, String pwd, Integer code) {
//        QueryWrapper<SuperAccount> query = new QueryWrapper<>();
////        superAccountMapper.
        return userMapper.getOneByBusinessId(12);
    }

}




