package generator.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.PayOrderStatusEnum;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.PayOrderDto;
import com.qf.zpay.dto.req.PaymentPayOrderDto;
import com.qf.zpay.dto.res.v2.PaymentFlowDto;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.*;
import generator.mapper.PaymentPayOrderMapper;
import generator.mapper.UserMapper;
import generator.mapper.UserWalletLogMapper;
import generator.mapper.UserWalletMapper;
import generator.service.BusinessDayPaymentFlowLogService;
import generator.service.PaymentPayOrderService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Log4j2
@Service
public class PaymentPayOrderServiceImpl extends ServiceImpl<PaymentPayOrderMapper, PaymentPayOrder> implements PaymentPayOrderService {

    @Autowired
    private PaymentPayOrderMapper mapper;
    @Autowired
    private UserWalletMapper userWalletMapper;
    @Autowired
    private UserWalletLogMapper userWalletLogMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private BusinessDayPaymentFlowLogService businessDayPaymentFlowLogService;

    /**
     * 订单分页
     *
     * @param payOrderDto 筛选条件
     * @return 订单分页数据
     */
    @Override
    public IPage<PaymentPayOrderDto> payeePage(PayOrderDto payOrderDto) {
        Page<PaymentPayOrderDto> page = new Page<>(payOrderDto.getPage(), payOrderDto.getPageSize());
        return mapper.payeePage(page, payOrderDto);
    }

    /**
     * 订单导出
     *
     * @param payOrderDto 筛选条件
     * @param user2       商户
     * @param response    响应
     * @param locale      语言环境
     * @throws IOException ；
     */
    @Override
    public void export(PayOrderDto payOrderDto, User user2, HttpServletResponse response, Locale locale) throws IOException {
        // 获取当前的语言环境
        Locale currentLocale = LocaleContextHolder.getLocale();
        List<PaymentPayOrderDto> list;
        // 判断语言环境
        if (currentLocale.getLanguage().equals("en")) {
            list = mapper.exportEn(payOrderDto);
        } else if (currentLocale.getLanguage().equals("zh")) {
            list = mapper.export(payOrderDto);
        } else {
            list = mapper.exportEn(payOrderDto);
        }

        list.forEach(dto -> {
            dto.setFullName(dto.getFirstName() + " " + dto.getLastName());
        });

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("createTime", ZHelperUtil.getI18nMessages("createTime"));
        writer.addHeaderAlias("orderId", ZHelperUtil.getI18nMessages("orderId"));
        writer.addHeaderAlias("fullName", ZHelperUtil.getI18nMessages("fullName"));
        writer.addHeaderAlias("country", ZHelperUtil.getI18nMessages("country"));
        writer.addHeaderAlias("paymentMethod", ZHelperUtil.getI18nMessages("paymentMethod"));
        writer.addHeaderAlias("amountReceived", ZHelperUtil.getI18nMessages("amountReceived"));
        writer.addHeaderAlias("paymentAmount", ZHelperUtil.getI18nMessages("paymentAmount"));
        writer.addHeaderAlias("fee", ZHelperUtil.getI18nMessages("fee"));
        writer.addHeaderAlias("totalFee", ZHelperUtil.getI18nMessages("totalFee"));
        writer.addHeaderAlias("costPaymentRate", ZHelperUtil.getI18nMessages("costPaymentRate"));
        writer.addHeaderAlias("businessName", ZHelperUtil.getI18nMessages("businessName"));
        writer.addHeaderAlias("paymentTime", ZHelperUtil.getI18nMessages("paymentTime"));
        writer.addHeaderAlias("completionTime", ZHelperUtil.getI18nMessages("completionTime"));
        writer.addHeaderAlias("paymentPurpose", ZHelperUtil.getI18nMessages("paymentPurpose"));
        writer.addHeaderAlias("status", ZHelperUtil.getI18nMessages("status"));
        writer.addHeaderAlias("failureReason", ZHelperUtil.getI18nMessages("failureReason"));
        writer.merge(15, ZHelperUtil.getI18nMessages("paymentDetails"));
        String fileName = user2.getBusinessName() + ZHelperUtil.getI18nMessages("paymentDetails") + ".xlsx";
        extracted(response, writer, list, fileName);
    }


    private static void extracted(HttpServletResponse response, ExcelWriter writer, List<PaymentPayOrderDto> list, String fileName) throws IOException {
        //只保留别名的数据
        writer.setOnlyAlias(true);
        // 默认配置
        writer.write(list, true);
        // 设置单元格样式
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < list.size(); i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 20 / 10);
        }
        // 设置content—type
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        ServletOutputStream outputStream = response.getOutputStream();
        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }


    /**
     * 退款接口
     *
     * @param order 订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refund(PaymentPayOrder order) {
        // 退款金额
        BigDecimal refundAmount = order.getPaymentAmount();
        // 商户
        UserWallet userWallet = userWalletMapper.getOneByBusinessId(order.getBusinessId());
        BigDecimal amount = userWallet.getPaymentWallet().add(refundAmount);
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userWallet.getUserId());
        userWalletLog.setBusinessId(userWallet.getBusinessId());
        userWalletLog.setAction(WalletTypeEnum.REFUND.getCode());
        userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_PAYMENT);
        userWalletLog.setChangeBefore(userWallet.getPaymentWallet());
        userWalletLog.setChangeMoney(refundAmount);
        userWalletLog.setChangeAfter(amount);
        userWalletLog.setNote("代付退款");
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_PAYMENT));
        userWalletLog.setCreateTime(new Date());
        userWalletLogMapper.insert(userWalletLog);
        userWallet.setPaymentWallet(amount);
        userWalletMapper.updateById(userWallet);
    }

    @Override
    public PaymentPayOrder getByOrderNo(String orderNo) {
        QueryWrapper<PaymentPayOrder> qw = new QueryWrapper<>();
        qw.eq("order_id", orderNo);
        return mapper.selectOne(qw);
    }


    @Override
    public void paymentPayOrderStatistics() {
        List<User> list = userMapper.selectAll();
        // 创建一个Calendar实例，并设置为当前时间和时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        // 设置时间为前一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        // 获取前一天的Date对象
        Date previousDay = calendar.getTime();
        String startTime = DateUtil.format(previousDay, "yyyy-MM-dd 00:00:00"); //"2024-10-14 00:00:00";//
        String endTime = DateUtil.format(previousDay, "yyyy-MM-dd 23:59:59");//"2024-10-14 23:59:59";//
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat ymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<BusinessDayPaymentFlowLog> paymentFlowLogs = new ArrayList<>();
        for (User user : list) {
            Integer businessId = user.getBusinessId();
            // 今日 支出/收益
            List<PaymentPayOrder> paymentPayOrders = this.mapper.selectList(new LambdaQueryWrapper<PaymentPayOrder>()
                    .eq(PaymentPayOrder::getBusinessId, businessId)
                    .eq(PaymentPayOrder::getStatus, PayOrderStatusEnum.COMPLETED.getCode())
                    .between(PaymentPayOrder::getCreateTime, startTime, endTime));
            BigDecimal businessFeeTotal = paymentPayOrders.stream().map(p -> p.getBusinessFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal platformFeeTotal = paymentPayOrders.stream().map(p -> p.getPlatformFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subtract = businessFeeTotal.subtract(platformFeeTotal);
            // 用实际到账金额
            BigDecimal costTotalMoney = paymentPayOrders.stream().map(p -> p.getCostTotalMoney()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal costFee = paymentPayOrders.stream().map(p -> p.getCostFee()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal dayUserArrivalAmount = costTotalMoney.subtract(costFee);
            // 历史 支出/收益
            List<PaymentPayOrder> paymentPayOrderAll = this.mapper.selectList(new LambdaQueryWrapper<PaymentPayOrder>()
                    .eq(PaymentPayOrder::getBusinessId, businessId)
                    .eq(PaymentPayOrder::getStatus, PayOrderStatusEnum.COMPLETED.getCode())
                    .le(PaymentPayOrder::getCreateTime, endTime));
            BigDecimal businessFeeTotalAll = paymentPayOrderAll.stream().map(p -> p.getBusinessFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal platformFeeTotalAll = paymentPayOrderAll.stream().map(p -> p.getPlatformFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal historyEarnings = businessFeeTotalAll.subtract(platformFeeTotalAll);
            try {
                BusinessDayPaymentFlowLog businessDayPaymentFlowLog = new BusinessDayPaymentFlowLog();
                businessDayPaymentFlowLog.setBusinessId(user.getBusinessId());
                businessDayPaymentFlowLog.setCreateTime(new Date());
                businessDayPaymentFlowLog.setStatDate(ymd.parse(startTime));
                businessDayPaymentFlowLog.setDayEarnings(subtract);
                businessDayPaymentFlowLog.setDayExpend(platformFeeTotal);
                businessDayPaymentFlowLog.setHistoryEarnings(historyEarnings);
                businessDayPaymentFlowLog.setHistoryExpend(platformFeeTotalAll);
                businessDayPaymentFlowLog.setDaySucceedSum(paymentPayOrders.size());
                businessDayPaymentFlowLog.setDayUserArrivalAmount(dayUserArrivalAmount);
                paymentFlowLogs.add(businessDayPaymentFlowLog);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        for (BusinessDayPaymentFlowLog earningsLog : paymentFlowLogs) {
            BusinessDayPaymentFlowLog businessDayCardEarnLog = businessDayPaymentFlowLogService.getOne(new LambdaQueryWrapper<BusinessDayPaymentFlowLog>()
                    .eq(BusinessDayPaymentFlowLog::getBusinessId, earningsLog.getBusinessId())
                    .eq(BusinessDayPaymentFlowLog::getStatDate, earningsLog.getStatDate()));
            if (null == businessDayCardEarnLog) {
                businessDayPaymentFlowLogService.save(earningsLog);
            } else {
                earningsLog.setId(businessDayCardEarnLog.getId());
                businessDayPaymentFlowLogService.updateById(earningsLog);
            }
        }
    }


    @Override
    public PaymentFlowDto paymentFlowStatistics(Integer businessId) {
        PaymentFlowDto dto = new PaymentFlowDto();
        // 创建一个Calendar实例，并设置为当前时间和时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        // 获取前一天的Date对象
        Date previousDay = calendar.getTime();
        String startTime = DateUtil.format(previousDay, "yyyy-MM-dd 00:00:00"); //"2024-10-14 00:00:00";//
        String endTime = DateUtil.format(previousDay, "yyyy-MM-dd 23:59:59");//"2024-10-14 23:59:59";//
        // 今日 支出/收益
        List<PaymentPayOrder> paymentPayOrders = this.list(new LambdaQueryWrapper<PaymentPayOrder>()
                .eq(PaymentPayOrder::getBusinessId, businessId)
                .eq(PaymentPayOrder::getStatus, PayOrderStatusEnum.COMPLETED.getCode())
                .between(PaymentPayOrder::getCreateTime, startTime, endTime));
        BigDecimal businessFeeTotal = paymentPayOrders.stream().map(p -> p.getBusinessFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal platformFeeTotal = paymentPayOrders.stream().map(p -> p.getPlatformFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal subtract = businessFeeTotal.subtract(platformFeeTotal);
        // 历史 支出/收益
        List<PaymentPayOrder> paymentPayOrderAll = this.list(new LambdaQueryWrapper<PaymentPayOrder>()
                .eq(PaymentPayOrder::getBusinessId, businessId)
                .eq(PaymentPayOrder::getStatus, PayOrderStatusEnum.COMPLETED.getCode())
                .le(PaymentPayOrder::getCreateTime, endTime));
        BigDecimal businessFeeTotalAll = paymentPayOrderAll.stream().map(p -> p.getBusinessFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal platformFeeTotalAll = paymentPayOrderAll.stream().map(p -> p.getPlatformFeeTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal historyEarnings = businessFeeTotalAll.subtract(platformFeeTotalAll);
        dto.setBusinessId(businessId);
        dto.setDayEarnings(subtract);
        dto.setDayExpend(platformFeeTotal);
        dto.setHistoryEarnings(historyEarnings);
        dto.setHistoryExpend(platformFeeTotalAll);
        dto.setDaySucceedSum(paymentPayOrders.size());
        return dto;
    }
}