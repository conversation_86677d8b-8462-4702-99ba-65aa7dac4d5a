package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.UserMoneyLog;
import generator.service.UserMoneyLogService;
import generator.mapper.UserMoneyLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_user_money_log】的数据库操作Service实现
* @createDate 2024-06-05 10:16:58
*/
@Service
public class UserMoneyLogServiceImpl extends ServiceImpl<UserMoneyLogMapper, UserMoneyLog>
    implements UserMoneyLogService{
    @Autowired
    private UserMoneyLogMapper userMoneyLogMapper;
    @Override
    public BigDecimal queryCardMoneyAmount(String formattedDate, Integer businessId, Integer subType) {
        return userMoneyLogMapper.queryCardMoneyAmount(formattedDate,businessId,subType);
    }

    @Override
    public BigDecimal getDayChargeTotal(String formattedDate, Integer businessId, List<Integer> subTypes) {
        return userMoneyLogMapper.getDayChargeTotal(formattedDate,businessId,subTypes);
    }

    @Override
    public BigDecimal getDayMoneyAmount(Integer businessId, List<Integer> subTypes) {
        return userMoneyLogMapper.getDayMoneyAmount(businessId,subTypes);
    }

    @Override
    public BigDecimal queryCardMoneyAmountSum(Integer businessId, Integer subType) {
        return userMoneyLogMapper.queryCardMoneyAmountSum(businessId,subType);
    }
}




