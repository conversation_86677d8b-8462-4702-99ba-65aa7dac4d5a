package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PlatformDayCardLogService;
import generator.domain.PlatformDayCardLog;
import generator.mapper.PlatformDayCardLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PlatformDayCardLogServiceImpl extends ServiceImpl<PlatformDayCardLogMapper, PlatformDayCardLog> implements PlatformDayCardLogService {

}