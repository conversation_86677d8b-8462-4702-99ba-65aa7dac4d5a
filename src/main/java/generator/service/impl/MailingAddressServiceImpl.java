package generator.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.MailingAddress;
import generator.service.MailingAddressService;
import generator.mapper.MailingAddressMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tb_mailing_address】的数据库操作Service实现
* @createDate 2024-08-21 10:16:47
*/
@Service
public class MailingAddressServiceImpl extends ServiceImpl<MailingAddressMapper, MailingAddress>
implements MailingAddressService{

    @Autowired
    MailingAddressMapper mapper;
    @Override
    public MailingAddress getMailingAddress(String cardId) {
        QueryWrapper<MailingAddress> mailingAddressQueryWrapper = new QueryWrapper<>();
        mailingAddressQueryWrapper.eq("card_id",cardId);
        return mapper.selectOne(mailingAddressQueryWrapper);
    }
}
