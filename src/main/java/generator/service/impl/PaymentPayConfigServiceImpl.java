package generator.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.PaymentPayConfig;
import generator.mapper.PaymentPayConfigMapper;
import generator.service.PaymentPayConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;


@Service
public class PaymentPayConfigServiceImpl extends ServiceImpl<PaymentPayConfigMapper, PaymentPayConfig> implements PaymentPayConfigService {
    @Autowired
    PaymentPayConfigMapper mapper;
    @Override
    public  List<PaymentPayConfig> availablePaymentMethods(String country) {
        QueryWrapper<PaymentPayConfig> paymentPayConfigQueryWrapper = new QueryWrapper<>();
        paymentPayConfigQueryWrapper.isNotNull("two_letter_code");
        paymentPayConfigQueryWrapper.eq("status",1);
        if (!country.isEmpty()){
            paymentPayConfigQueryWrapper.like("country_region", country)
                    .or()
                    .like("english_name_country", country);
        }
        return mapper.selectList(paymentPayConfigQueryWrapper);
    }

    @Override
    public HashMap<String, String> getProductType(String product) {
        QueryWrapper<PaymentPayConfig> paymentPayConfigQueryWrapper = new QueryWrapper<>();
        paymentPayConfigQueryWrapper.eq("status",2).eq("payment_product",product);
        PaymentPayConfig paymentPayConfig = mapper.selectOne(paymentPayConfigQueryWrapper);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        if (paymentPayConfig!=null){
            stringStringHashMap.put("product",paymentPayConfig.getPaymentProduct());
            stringStringHashMap.put("field",paymentPayConfig.getProductType());
            stringStringHashMap.put("currency",paymentPayConfig.getSettlementCurrency());
            stringStringHashMap.put("country",paymentPayConfig.getTwoLetterCode());
        }
        return stringStringHashMap;
    }

    @Override
    public List<PaymentPayConfig> getCurrencyAndCountry(String currency, String country) {
        QueryWrapper<PaymentPayConfig> paymentPayConfigQueryWrapper = new QueryWrapper<>();
        paymentPayConfigQueryWrapper.isNotNull("two_letter_code");
        paymentPayConfigQueryWrapper.eq("status",1);
        paymentPayConfigQueryWrapper.eq("settlement_currency",currency);
        if (ObjectUtil.isNotNull(country)){
            paymentPayConfigQueryWrapper.eq("two_letter_code",country);
        }else {
            paymentPayConfigQueryWrapper.eq("two_letter_code","US");
        }
        return mapper.selectList(paymentPayConfigQueryWrapper);
    }

}