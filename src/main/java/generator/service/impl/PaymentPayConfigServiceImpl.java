package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PaymentPayConfigService;
import generator.domain.PaymentPayConfig;
import generator.mapper.PaymentPayConfigMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PaymentPayConfigServiceImpl extends ServiceImpl<PaymentPayConfigMapper, PaymentPayConfig> implements PaymentPayConfigService {

}