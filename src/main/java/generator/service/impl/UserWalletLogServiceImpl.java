package generator.service.impl;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.NumberOfTransactionsDto;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.User;
import generator.domain.UserWalletLog;
import generator.mapper.UserWalletLogMapper;
import generator.service.UserWalletLogService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_wallet_log】的数据库操作Service实现
 * @createDate 2024-06-18 14:32:59
 */
@Service
public class UserWalletLogServiceImpl extends ServiceImpl<UserWalletLogMapper, UserWalletLog>
        implements UserWalletLogService {

    @Autowired
    private UserWalletLogMapper mapper;

    @Override
    public BigDecimal queryCardMoneyAmount2(String o, Integer businessId, List<Integer> subTypes) {
        return mapper.queryCardMoneyAmount2(o, businessId, subTypes);
    }

    @Override
    public IPage<UserWalletLog> userWalletTransferDetails(CardSettingsDto cardSettingsDto) {
        Page<UserWalletLog> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return mapper.userWalletTransferDetails(page, cardSettingsDto);
    }

    @Override
    public void exportUserWalletTransferDetails(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        List<UserWalletLog> list = mapper.userWalletTransferDetailsAll(cardSettingsDto);
        list.forEach(l -> l.setActionStr(WalletTypeEnum.getNameByCode(l.getAction())));
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("createTime", ZHelperUtil.getI18nMessages("card_createTime"));
        writer.addHeaderAlias("orderId", ZHelperUtil.getI18nMessages("orderId"));
        writer.addHeaderAlias("actionStr", ZHelperUtil.getI18nMessages("transactionType"));
        writer.addHeaderAlias("changeMoney", ZHelperUtil.getI18nMessages("card_authAmount"));
        writer.addHeaderAlias("changeAfter", ZHelperUtil.getI18nMessages("card_transAmount"));
        writer.merge(4, ZHelperUtil.getI18nMessages("card_fileTitle2"));
        String fileName = ZHelperUtil.getI18nMessages("card_fileTitle2") + ".xlsx";
        extracted(response, writer, list, fileName);
    }


    /**
     * 仪表盘1
     * 资金变化
     *
     * @param user2 商户
     * @return List<Map.Entry < String, List < UserWalletLog>>> ；
     * @throws ParseException
     */
    @Override
    public List<Map.Entry<String, List<UserWalletLog>>> changeOfFunds(User user2) throws ParseException {
        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 获取最近10天的日期
        List<String> lastTenDays = IntStream.range(1, 11)
                .mapToObj(now::minusDays)
                .map(date -> DATE_FORMAT.format(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant())))
                .toList();

        // 模拟从mapper获取的数据
        List<UserWalletLog> list = mapper.changeOfFunds(user2.getBusinessId());

        // 按日期分组数据
        Map<String, List<UserWalletLog>> groupedByTime = list.stream()
                .collect(Collectors.groupingBy(
                        log -> DATE_FORMAT.format(log.getCreateTime()),
                        Collectors.toList()
                ));

        // 为缺失的日期添加空列表
        lastTenDays.forEach(day -> groupedByTime.putIfAbsent(day, new ArrayList<>()));

        // 将分组结果按日期排序
        LinkedHashMap<String, List<UserWalletLog>> sortedGroupedByTime = groupedByTime.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        // 初始化钱包类型列表
        List<String> walletTypes = Arrays.asList(
                WalletTypeConstants.STWALLET_TYPE_MAIN,
                WalletTypeConstants.STWALLET_TYPE_STORE,
                WalletTypeConstants.STWALLET_TYPE_SHARE,
                WalletTypeConstants.STWALLET_TYPE_PHYSICAL,
                WalletTypeConstants.STWALLET_TYPE_PAYMENT,
                WalletTypeConstants.STWALLET_TYPE_USDT,
                WalletTypeConstants.STWALLET_TYPE_TOKEN
        );

        // 将排序后的分组结果转换为Entry列表
        List<Map.Entry<String, List<UserWalletLog>>> entryList = new ArrayList<>(sortedGroupedByTime.entrySet());

        if (!entryList.isEmpty()) {
            // 处理第一个Entry
            Map.Entry<String, List<UserWalletLog>> firstEntry = entryList.get(0);
            List<UserWalletLog> firstLogList = firstEntry.getValue();
            Set<Object> firstWalletTypes = firstLogList.stream().map(UserWalletLog::getWalletType).collect(Collectors.toSet());
            List<String> missingWalletTypes = walletTypes.stream().filter(walletType -> !firstWalletTypes.contains(walletType)).collect(Collectors.toList());

            if (!missingWalletTypes.isEmpty()) {
                List<UserWalletLog> missingLogs = mapper.findByWalletTypes(missingWalletTypes, user2.getBusinessId(), firstEntry.getKey());
                firstLogList.addAll(missingLogs);
            }

            // 处理后续的Entries
            for (int i = 1; i < entryList.size(); i++) {
                Map.Entry<String, List<UserWalletLog>> entry = entryList.get(i);

                List<UserWalletLog> logList = entry.getValue();
                Set<Object> existingWalletTypes = logList.stream().map(UserWalletLog::getWalletType).collect(Collectors.toSet());
                List<String> missingWalletTypes2 = walletTypes.stream().filter(walletType -> !existingWalletTypes.contains(walletType)).toList();

                if (!missingWalletTypes2.isEmpty()) {
                    Map.Entry<String, List<UserWalletLog>> entry2 = entryList.get(i - 1);
                    List<UserWalletLog> value = entry2.getValue();
                    List<UserWalletLog> previousDayLogs = value.stream().filter(log -> missingWalletTypes2.contains(log.getWalletType())).toList();
                    logList.addAll(previousDayLogs);
                }

            }
        }
        return entryList.stream()
                .map(entry -> new AbstractMap.SimpleEntry<>(formatDate(entry.getKey()), entry.getValue()))
                .collect(Collectors.toList());
    }

    private static final SimpleDateFormat OUTPUT_FORMAT = new SimpleDateFormat("MM-dd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 交易笔数仪表盘
     *
     * @param user2 商户
     * @return HashMap
     */
    @Override
    public HashMap<Integer, List<NumberOfTransactionsDto>> numberOfTransactions(User user2) {
        List<NumberOfTransactionsDto> list = mapper.numberOfTransactions(user2.getBusinessId());
        List<String> allCardIds = list.stream().map(NumberOfTransactionsDto::getCardId).toList();
        Integer i = 0;
        if (!allCardIds.isEmpty()) {
            i = mapper.notInCardIds(user2.getBusinessId(), allCardIds);
        }
        HashMap<Integer, List<NumberOfTransactionsDto>> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(i, list);
        return objectObjectHashMap;
    }


    private static String formatDate(String date) {
        try {
            Date parsedDate = DATE_FORMAT.parse(date);
            return OUTPUT_FORMAT.format(parsedDate);
        } catch (ParseException e) {
            throw new RuntimeException("Error parsing date", e);
        }
    }


    private static void extracted(HttpServletResponse response, ExcelWriter writer, List<UserWalletLog> list, String fileName) throws IOException {
        //只保留别名的数据
        writer.setOnlyAlias(true);
        // 默认配置
        writer.write(list, true);
        // 设置单元格样式
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < list.size(); i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 20 / 10);
        }

        // 设置content—type
        response.setContentType("application/vnd.ms-excel;charset=utf-8");

        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Content-Disposition", "attachment;filename=" + Base64.getEncoder().encodeToString(fileName.getBytes("UTF-8")));

        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }
}




