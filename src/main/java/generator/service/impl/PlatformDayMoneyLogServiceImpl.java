package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.PlatformDayMoneyLog;
import generator.service.PlatformDayMoneyLogService;
import generator.mapper.PlatformDayMoneyLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tb_platform_day_money_log】的数据库操作Service实现
* @createDate 2024-06-05 10:16:39
*/
@Service
public class PlatformDayMoneyLogServiceImpl extends ServiceImpl<PlatformDayMoneyLogMapper, PlatformDayMoneyLog>
    implements PlatformDayMoneyLogService{

}




