package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PlatformDayMoneyLogService;
import generator.domain.PlatformDayMoneyLog;
import generator.mapper.PlatformDayMoneyLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PlatformDayMoneyLogServiceImpl extends ServiceImpl<PlatformDayMoneyLogMapper, PlatformDayMoneyLog> implements PlatformDayMoneyLogService {

}