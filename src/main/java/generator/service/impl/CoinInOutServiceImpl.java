package generator.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.CoinInOut;
import generator.domain.User;
import generator.domain.UserWallet;
import generator.domain.UserWalletLog;
import generator.mapper.CoinInOutMapper;
import generator.service.CoinInOutService;
import generator.service.UserAddressService;
import generator.service.UserWalletLogService;
import generator.service.UserWalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @description 针对表【tb_coin_in_out】的数据库操作Service实现
 * @createDate 2024-07-19 14:59:27
 */
@Service
@Slf4j
public class CoinInOutServiceImpl extends ServiceImpl<CoinInOutMapper, CoinInOut>
        implements CoinInOutService {


    @Autowired
    UserWalletService userWalletService;

    @Autowired
    UserWalletLogService userWalletLogService;

    @Autowired
    UserAddressService userAddressService;

    @Autowired
    CoinInOutMapper coinInOutMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rechargeNow(JSONObject data) {
        //"cointype": 195,
        //"tx": "1597f8e931e3fc23bc13cee34e537202dccf368b6a4a139b8a227a654f6bc56c",
        //"from": "TKsN5cgEbqYqtKXMwTaDKZjpsv5oUhyqs8",
        //"to": "TN99juNgWbH7yYY2gBKx6qC5Vqf6nTmGwS",
        //"amount": "100000000",
        //"decimal": 6,
        //"symbol": "USDT",
        //"contract": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
        //"height": 63283889,
        //"timestamp": 1720496613000
        try {
            QueryWrapper<CoinInOut> query = new QueryWrapper<>();
            query.eq("hash", data.getStr("tx"));
            query.eq("status", 9);
            CoinInOut coinInOut = this.getOne(query);
            if (coinInOut != null) {
                return;
            }

            Integer coinType = data.getInt("cointype");
            String address = data.getStr("to");
            User userInfo = userAddressService.getUserByAddress(address, coinType);


            // 充值手续费
            BigDecimal rate = userInfo.getRechargeRate();

            BigDecimal rechargeMoney = data.getBigDecimal("amount").movePointLeft(data.getInt("decimal"));
            // 将百分比转换为小数
            BigDecimal decimalRate = rate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN);
            // 计算最终金额
            BigDecimal rechargeRate = rechargeMoney.multiply(decimalRate).setScale(2, RoundingMode.HALF_EVEN);
            rechargeMoney = rechargeMoney.subtract(rechargeRate);

            // 写充值日志
            CoinInOut coinLog = new CoinInOut();
            coinLog.setBusinessId(userInfo.getBusinessId());
            coinLog.setTransType(1);
            coinLog.setCoinType(coinType);
            coinLog.setFromAddress(data.getStr("from"));
            coinLog.setToAddress(address);
            coinLog.setHash(data.getStr("tx"));
            coinLog.setAmount(rechargeMoney);
            coinLog.setFee(rechargeRate);
            coinLog.setStatus(9);
//            coinLog.setCreateTime(new Date());
//            coinLog.setUpdateTime(new Date());
            this.save(coinLog);

            // 更改用户钱包
            UserWallet userWallet = userWalletService.getByBusinessId(userInfo.getBusinessId());
            BigDecimal beforeMoney = userWallet.getMainWallet();
            BigDecimal afterMoney = userWallet.getMainWallet().add(rechargeMoney).setScale(2, RoundingMode.CEILING);
            userWallet.setMainWallet(afterMoney);
            userWalletService.updateById(userWallet);

            // 钱包变化日志
            UserWalletLog userWalletLog = new UserWalletLog();
            userWalletLog.setUserId(userInfo.getId());
            userWalletLog.setBusinessId(userInfo.getBusinessId());
            userWalletLog.setAction(WalletTypeEnum.RECHARGE.getCode());
            userWalletLog.setWalletType("main");
            userWalletLog.setChangeBefore(beforeMoney);
            userWalletLog.setChangeMoney(rechargeMoney);
            userWalletLog.setChangeAfter(afterMoney);
            userWalletLog.setNote(String.format("链上充值：%s", rechargeMoney));
            userWalletLog.setOrderId(ZHelperUtil.genOrderId("Z-WR"));
            userWalletLogService.save(userWalletLog);


        } catch (Exception e) {
            log.error("充值操作处理失败 data: {}", data.toStringPretty());
            throw e;
        }

    }
}




