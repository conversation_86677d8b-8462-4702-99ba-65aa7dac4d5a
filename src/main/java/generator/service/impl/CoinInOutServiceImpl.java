package generator.service.impl;


import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.zpay.admin.job.dto.StatInOutDto;
import generator.domain.CoinInOut;
import generator.mapper.CoinInOutMapper;
import generator.service.CoinInOutService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

import static generator.domain.table.CoinInOutTableDef.COIN_IN_OUT;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class CoinInOutServiceImpl extends ServiceImpl<CoinInOutMapper, CoinInOut> implements CoinInOutService {


    @Override
    public HashMap<Integer, StatInOutDto> stat(String dateStr) {
        HashMap<Integer, StatInOutDto> data = new HashMap<>();

        QueryWrapper query = new QueryWrapper().from(COIN_IN_OUT)
                .select(COIN_IN_OUT.BUSINESS_ID)
                .select(COIN_IN_OUT.TRANS_TYPE)
                .select("SUM(`amount`) AS `total_amount`")
                .select("SUM(`fee`) AS `total_fee`")
                .eq("DATE(`create_time`)", dateStr)
                .eq(CoinInOut::getStatus, 9)
                .groupBy(CoinInOut::getBusinessId)
                .groupBy(COIN_IN_OUT.TRANS_TYPE)
                .orderBy(COIN_IN_OUT.BUSINESS_ID, true)
                .orderBy(COIN_IN_OUT.TRANS_TYPE, true);

        List<Row> list = this.listAs(query, Row.class);
        for (Row obj : list) {
            Integer businessId = obj.getInt("business_id");
            StatInOutDto dto;
            if (!data.containsKey(businessId)) {
                dto = new StatInOutDto();
            } else {
                dto = data.get(businessId);
            }
            switch (obj.getInt("trans_type")) {
                case 1:
                    dto.setIn(dto.getIn().add(obj.getBigDecimal("total_amount")));
                    dto.setInFee(dto.getInFee().add(obj.getBigDecimal("total_fee")));
                    break;
                case 2:
                    dto.setOut(dto.getOut().add(obj.getBigDecimal("total_amount")));
                    dto.setOutFee(dto.getOutFee().add(obj.getBigDecimal("total_fee")));
                    break;
                default:
                    break;
            }
            data.put(businessId, dto);
        }
        return data;
    }


    @Override
    public HashMap<Integer, HashMap<String, StatInOutDto>> statOrderByBusiness() {
        HashMap<Integer, HashMap<String, StatInOutDto>> data = new HashMap<>();
        List<Row> list = getStatData();
        for (Row obj : list) {
            Integer businessId = obj.getInt("business_id");
            HashMap<String, StatInOutDto> bidMap;
            if (!data.containsKey(businessId)) {
                bidMap = new HashMap<>();
            } else {
                bidMap = data.get(businessId);
            }
            String statDate = obj.getString("stat_date");
            StatInOutDto dateDto;
            if (!bidMap.containsKey(statDate)) {
                dateDto = new StatInOutDto();
            } else {
                dateDto = bidMap.get(statDate);
            }

            switch (obj.getInt("trans_type")) {
                case 1:
                    dateDto.setIn(dateDto.getIn().add(obj.getBigDecimal("total_amount")));
                    dateDto.setInFee(dateDto.getInFee().add(obj.getBigDecimal("total_fee")));
                    break;
                case 2:
                    dateDto.setOut(dateDto.getOut().add(obj.getBigDecimal("total_amount")));
                    dateDto.setOutFee(dateDto.getOutFee().add(obj.getBigDecimal("total_fee")));
                    break;
                default:
                    break;
            }
            bidMap.put(obj.getString("stat_date"), dateDto);
            data.put(businessId, bidMap);
        }
        return data;
    }


    @Override
    public HashMap<String, HashMap<Integer, StatInOutDto>> statOrderByDate() {
        HashMap<String, HashMap<Integer, StatInOutDto>> data = new HashMap<>();
        List<Row> list = getStatData();
        for (Row obj : list) {
            Integer businessId = obj.getInt("business_id");
            String statDate = obj.getString("stat_date");

            HashMap<Integer, StatInOutDto> statMap;
            if (!data.containsKey(statDate)) {
                statMap = new HashMap<>();
            } else {
                statMap = data.get(statDate);
            }

            StatInOutDto dto;
            if (!statMap.containsKey(businessId)) {
                dto = new StatInOutDto();
            } else {
                dto = statMap.get(businessId);
            }

            switch (obj.getInt("trans_type")) {
                case 1:
                    dto.setIn(dto.getIn().add(obj.getBigDecimal("total_amount")));
                    dto.setInFee(dto.getInFee().add(obj.getBigDecimal("total_fee")));
                    break;
                case 2:
                    dto.setOut(dto.getOut().add(obj.getBigDecimal("total_amount")));
                    dto.setOutFee(dto.getOutFee().add(obj.getBigDecimal("total_fee")));
                    break;
                default:
                    break;
            }
            statMap.put(businessId, dto);
            data.put(statDate, statMap);
        }
        return data;
    }


    private List<Row> getStatData() {
        QueryWrapper query = new QueryWrapper().from(COIN_IN_OUT)
                .select(COIN_IN_OUT.BUSINESS_ID)
                .select(COIN_IN_OUT.TRANS_TYPE)
                .select("DATE(`create_time`) AS `stat_date`")
                .select("SUM(`amount`) AS `total_amount`")
                .select("SUM(`fee`) AS `total_fee`")
                .eq(CoinInOut::getStatus, 9)
                .groupBy(CoinInOut::getBusinessId)
                .groupBy("stat_date")
                .groupBy(COIN_IN_OUT.TRANS_TYPE)
                .orderBy(COIN_IN_OUT.BUSINESS_ID, true)
                .orderBy("stat_date", true)
                .orderBy(COIN_IN_OUT.TRANS_TYPE, true);

        return this.listAs(query, Row.class);
    }
}