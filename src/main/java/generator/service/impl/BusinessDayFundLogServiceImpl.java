package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.BusinessDayFundLog;
import generator.domain.UserWallet;
import generator.domain.UserWalletLog;
import generator.mapper.*;
import generator.service.UserCardLogService;
import generator.service.UserMoneyLogService;
import generator.service.UserService;
import com.qf.zpay.util.DateUtil;
import generator.domain.User;
import generator.service.BusinessDayFundLogService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Log4j2
@Service
public class BusinessDayFundLogServiceImpl extends ServiceImpl<BusinessDayFundLogMapper, BusinessDayFundLog> implements BusinessDayFundLogService {

    @Autowired
    private UserMapper userService;

    @Autowired
    private UserCardLogMapper userCardLogService;

    @Autowired
    private UserMoneyLogMapper userMoneyLogService;

    @Autowired
    private BusinessDayFundLogMapper businessDayFundLogMapper;
    @Autowired
    private UserWalletLogMapper userWalletLogMapper;
    @Autowired
    private UserWalletMapper userWalletMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addExpenditureLog() {

        log.info("-------------------支出流水——————————————开始");
        // 查询所有用户
        QueryWrapper<User> userQueryWrapper2 = new QueryWrapper<>();
        userQueryWrapper2.eq("status", 1);
        List<User> userList = userService.selectList(userQueryWrapper2);
        int i = 0;

        // 获取前一天的日期 格式化为YYYY-MM-DD格式
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String format = today.format(formatter);
        String formattedDate = yesterday.format(formatter);
        List<String> walletTypes = Arrays.asList(
                WalletTypeConstants.STWALLET_TYPE_MAIN,
                WalletTypeConstants.STWALLET_TYPE_STORE,
                WalletTypeConstants.STWALLET_TYPE_SHARE,
                WalletTypeConstants.STWALLET_TYPE_PHYSICAL,
                WalletTypeConstants.STWALLET_TYPE_PAYMENT,
                WalletTypeConstants.STWALLET_TYPE_USDT,
                WalletTypeConstants.STWALLET_TYPE_TOKEN
        );
        for (User user : userList) {
            UserWallet userWallet = userWalletMapper.getOneByBusinessId(user.getBusinessId());
            log.error(userWallet);
            if (userWallet==null){
                continue;
            }
            for (String walletType : walletTypes) {

                //商户今日充值支出(USD）
                BigDecimal dayRecharge = userWalletLogMapper.queryCardAmount(formattedDate, walletType, user.getBusinessId(), WalletTypeEnum.RECHARGE_FEE.getCode());
                List<Integer> subTypes = Arrays.asList(WalletTypeEnum.OPEN_CARD_FEE.getCode(), WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(), WalletTypeEnum.WITHDRAW_FEE.getCode());
                // 商户今日手续费支出
                BigDecimal pingtaiRechargeChargeFee = userWalletLogMapper.queryCardMoneyAmount(formattedDate, walletType, user.getBusinessId(), Collections.singletonList(WalletTypeEnum.RECHARGE_FEE.getCode()));

                //商户今日开卡费支出(USD）
                BigDecimal dayOpenCardCharge = userWalletLogMapper.queryCardAmount(formattedDate, walletType, user.getBusinessId(), WalletTypeEnum.OPEN_CARD_FEE.getCode());

                //今日总支出

                BigDecimal dayChargeTotalExpenditure = userWalletLogMapper.queryCardMoneyAmount(formattedDate, walletType, user.getBusinessId(), subTypes);

                //总开卡费支出
                BigDecimal pingtaiRechargeChargeFee2 =  userWalletLogMapper.queryCardMoneyAmount2(walletType,user.getBusinessId(), Collections.singletonList(WalletTypeEnum.OPEN_CARD_FEE.getCode()));
                List<Integer> subTypes2 = Arrays.asList( WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(),WalletTypeEnum.WITHDRAW_FEE.getCode());

                // 总手续费支出
                BigDecimal pingtaiRechargeChargeFee3 =  userWalletLogMapper.queryCardMoneyAmount2(walletType,user.getBusinessId(), subTypes2);

                // 卡注销支出
                BigDecimal cardCancellationExpenditure = userWalletLogMapper.queryCardMoneyAmount2(walletType, user.getBusinessId(), Collections.singletonList(WalletTypeEnum.CARD_CANCEL_FEE.getCode()));

                // 总支出
                BigDecimal totalChargeFeeExpenditure = userWalletLogMapper.queryCardMoneyAmount2(walletType, user.getBusinessId(), subTypes);

                BusinessDayFundLog businessDayFundLog = businessDayFundLogMapper.queryBusinessIdAndCreateDate(walletType, user.getBusinessId(), format);
                if (businessDayFundLog != null) {
                    setBusinessDayFundLog(walletType, user, dayRecharge, pingtaiRechargeChargeFee, dayOpenCardCharge, dayChargeTotalExpenditure, pingtaiRechargeChargeFee2, pingtaiRechargeChargeFee3, cardCancellationExpenditure, totalChargeFeeExpenditure, businessDayFundLog);
                    int update = businessDayFundLogMapper.updateById(businessDayFundLog);
                    if (update > 0) {
                        i++;
                    }
                } else {
                    //20:36:00.375 ERROR g.s.i.BusinessDayFundLogServiceImpl      : BusinessDayFundLog(id=null, businessId=172589, walletType=store, dayOpenCardFeeExpenditure=null, dayRechargeCardExpenditure=null, dayRechargeChargeCardExpenditure=null, historyBalance=539.50, dayChargeTotalExpenditure=null, totalChargeFeeExpenditure=-30.20, totalOpenCardCharge=-30.00, totalRechargeCharge=-30.20, createTime=Fri Jul 05 20:36:00 GMT+08:00 2024, createDate=Fri Jul 05 00:00:00 GMT+08:00 2024, cardCancellationExpenditure=0.00)
                    BusinessDayFundLog businessDayFundLog1 = new BusinessDayFundLog();
                    setBusinessDayFundLog(walletType, user, dayRecharge, pingtaiRechargeChargeFee, dayOpenCardCharge, dayChargeTotalExpenditure, pingtaiRechargeChargeFee2, pingtaiRechargeChargeFee3, cardCancellationExpenditure, totalChargeFeeExpenditure, businessDayFundLog1);
                    log.error(businessDayFundLog1);
                    int insert = businessDayFundLogMapper.insert(businessDayFundLog1);
                    if (insert > 0) {
                        i++;
                    }
                }
            }
        }
        log.info("统计支出流水任务 结束--------------" + today + "汇总了" + formattedDate + "的" + i + "条数据");
    }

    @Override
    public IPage<BusinessDayFundLog> getExpenditureTurnover(CardSettingsDto cardSettingsDto) {

        Page<BusinessDayFundLog> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return businessDayFundLogMapper.getExpenditureTurnover(page, cardSettingsDto);
    }

    private void setBusinessDayFundLog(String walletType, User user, BigDecimal dayRecharge, BigDecimal pingtaiRechargeChargeFee, BigDecimal dayOpenCardCharge, BigDecimal dayChargeTotalExpenditure, BigDecimal pingtaiRechargeChargeFee2, BigDecimal pingtaiRechargeChargeFee3, BigDecimal cardCancellationExpenditure, BigDecimal totalChargeFeeExpenditure, BusinessDayFundLog businessDayFundLog) {
        businessDayFundLog.setBusinessId(user.getBusinessId());
        businessDayFundLog.setWalletType(walletType);
        businessDayFundLog.setDayOpenCardFeeExpenditure(dayOpenCardCharge != null ? dayOpenCardCharge.negate() : null);
        businessDayFundLog.setDayRechargeCardExpenditure(dayRecharge != null ? dayRecharge.negate() : null);
        businessDayFundLog.setDayRechargeChargeCardExpenditure(pingtaiRechargeChargeFee != null ? pingtaiRechargeChargeFee.negate() : null);
        businessDayFundLog.setHistoryBalance(getWalletAmount(walletType, user));
        businessDayFundLog.setDayChargeTotalExpenditure(dayChargeTotalExpenditure != null ? dayChargeTotalExpenditure.negate() : null);
        businessDayFundLog.setTotalChargeFeeExpenditure(totalChargeFeeExpenditure != null ? totalChargeFeeExpenditure.negate() : null);
        businessDayFundLog.setTotalOpenCardCharge(pingtaiRechargeChargeFee2 != null ? pingtaiRechargeChargeFee2.negate() : null);
        businessDayFundLog.setTotalRechargeCharge(pingtaiRechargeChargeFee3 != null ? pingtaiRechargeChargeFee3.negate() : null);
        businessDayFundLog.setCreateDate(DateUtil.getDate(new Date()));
        // 获取当前日期
        Date today = new Date();

        // 使用 Calendar 类减一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date yesterday = calendar.getTime();
        businessDayFundLog.setCreateTime(yesterday);
        businessDayFundLog.setCardCancellationExpenditure(cardCancellationExpenditure != null ? cardCancellationExpenditure.negate() : null);
    }

    public BigDecimal getWalletAmount(String walletType, User user) {
        QueryWrapper<UserWallet> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("business_id", user.getBusinessId());
        UserWallet userWallet = userWalletMapper.selectOne(userQueryWrapper);
        return switch (walletType) {
            case WalletTypeConstants.STWALLET_TYPE_MAIN ->
                    userWallet.getMainWallet() != null ? userWallet.getMainWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_STORE ->
                    userWallet.getStoreWallet() != null ? userWallet.getStoreWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_SHARE ->
                    userWallet.getShareWallet() != null ? userWallet.getShareWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_PHYSICAL ->
                    userWallet.getPhysicalWallet() != null ? userWallet.getPhysicalWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_PAYMENT ->
                    userWallet.getPaymentWallet() != null ? userWallet.getPaymentWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_USDT ->
                    userWallet.getUsdtWallet() != null ? userWallet.getUsdtWallet() : BigDecimal.ZERO;
            case WalletTypeConstants.STWALLET_TYPE_TOKEN ->
                    userWallet.getTokenWallet() != null ? userWallet.getTokenWallet() : BigDecimal.ZERO;
            default -> throw new IllegalArgumentException("Invalid wallet type: " + walletType);
        };
    }
}
