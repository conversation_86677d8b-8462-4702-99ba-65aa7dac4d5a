package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.BusinessDayInOutLogService;
import generator.domain.BusinessDayInOutLog;
import generator.mapper.BusinessDayInOutLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class BusinessDayInOutLogServiceImpl extends ServiceImpl<BusinessDayInOutLogMapper, BusinessDayInOutLog> implements BusinessDayInOutLogService {

}