package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.PaymentPayeeAccount;
import generator.domain.User;
import generator.mapper.PaymentPayeeAccountMapper;
import generator.service.PaymentPayeeAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_payment_payee_account】的数据库操作Service实现
* @createDate 2024-07-08 09:37:14
*/
@Service
public class PaymentPayeeAccountServiceImpl extends ServiceImpl<PaymentPayeeAccountMapper, PaymentPayeeAccount>
implements PaymentPayeeAccountService{

    @Autowired
    private PaymentPayeeAccountMapper mapper;

    @Override
    public List<PaymentPayeeAccount> accountList(User user, String payeeId) {
        QueryWrapper<PaymentPayeeAccount> qw = new QueryWrapper<>();
        qw.eq("payee_id",payeeId);
        qw.eq("business_id",user.getBusinessId());
        qw.notIn("status","INACTIVE");
        return mapper.selectList(qw);
    }

    @Override
    public PaymentPayeeAccount getByAccountId(String accountId) {
        QueryWrapper<PaymentPayeeAccount> qw = new QueryWrapper<>();
        qw.eq("account_id",accountId);
        return mapper.selectOne(qw);
    }

    @Override
    public void deleteAccount(String accountId) {
        PaymentPayeeAccount byAccountId = getByAccountId(accountId);
        byAccountId.setStatus("INACTIVE");
        mapper.updateById(byAccountId);
    }

    @Override
    public PaymentPayeeAccount getByAccountNoAndBusinessId(Integer businessId, String accountNo, String paymentMethod) {
        QueryWrapper<PaymentPayeeAccount> qw = new QueryWrapper<>();
        qw.eq("payment_method",paymentMethod);
        qw.eq("account_no",accountNo);
        return mapper.selectOne(qw);
    }

    @Override
    public PaymentPayeeAccount getByAccountAndPaymentMethod(String accountNo, String paymentMethod) {
        QueryWrapper<PaymentPayeeAccount> qw = new QueryWrapper<>();
        qw.eq("payment_method",paymentMethod);
        qw.eq("account_no",accountNo);
        return mapper.selectOne(qw);
    }

}
