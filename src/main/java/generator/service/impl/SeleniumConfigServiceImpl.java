package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.SeleniumConfig;
import generator.mapper.SeleniumConfigMapper;
import generator.service.SeleniumConfigService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_selenium_config】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Service
public class SeleniumConfigServiceImpl extends ServiceImpl<SeleniumConfigMapper, SeleniumConfig> implements SeleniumConfigService {

}




