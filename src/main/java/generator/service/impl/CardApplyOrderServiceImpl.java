package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.CardApplyOrderService;
import generator.domain.CardApplyOrder;
import generator.mapper.CardApplyOrderMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class CardApplyOrderServiceImpl extends ServiceImpl<CardApplyOrderMapper, CardApplyOrder> implements CardApplyOrderService {

}