package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CardApplyOrder;
import generator.domain.UserCard;
import generator.mapper.CardApplyOrderMapper;
import generator.service.CardApplyOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_apply_order】的数据库操作Service实现
 * @createDate 2024-07-08 15:34:33
 */
@Service
public class CardApplyOrderServiceImpl extends ServiceImpl<CardApplyOrderMapper, CardApplyOrder>
        implements CardApplyOrderService {
    @Autowired
    private CardApplyOrderMapper cardApplyOrderMapper;


    /**
     * 卡片操作申请数据
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @Override
    public IPage<CardApplyOrder> cardApplyOrderList(CardSettingsDto cardSettingsDto) {
        Page<CardApplyOrder> objectPage = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return cardApplyOrderMapper.cardApplyOrderList(objectPage, cardSettingsDto);
    }

    @Override
    public void updateStatus(UserCard oneByCardNumber, Integer status) {
        QueryWrapper<CardApplyOrder> cardApplyOrderQueryWrapper = new QueryWrapper<>();
        cardApplyOrderQueryWrapper.eq("card_id", oneByCardNumber.getCardId());
        CardApplyOrder cardApplyOrder = cardApplyOrderMapper.selectOne(cardApplyOrderQueryWrapper);
        cardApplyOrder.setStatus(status);
        cardApplyOrderMapper.updateById(cardApplyOrder);
    }

    @Override
    public CardApplyOrder getOpenCardOneByCardNumber(String cardNumber) {
        return getOneByCardNumber(cardNumber, 1);
    }

    @Override
    public CardApplyOrder getOneByCardNumber(String cardNumber, Integer action) {
        LambdaQueryWrapper<CardApplyOrder> query = new LambdaQueryWrapper<>();
        query.eq(CardApplyOrder::getCardNumber, cardNumber);
        query.eq(CardApplyOrder::getAction, action);
        return cardApplyOrderMapper.selectOne(query);
    }

}




