package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import generator.mapper.UserCardCategoryConfigMapper;
import generator.domain.UserCardCategoryConfig;
import generator.service.UserCardCategoryConfigService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_category_config】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserCardCategoryConfigServiceImpl extends ServiceImpl<UserCardCategoryConfigMapper, UserCardCategoryConfig>
        implements UserCardCategoryConfigService {

    @Override
    public UserCardCategoryConfig getAndCheckCardCategoryConfig(Integer cardCategoryConfigId) {
        UserCardCategoryConfig cardCategoryConfig = this.getById(cardCategoryConfigId);
        if (cardCategoryConfig == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }

        return cardCategoryConfig;
    }
}




