package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.UserCardCategoryConfigService;
import generator.domain.UserCardCategoryConfig;
import generator.mapper.UserCardCategoryConfigMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserCardCategoryConfigServiceImpl extends ServiceImpl<UserCardCategoryConfigMapper, UserCardCategoryConfig> implements UserCardCategoryConfigService {

}