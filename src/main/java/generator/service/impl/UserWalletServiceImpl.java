package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserWalletTransferDto;
import com.qf.zpay.dto.req.WithdrawDto;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import generator.domain.*;
import generator.mapper.*;
import generator.service.CardTransFeeLogService;
import generator.service.UserWalletAdvanceLogService;
import generator.service.UserWalletLogService;
import generator.service.UserWalletService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_wallet】的数据库操作Service实现
 * @createDate 2024-06-18 14:32:59
 */
@Service
@Slf4j
public class UserWalletServiceImpl extends ServiceImpl<UserWalletMapper, UserWallet>
        implements UserWalletService {
    @Autowired
    private UserWalletMapper userWalletMapper;
    @Autowired
    private UserWalletLogService userWalletLogService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserMoneyLogMapper userMoneyLogMapper;
    @Autowired
    private UserWalletLogMapper userWalletLogMapper;
    @Autowired
    private AppConfigMapper appConfigMapper;

    @Autowired
    private UserWalletAdvanceLogService userWalletAdvanceLogService;
    private final String BUSINESS_ID = "business_id";
    @Autowired
    private CardTransFeeLogService cardTransFeeLogService;

    /**
     * 用户钱包详情
     *
     * @param businessId 商户id
     * @return UserWallet钱包详情
     */
    @Override
    public UserWallet getByBusinessId(Integer businessId) {
        QueryWrapper<UserWallet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(BUSINESS_ID, businessId);
        return userWalletMapper.selectOne(queryWrapper);
    }

    /**
     * 用户划转
     *
     * @param userWalletTransferDto userWalletTransferDto 划转请求数据dto
     * @param request               获取请求对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userWalletTransfer(UserWalletTransferDto userWalletTransferDto, HttpServletRequest request) {
        User user = (User) request.getAttribute("user");
        QueryWrapper<UserWallet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(BUSINESS_ID, user.getBusinessId()).eq("id", userWalletTransferDto.getId());
        UserWallet userWallet = userWalletMapper.selectOne(queryWrapper);
        BigDecimal amount = userWalletTransferDto.getAmount();
        if (userWalletTransferDto.getAccount().equals(WalletTypeConstants.STWALLET_TYPE_MAIN) && userWalletTransferDto.getTargetAccount().equals(WalletTypeConstants.STWALLET_TYPE_MAIN)) {
            throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR);
        }
        if (!WalletTypeConstants.STWALLET_TYPE_MAIN.equals(userWalletTransferDto.getAccount()) && !WalletTypeConstants.STWALLET_TYPE_MAIN.equals(userWalletTransferDto.getTargetAccount())) {
            throw new ApiException(ResultCode.USER_WALLET_TRANSFER);
        }
        // Target Account
        updateWalletAndLog(userWallet, userWalletTransferDto.getTargetAccount(), userWalletTransferDto.getAccount(), amount, Boolean.TRUE, user, userWalletTransferDto.getBusinessId(), WalletTypeConstants.TYPE_OF_OPERATION_ROLL_INTO);
        // Source Account
        updateWalletAndLog(userWallet, userWalletTransferDto.getAccount(), userWalletTransferDto.getTargetAccount(), amount, Boolean.FALSE, user, userWalletTransferDto.getBusinessId(), WalletTypeConstants.TYPE_OF_OPERATION_ROLL_OVER);
        userWalletMapper.updateById(userWallet);
    }

    public boolean validateChange(BigDecimal changeBefore, BigDecimal amount) {
        int compareResult = changeBefore.compareTo(amount);
        return compareResult >= 0;
    }

    public int getWalletTypeCode(String account) {
        return switch (account) {
            case WalletTypeConstants.STWALLET_TYPE_STORE -> WalletTypeEnum.TRANSFER_TO_STORE_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_SHARE -> WalletTypeEnum.TRANSFER_TO_SHARE_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_PHYSICAL -> WalletTypeEnum.TRANSFER_TO_PHYSICAL_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_PAYMENT -> WalletTypeEnum.TRANSFER_TO_PAYMENT_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_USDT -> WalletTypeEnum.TRANSFER_TO_USDT_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_TOKEN -> WalletTypeEnum.TRANSFER_TO_TOKEN_WALLET.getCode();
            default -> throw new ApiException(ResultCode.PARAM_ERR);
        };
    }

    public int getBeFromWalletTypeCode(String account) {
        return switch (account) {
            case WalletTypeConstants.STWALLET_TYPE_STORE -> WalletTypeEnum.BE_FROM_STORE_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_SHARE -> WalletTypeEnum.BE_FROM_SHARE_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_PHYSICAL -> WalletTypeEnum.BE_FROM_PHYSICAL_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_PAYMENT -> WalletTypeEnum.BE_FROM_PAYMENT_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_USDT -> WalletTypeEnum.BE_FROM_USDT_WALLET.getCode();
            case WalletTypeConstants.STWALLET_TYPE_TOKEN -> WalletTypeEnum.BE_FROM_TOKEN_WALLET.getCode();
            default -> throw new ApiException(ResultCode.PARAM_ERR);
        };
    }

    private void updateWalletAndLog(UserWallet userWallet, String walletType, String account, BigDecimal amount, Boolean isAddition, User user, Integer businessId, Integer action) {
        BigDecimal changeBefore;
        BigDecimal changeAfter;
        UserWalletLog userWalletLog = new UserWalletLog();
        switch (walletType) {
            case WalletTypeConstants.STWALLET_TYPE_MAIN:
                changeBefore = userWallet.getMainWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                if (!isAddition && Objects.equals(action, WalletTypeConstants.TYPE_OF_OPERATION_ROLL_OVER)) {
                    boolean b = validateChange(changeBefore, amount);
                    if (!b) {
                        throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
                    }
                    userWalletLog.setAction(getWalletTypeCode(account));

                } else {
                    userWalletLog.setAction(getBeFromWalletTypeCode(account));
                }
                userWallet.setMainWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_STORE:
                changeBefore = userWallet.getStoreWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setStoreWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_SHARE:
                changeBefore = userWallet.getShareWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setShareWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_PHYSICAL:
                changeBefore = userWallet.getPhysicalWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setPhysicalWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_PAYMENT:
                changeBefore = userWallet.getPaymentWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setPaymentWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_USDT:
                changeBefore = userWallet.getUsdtWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);
                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setUsdtWallet(changeAfter);
                break;
            case WalletTypeConstants.STWALLET_TYPE_TOKEN:
                changeBefore = userWallet.getTokenWallet();
                changeAfter = isAddition ? changeBefore.add(amount) : changeBefore.subtract(amount);

                walletTransfer(amount, isAddition, action, changeBefore, userWalletLog);
                userWallet.setTokenWallet(changeAfter);
                break;
            default:
                throw new ApiException(ResultCode.PARAM_ERR);
        }
        if (isAddition) {
            userWalletLog.setChangeMoney(amount);
        } else {
            userWalletLog.setChangeMoney(amount.negate());
        }
        userWalletLog.setUserId(user.getId());
        userWalletLog.setBusinessId(businessId);
        userWalletLog.setWalletType(walletType);
        userWalletLog.setChangeAfter(changeAfter);
        userWalletLog.setChangeBefore(changeBefore);
        userWalletLog.setCreateTime(new Date());
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_NUMBER));
        userWalletLogService.save(userWalletLog);
    }

    private void walletTransfer(BigDecimal amount, Boolean isAddition, Integer action, BigDecimal changeBefore, UserWalletLog userWalletLog) {
        if (!isAddition && Objects.equals(action, WalletTypeConstants.TYPE_OF_OPERATION_ROLL_OVER)) {
            boolean b = validateChange(changeBefore, amount);
            if (!b) {
                throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
            }
            userWalletLog.setAction(WalletTypeEnum.TRANSFER_TO_WALLET_MAIN.getCode());
        } else {
            userWalletLog.setAction(WalletTypeEnum.BE_FROM_TRANSFER_TO_WALLET_MAIN.getCode());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initWallet(Integer userId) {
        UserWallet userWallet = userWalletMapper.getOneByUserId(userId);
//        log.info("userWallet:{}",userWallet);
        // 如果用户钱包不存在则初始化
        if (userWallet == null) {
            User user = userMapper.getOneById(userId);

            userWallet = new UserWallet();
            userWallet.setUserId(user.getId());
            userWallet.setBusinessId(user.getBusinessId());
            userWallet.setMainWallet(user.getMoney());
            userWalletMapper.insert(userWallet);

            if (user.getMoney().compareTo(BigDecimal.ZERO) > 0) {
                // 记录日志
                UserWalletLog userWalletLog = new UserWalletLog();
                userWalletLog.setUserId(user.getId());
                userWalletLog.setBusinessId(user.getBusinessId());
                userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_MAIN);
                userWalletLog.setAction(1);
                userWalletLog.setChangeBefore(BigDecimal.ZERO);
                userWalletLog.setChangeMoney(user.getMoney());
                userWalletLog.setChangeAfter(user.getMoney());
                userWalletLog.setNote("初始化用户钱包");
                userWalletLogMapper.insert(userWalletLog);
            }
        }
    }

    @Autowired
    private CoinInOutMapper coinInOutMapper;

    /**
     * 提现
     *
     * @param withdrawDto 提现数据dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void withdraw(WithdrawDto withdrawDto) {

        // 最小提现金额
        AppConfig appConfig = appConfigMapper.selectByKey("mix_deposit_amount");

        // 验证提现金额是否小于最小提现金额
        if (!validateChange(withdrawDto.getMoney(), BigDecimal.valueOf(Long.parseLong(appConfig.getValue())))) {
            throw new ApiException(ResultCode.USER_WALLET_THE_WITHDRAWAL_AMOUNT_IS_TOO_SMALL);
        }

        // 查询用户和钱包信息
        User user = userMapper.getOneById(withdrawDto.getUserId());
        UserWallet userWallet = userWalletMapper.getOneByBusinessId(withdrawDto.getBusinessId());

        // 验证钱包余额是否充足
        if (!validateChange(userWallet.getMainWallet(), withdrawDto.getMoney())) {
            throw new ApiException(ResultCode.USER_WALLET_TRANSFER_ERROR_INSUFFICIENT_BALANCE);
        }


        // 计算手续费和变动金额
        BigDecimal fee = calculateFee(withdrawDto.getMoney(), user.getWithdrawRate());
        // 到账金额
        BigDecimal amountAfterFee = withdrawDto.getMoney().subtract(fee);
        // 钱包剩余金额
        BigDecimal newWalletBalance = userWallet.getMainWallet().subtract(amountAfterFee).subtract(fee);
        String orderId = UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_NUMBER);
        // 记录提现日志
        CoinInOut coinInOut = new CoinInOut();
        coinInOut.setBusinessId(withdrawDto.getBusinessId());
        coinInOut.setTransType(2);
        coinInOut.setCoinType(withdrawDto.getCoinType());
        coinInOut.setFromAddress("");
        coinInOut.setToAddress(withdrawDto.getAddress());
        coinInOut.setHash("");
        coinInOut.setAmount(amountAfterFee);
        coinInOut.setFee(fee);
        coinInOut.setStatus(0);
        coinInOutMapper.insert(coinInOut);
        // 记录钱包操作日志
        logWalletChange(withdrawDto, userWallet, newWalletBalance);

        // 更新用户钱包余额
        userWallet.setMainWallet(newWalletBalance);
        userWalletMapper.updateById(userWallet);

    }

    /**
     * 充提记录
     *
     * @param cardSettingsDto 筛选条件
     * @return 充提记录分页数据
     */
    @Override
    public IPage<CoinInOut> getCardTransactionDetails(CardSettingsDto cardSettingsDto) {
        Page<CoinInOut> objectPage = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return coinInOutMapper.getUserMoneyLogList(objectPage, cardSettingsDto);
    }

    @Override
    public void changeAmount(Integer businessId, BigDecimal amount, CardModelEnum cardModelEnum, WalletTypeEnum action, UserWalletAdvanceLog advanceLog, CardTransFeeLog cardTransFee) {
        UserWallet userWallet = getByBusinessId(businessId);
        BigDecimal changeBefore = BigDecimal.ZERO;
        BigDecimal changeAfter = BigDecimal.ZERO;
        String walletType = cardModelEnum.getCode();
        String note = action.getDesc();
        String orderId = UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_PAYMENT);
        switch (cardModelEnum) {
            case RECHARGE:
                walletType = "store";
                changeAfter = userWallet.getStoreWallet().add(amount);
                changeBefore = (userWallet.getStoreWallet());
                if (changeAfter.compareTo(BigDecimal.ZERO) < 0) {
                    BigDecimal storeAmo = changeAfter;
                    changeAfter = (BigDecimal.ZERO);
                    amount = changeBefore.negate();
                    userWallet.setStoreWallet(changeAfter);

                    BigDecimal changeAfter2;
                    BigDecimal changeBefore2;
                    String walletType2 = "main";
                    String note2 = note;
                    changeBefore2 = userWallet.getMainWallet();
                    if (userWallet.getMainWallet().compareTo(storeAmo.negate()) < 0) {
                        BigDecimal mainAmo = userWallet.getMainWallet().add(storeAmo);
                        changeAfter2 = BigDecimal.ZERO;
                        userWallet.setMainWallet(changeAfter2);
                        note2 = "剩余未扣款金额 ：" + mainAmo;
                        // 冻结商户
                        userMapper.update(new LambdaUpdateWrapper<User>()
                                .set(User::getBusinessStatus, 2).eq(User::getBusinessId, businessId));
                    } else {
                        changeAfter2 = userWallet.getMainWallet().add(storeAmo);
                        userWallet.setMainWallet(changeAfter2);
                    }
                    addUserWalletLog(userWallet, action.getCode(), storeAmo, note2, walletType2, changeBefore2, changeAfter2, orderId);
                    break;
                }
                userWallet.setStoreWallet(changeAfter);
                break;
            case SHARE:
                changeAfter = userWallet.getShareWallet().add(amount);
                changeBefore = (userWallet.getShareWallet());
                userWallet.setShareWallet(changeAfter);
                break;
            case PHYSICAL:
                changeAfter = userWallet.getPhysicalWallet().add(amount);
                changeBefore = (userWallet.getPhysicalWallet());
                userWallet.setPhysicalWallet(changeAfter);
                break;
        }
        this.updateById(userWallet);
        if (advanceLog != null && advanceLog.getChangeFee() != null) {
            advanceLog.setOrderId(orderId);
            UserWalletAdvanceLog one = userWalletAdvanceLogService.getOne(new LambdaQueryWrapper<UserWalletAdvanceLog>()
                    .eq(UserWalletAdvanceLog::getCardId, advanceLog.getCardId())
                    .orderByDesc(UserWalletAdvanceLog::getCreateTime)
                    .last("limit 1"));
            if (one != null) {
                advanceLog.setAccruingAmounts(one.getAccruingAmounts().add(advanceLog.getChangeFee()));
            } else {
                advanceLog.setAccruingAmounts(advanceLog.getChangeFee());
            }
            userWalletAdvanceLogService.save(advanceLog);
        }
        if (cardTransFee != null) {
            cardTransFeeLogService.save(cardTransFee);
        }
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            addUserWalletLog(userWallet, action.getCode(), amount, note, walletType, changeBefore, changeAfter, orderId);
        }
    }

    private void addUserWalletLog(UserWallet userWallet, Integer action, BigDecimal amount, String note, String walletType, BigDecimal changeBefore, BigDecimal changeAfter, String orderId) {
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userWallet.getUserId());
        userWalletLog.setBusinessId(userWallet.getBusinessId());
        userWalletLog.setAction(action);
        userWalletLog.setWalletType(walletType);
        userWalletLog.setChangeMoney(amount);
        userWalletLog.setChangeBefore(changeBefore);
        userWalletLog.setChangeAfter(changeAfter);
        userWalletLog.setNote(note);
        userWalletLog.setOrderId(orderId);
        userWalletLog.setCreateTime(new Date());
        userWalletLogService.save(userWalletLog);
    }

    private void logWalletChange(WithdrawDto withdrawDto, UserWallet userWallet, BigDecimal afterWalletBalance) {
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(withdrawDto.getUserId());
        userWalletLog.setBusinessId(withdrawDto.getBusinessId());
        userWalletLog.setChangeMoney(withdrawDto.getMoney().negate());
        userWalletLog.setAction(WalletTypeEnum.WITHDRAW.getCode());
        userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_MAIN);
        userWalletLog.setChangeBefore(userWallet.getMainWallet());
        userWalletLog.setChangeAfter(afterWalletBalance);
        userWalletLog.setCreateTime(new Date());
        userWalletLog.setNote("提现");
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_NUMBER));
        userWalletLogMapper.insert(userWalletLog);

    }

    public static BigDecimal calculateFee(BigDecimal money, BigDecimal withdrawRate) {
        // 将百分比转换为小数
        BigDecimal decimalRate = withdrawRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_EVEN);
        // 计算手续费
        return money.multiply(decimalRate).setScale(2, RoundingMode.HALF_EVEN);
    }
}




