package generator.service.impl;


import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.zpay.admin.job.dto.StatCardFundDto;
import generator.domain.CardActionLog;
import generator.mapper.CardActionLogMapper;
import generator.service.CardActionLogService;
import generator.service.UserCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.erupt.linq.Linq;
import xyz.erupt.linq.util.Columns;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mybatisflex.core.query.QueryMethods.sum;
import static generator.domain.table.CardActionLogTableDef.CARD_ACTION_LOG;

/**
 * 卡片操作日志 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service

public class CardActionLogServiceImpl extends ServiceImpl<CardActionLogMapper, CardActionLog> implements CardActionLogService {

    @Autowired
    UserCardService userCardService;

    @Override
    public StatCardFundDto statCard(String statDate, Integer businessId) {
        StatCardFundDto stat = new StatCardFundDto();

        QueryWrapper query = new QueryWrapper();
        query.where(CARD_ACTION_LOG.CREATE_DATE.eq(statDate));
        if (businessId != null) {
            query.where(CARD_ACTION_LOG.BUSINESS_ID.eq(businessId));
        }
        List<CardActionLog> cardActionList = this.list(query);

        Map<String, Object> openCardData = Linq.from(cardActionList)
                .where(CardActionLog::getType, type -> type == 1)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (openCardData != null) {
            BigDecimal count = (BigDecimal) openCardData.get("count");
            stat.setOpenCardNum(count.intValue());
            stat.setOpenCardFeeCost((BigDecimal) openCardData.get("costFee"));
            stat.setOpenCardFeePlatform((BigDecimal) openCardData.get("platformFee"));
            stat.setOpenCardFeeBusiness((BigDecimal) openCardData.get("businessFee"));
            stat.setOpenCardFeePlatformEarn((BigDecimal) openCardData.get("platformEarn"));
            stat.setOpenCardFeeBusinessEarn((BigDecimal) openCardData.get("businessEarn"));
        }

        Map<String, Object> rechargeData = Linq.from(cardActionList)
                .where(CardActionLog::getType, type -> type == 2)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (rechargeData != null) {
            BigDecimal count = (BigDecimal) rechargeData.get("count");
            stat.setRechargeNum(count.intValue());
            stat.setRechargeFeeCost((BigDecimal) rechargeData.get("costFee"));
            stat.setRechargeFeePlatform((BigDecimal) rechargeData.get("platformFee"));
            stat.setRechargeFeeBusiness((BigDecimal) rechargeData.get("businessFee"));
            stat.setRechargeFeePlatformEarn((BigDecimal) rechargeData.get("platformEarn"));
            stat.setRechargeFeeBusinessEarn((BigDecimal) rechargeData.get("businessEarn"));
        }

        Map<String, Object> cancelCardData = Linq.from(cardActionList)
                .where(CardActionLog::getType, type -> type == 3)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (cancelCardData != null) {
            BigDecimal count = (BigDecimal) cancelCardData.get("count");
            stat.setCancelCardNum(count.intValue());
            stat.setCancelCardFeeCost((BigDecimal) cancelCardData.get("costFee"));
            stat.setCancelCardFeePlatform((BigDecimal) cancelCardData.get("platformFee"));
            stat.setCancelCardFeeBusiness((BigDecimal) cancelCardData.get("businessFee"));
            stat.setCancelCardFeePlatformEarn((BigDecimal) cancelCardData.get("platformEarn"));
            stat.setCancelCardFeeBusinessEarn((BigDecimal) cancelCardData.get("businessEarn"));
        }

        Map<String, Object> rechargeCardData = Linq.from(cardActionList)
                .where(CardActionLog::getCardModel, "recharge"::equals)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (rechargeCardData != null) {
            BigDecimal count = (BigDecimal) rechargeCardData.get("count");
            stat.setRechargeCardNum(count.intValue());
            stat.setRechargeCardFeeCost((BigDecimal) rechargeCardData.get("costFee"));
            stat.setRechargeCardFeePlatform((BigDecimal) rechargeCardData.get("platformFee"));
            stat.setRechargeCardFeeBusiness((BigDecimal) rechargeCardData.get("businessFee"));
            stat.setRechargeCardFeePlatformEarn((BigDecimal) rechargeCardData.get("platformEarn"));
            stat.setRechargeCardFeeBusinessEarn((BigDecimal) rechargeCardData.get("businessEarn"));
        }

        Map<String, Object> shareCardData = Linq.from(cardActionList)
                .where(CardActionLog::getCardModel, "share"::equals)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (shareCardData != null) {
            BigDecimal count = (BigDecimal) shareCardData.get("count");
            stat.setShareCardNum(count.intValue());
            stat.setShareCardFeeCost((BigDecimal) shareCardData.get("costFee"));
            stat.setShareCardFeePlatform((BigDecimal) shareCardData.get("platformFee"));
            stat.setShareCardFeeBusiness((BigDecimal) shareCardData.get("businessFee"));
            stat.setShareCardFeePlatformEarn((BigDecimal) shareCardData.get("platformEarn"));
            stat.setShareCardFeeBusinessEarn((BigDecimal) shareCardData.get("businessEarn"));
        }

        Map<String, Object> physicalCardData = Linq.from(cardActionList)
                .where(CardActionLog::getCardModel, "physical"::equals)
                .select(Columns.count(CardActionLog::getId, "count"))
                .select(Columns.sum(CardActionLog::getCostFee, "costFee"))
                .select(Columns.sum(CardActionLog::getPlatformFee, "platformFee"))
                .select(Columns.sum(CardActionLog::getBusinessFee, "businessFee"))
                .select(Columns.sum(CardActionLog::getPlatformEarn, "platformEarn"))
                .select(Columns.sum(CardActionLog::getBusinessEarn, "businessEarn"))
                .writeMapOne();
        if (physicalCardData != null) {
            BigDecimal count = (BigDecimal) physicalCardData.get("count");
            stat.setPhysicalCardNum(count.intValue());
            stat.setPhysicalCardFeeCost((BigDecimal) physicalCardData.get("costFee"));
            stat.setPhysicalCardFeePlatform((BigDecimal) physicalCardData.get("platformFee"));
            stat.setPhysicalCardFeeBusiness((BigDecimal) physicalCardData.get("businessFee"));
            stat.setPhysicalCardFeePlatformEarn((BigDecimal) physicalCardData.get("platformEarn"));
            stat.setPhysicalCardFeeBusinessEarn((BigDecimal) physicalCardData.get("businessEarn"));
        }

        stat.setPlatformDayDisburse(stat.getOpenCardFeeCost().add(stat.getRechargeFeeCost()).add(stat.getCancelCardFeeCost()));
        stat.setBusinessDayDisburse(stat.getOpenCardFeePlatform().add(stat.getRechargeFeePlatform()).add(stat.getCancelCardFeePlatform()));

        stat.setPlatformDayEarn(stat.getOpenCardFeePlatformEarn().add(stat.getRechargeFeePlatformEarn()).add(stat.getCancelCardFeePlatformEarn()));
        stat.setBusinessDayEarn(stat.getOpenCardFeeBusinessEarn().add(stat.getRechargeFeeBusinessEarn()).add(stat.getCancelCardFeeBusinessEarn()));

        HashMap fundHistory = this.queryChain()
                .select(sum(CARD_ACTION_LOG.COST_FEE).as("platformHistoryDisburse"))
                .select(sum(CARD_ACTION_LOG.BUSINESS_FEE).as("businessHistoryDisburse"))
                .select(sum(CARD_ACTION_LOG.PLATFORM_EARN).as("platformHistoryEarn"))
                .select(sum(CARD_ACTION_LOG.BUSINESS_EARN).as("businessHistoryEarn"))
                .from(CARD_ACTION_LOG)
                .oneAs(HashMap.class);

        if (fundHistory != null) {
            stat.setPlatformHistoryDisburse((BigDecimal) fundHistory.get("platformHistoryDisburse"));
            stat.setBusinessHistoryDisburse((BigDecimal) fundHistory.get("businessHistoryDisburse"));
            stat.setPlatformHistoryEarn((BigDecimal) fundHistory.get("platformHistoryEarn"));
            stat.setBusinessHistoryEarn((BigDecimal) fundHistory.get("businessHistoryEarn"));
        }
        return stat;
    }

    @Override
    public StatCardFundDto statPlatformCard(String statDate) {
        return statCard(statDate, null);
    }

    @Override
    public StatCardFundDto statPlatformCardToday() {
        return statCard(DateUtil.today(), null);
    }

    @Override
    public StatCardFundDto statBusinessCardToday(Integer businessId) {
        return statCard(DateUtil.today(), businessId);
    }
}