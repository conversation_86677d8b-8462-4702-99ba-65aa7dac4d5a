package generator.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.CardActionLogDto;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.CardTransactionStatementDto;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.*;
import generator.mapper.CardActionLogMapper;
import generator.mapper.UserMapper;
import generator.mapper.UserWalletLogMapper;
import generator.service.BusinessDayCardDisburseLogService;
import generator.service.BusinessDayCardEarnLogService;
import generator.service.CardActionLogService;
import generator.service.UserWalletService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_action_log(卡片操作日志)】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Service
public class CardActionLogServiceImpl extends ServiceImpl<CardActionLogMapper, CardActionLog>
        implements CardActionLogService {
    @Autowired
    private CardActionLogMapper cardActionLogMapper;

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserWalletService userWalletService;
    @Autowired
    private BusinessDayCardEarnLogService businessDayCardEarnLogService;
    @Autowired
    private BusinessDayCardDisburseLogService businessDayCardDisburseLogService;
    @Autowired
    private UserWalletLogMapper userWalletLogMapper;

    /**
     * 卡片历史
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片历史分页数据详情
     */
    @Override
    public IPage<CardActionLogDto> getCardHistory(CardSettingsDto cardSettingsDto) {
        Page<CardActionLogDto> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return cardActionLogMapper.getCardHistory(page, cardSettingsDto);
    }

    /**
     * 卡片历史导出
     *
     * @param cardSettingsDto 筛选条件
     * @param response        请求对象
     * @param request         响应数据
     * @throws IOException ；
     */
    @Override
    public void exportCardHistory(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        User user2 = (User) request.getAttribute("user");
        cardSettingsDto.setBusinessId(user2.getBusinessId());
        List<CardActionLogDto> cardActionLog = cardActionLogMapper.exportCardHistory(cardSettingsDto);
        Locale currentLocale = LocaleContextHolder.getLocale();
        List<CardTransactionStatementDto> list;
        // 判断语言环境
        if (currentLocale.getLanguage().equals("en")) {
            cardActionLog = cardActionLogMapper.exportCardHistoryEn(cardSettingsDto);
        } else if (currentLocale.getLanguage().equals("zh")) {
            cardActionLog = cardActionLogMapper.exportCardHistory(cardSettingsDto);
        } else {
            cardActionLog = cardActionLogMapper.exportCardHistoryEn(cardSettingsDto);
        }
        //在内存操作，写到浏览器
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("createTime", ZHelperUtil.getI18nMessages("createTime"));
        writer.addHeaderAlias("orderId", ZHelperUtil.getI18nMessages("orderId"));
        writer.addHeaderAlias("cardNumber", ZHelperUtil.getI18nMessages("cardNumber"));
        writer.addHeaderAlias("cardModel", ZHelperUtil.getI18nMessages("cardModel"));
        writer.addHeaderAlias("typeDesc", ZHelperUtil.getI18nMessages("typeDesc"));
        writer.addHeaderAlias("platformFee", ZHelperUtil.getI18nMessages("platformFee"));
        writer.addHeaderAlias("status", ZHelperUtil.getI18nMessages("status"));
        writer.addHeaderAlias("note", ZHelperUtil.getI18nMessages("note"));
        writer.merge(7, ZHelperUtil.getI18nMessages("fileTitle"));
        // 设置标题
        String fileName = ZHelperUtil.getI18nMessages("fileTitle") + ".xlsx";
        //只保留别名的数据
        writer.setOnlyAlias(true);
        // 默认配置
        writer.write(cardActionLog, true);
        // 设置单元格样式
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < cardActionLog.size(); i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 20 / 10);
        }
        // 设置content—type
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Content-Disposition", "attachment;filename=" + Base64.getEncoder().encodeToString(fileName.getBytes("UTF-8")));
        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();

    }

    @Override
    public void journalAccountStatistics() {
        List<User> list = userMapper.selectAll();
        // 创建一个Calendar实例，并设置为当前时间和时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        // 设置时间为前一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        // 获取前一天的Date对象
        Date previousDay = calendar.getTime();
        String startTime = DateUtil.format(previousDay, "yyyy-MM-dd 00:00:00"); //"2024-10-14 00:00:00";//
        String endTime = DateUtil.format(previousDay, "yyyy-MM-dd 23:59:59");//"2024-10-14 23:59:59";//
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat ymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 卡模式     RECHARGE("recharge", "充值"),SHARE("share", "分享"),PHYSICAL("physical", "实体卡");
        List<String> models = Arrays.asList("recharge", "share", "physical");
        for (User user : list) {
            List<BusinessDayCardEarnLog> earningsLogs = new ArrayList<>();
            for (String model : models) {
                List<CardActionLog> modelList = cardActionLogMapper.findListByModel(user.getBusinessId(), startTime, endTime, model);
                List<CardActionLog> openCard = modelList.stream().filter(m -> 1 == (m.getType())).collect(Collectors.toList());
                // 1开卡（卡费）
                BigDecimal dayOpenCardFee = new BigDecimal(0.0);
                // 开卡（卡费收益）
                BigDecimal dayOpenCardEarnings = new BigDecimal(0.0);
                if (!openCard.isEmpty()) {
                    CardActionLog cardActionLog = openCard.get(0);
                    dayOpenCardFee = cardActionLog.getBusinessFee();
                    dayOpenCardEarnings = cardActionLog.getBusinessEarn();
                }
                // 2充值（手续费）
                BigDecimal dayRechargeChargeFee = new BigDecimal(0.0);
                // 充值（手续费收益）
                BigDecimal dayRechargeChargeCardEarnings = new BigDecimal(0.0);
                List<CardActionLog> topUp = modelList.stream().filter(m -> 2 == (m.getType())).collect(Collectors.toList());
                if (!topUp.isEmpty()) {
                    CardActionLog cardActionLog = topUp.get(0);
                    dayRechargeChargeFee = cardActionLog.getBusinessFee();
                    dayRechargeChargeCardEarnings = cardActionLog.getBusinessEarn();
                }
                // 3销卡收益
                BigDecimal cancelCardEarnings = new BigDecimal(0.0);
                BigDecimal cancelCardFee = new BigDecimal(0.0);
                List<CardActionLog> cancelCard = modelList.stream().filter(m -> 3 == (m.getType())).collect(Collectors.toList());
                if (!cancelCard.isEmpty()) {
                    CardActionLog cardActionLog = cancelCard.get(0);
                    cancelCardEarnings = cardActionLog.getBusinessEarn();
                    cancelCardFee = cardActionLog.getBusinessFee();
                }
                // 今日总收益
                BigDecimal dayEarnings = new BigDecimal(0.0).add(dayOpenCardEarnings).add(dayRechargeChargeCardEarnings).add(cancelCardEarnings);
                // 历史总收益
                String historyEarnings = cardActionLogMapper.findTotalRevenue(user.getBusinessId(), endTime, model);
                String modelStr = model;
                if ("recharge".equals(model)) {
                    modelStr = "store";
                }
                UserWalletLog userWalletLog = userWalletLogMapper.findChangeMoneyById(user.getBusinessId(), startTime, endTime, modelStr, "34");
                try {
                    BusinessDayCardEarnLog businessDayCardEarnLog = new BusinessDayCardEarnLog();
                    businessDayCardEarnLog.setBusinessId(user.getBusinessId());
                    businessDayCardEarnLog.setDayOpenCardFee(dayOpenCardFee);
                    businessDayCardEarnLog.setDayOpenCardEarnings(dayOpenCardEarnings);
                    businessDayCardEarnLog.setDayRechargeChargeFee(dayRechargeChargeFee);
                    businessDayCardEarnLog.setDayRechargeChargeCardEarnings(dayRechargeChargeCardEarnings);
                    businessDayCardEarnLog.setCreateTime(new Date());
                    businessDayCardEarnLog.setStatDate(ymd.parse(startTime));
                    businessDayCardEarnLog.setCancelCardFee(cancelCardFee);
                    businessDayCardEarnLog.setCancelCardEarnings(cancelCardEarnings);
                    businessDayCardEarnLog.setDayEarnings(dayEarnings);
                    businessDayCardEarnLog.setHistoryEarnings(StringUtils.isEmpty(historyEarnings) ? new BigDecimal(0.0) : new BigDecimal(historyEarnings));
                    businessDayCardEarnLog.setCardModel(model);
                    businessDayCardEarnLog.setCardCancellationBalanceReturn(null != userWalletLog ? userWalletLog.getChangeMoney() : new BigDecimal(0.0));
                    earningsLogs.add(businessDayCardEarnLog);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            for (BusinessDayCardEarnLog earningsLog : earningsLogs) {
                BusinessDayCardEarnLog businessDayCardEarnLog = businessDayCardEarnLogService.getOne(new LambdaQueryWrapper<BusinessDayCardEarnLog>()
                        .eq(BusinessDayCardEarnLog::getBusinessId, earningsLog.getBusinessId())
                        .eq(BusinessDayCardEarnLog::getStatDate, earningsLog.getStatDate())
                        .eq(BusinessDayCardEarnLog::getCardModel, earningsLog.getCardModel()));
                if (null == businessDayCardEarnLog) {
                    businessDayCardEarnLogService.save(earningsLog);
                } else {
                    earningsLog.setId(businessDayCardEarnLog.getId());
                    businessDayCardEarnLogService.updateById(earningsLog);
                }
            }
        }
    }


    @Override
    public void expendStatistics() {
        List<User> list = userMapper.selectAll();
        // 创建一个Calendar实例，并设置为当前时间和时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        // 设置时间为前一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        // 获取前一天的Date对象
        Date previousDay = calendar.getTime();
        String startTime = DateUtil.format(previousDay, "yyyy-MM-dd 00:00:00"); //"2024-10-14 00:00:00";//
        String endTime = DateUtil.format(previousDay, "yyyy-MM-dd 23:59:59");//"2024-10-14 23:59:59";//
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat ymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 卡模式     RECHARGE("recharge", "充值"),SHARE("share", "分享"),PHYSICAL("physical", "实体卡");
        List<String> models = Arrays.asList("recharge", "share", "physical");
        for (User user : list) {
            List<BusinessDayCardDisburseLog> dayFundLogs = new ArrayList<>();
            UserWallet userWallet = userWalletService.getByBusinessId(user.getBusinessId());
            for (String model : models) {
                List<CardActionLog> modelList = cardActionLogMapper.findExpendByModel(user.getBusinessId(), startTime, endTime, model);
                List<CardActionLog> openCard = modelList.stream().filter(m -> 1 == (m.getType())).collect(Collectors.toList());
                // 1开卡费支出
                BigDecimal dayOpenCardFeeExpenditure = new BigDecimal(0.0);
                if (!openCard.isEmpty()) {
                    CardActionLog cardActionLog = openCard.get(0);
                    dayOpenCardFeeExpenditure = cardActionLog.getPlatformFee();
                }
                // 2 手续费支出
                BigDecimal dayRechargeChargeCardExpenditure = new BigDecimal(0.0);
                // 2.1 充值支出
                BigDecimal dayRechargeCardExpenditure = new BigDecimal(0.0);
                List<CardActionLog> topUp = modelList.stream().filter(m -> 2 == (m.getType())).collect(Collectors.toList());
                if (!topUp.isEmpty()) {
                    CardActionLog cardActionLog = topUp.get(0);
                    dayRechargeChargeCardExpenditure = cardActionLog.getPlatformFee();
                    dayRechargeCardExpenditure = cardActionLog.getMoney().subtract(cardActionLog.getBusinessFee());
                }
                // 3销卡支出
                BigDecimal cardCancellationExpenditure = new BigDecimal(0.0);
                List<CardActionLog> cancelCard = modelList.stream().filter(m -> 3 == (m.getType())).collect(Collectors.toList());
                if (!cancelCard.isEmpty()) {
                    CardActionLog cardActionLog = cancelCard.get(0);
                    cardCancellationExpenditure = cardActionLog.getPlatformFee();
                }
                // 总支出
                BigDecimal dayChargeTotalExpenditure = new BigDecimal(0.0).add(dayOpenCardFeeExpenditure).add(dayRechargeChargeCardExpenditure).add(cardCancellationExpenditure);
                // RECHARGE("recharge", "充值"),SHARE("share", "分享"),PHYSICAL("physical", "实体卡");
                // 余额
                BigDecimal balance = new BigDecimal(0.0);
                if (null != userWallet && "recharge".equals(model)) {
                    balance = userWallet.getStoreWallet();
                } else if (null != userWallet && "share".equals(model)) {
                    balance = userWallet.getShareWallet();
                } else if (null != userWallet && "physical".equals(model)) {
                    balance = userWallet.getPhysicalWallet();
                }
                try {
                    BusinessDayCardDisburseLog dLog = new BusinessDayCardDisburseLog();
                    dLog.setBusinessId(user.getBusinessId());
                    dLog.setCreateTime(new Date());
                    dLog.setStatDate(ymd.parse(startTime));
                    dLog.setDayOpenCardFeeExpenditure(dayOpenCardFeeExpenditure);
                    dLog.setDayRechargeChargeCardExpenditure(dayRechargeChargeCardExpenditure);
                    dLog.setCardCancellationExpenditure(cardCancellationExpenditure);
                    dLog.setDayChargeTotalExpenditure(dayChargeTotalExpenditure);
                    dLog.setDayRechargeCardExpenditure(dayRechargeCardExpenditure);
                    dLog.setCardModel(model);
                    dLog.setHistoryBalance(balance);
                    dayFundLogs.add(dLog);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            for (BusinessDayCardDisburseLog dayCardDisburseLog : dayFundLogs) {
                BusinessDayCardDisburseLog businessDayCardEarnLog = businessDayCardDisburseLogService.getOne(new LambdaQueryWrapper<BusinessDayCardDisburseLog>()
                        .eq(BusinessDayCardDisburseLog::getBusinessId, dayCardDisburseLog.getBusinessId())
                        .eq(BusinessDayCardDisburseLog::getStatDate, dayCardDisburseLog.getStatDate())
                        .eq(BusinessDayCardDisburseLog::getCardModel, dayCardDisburseLog.getCardModel()));
                if (null == businessDayCardEarnLog) {
                    businessDayCardDisburseLogService.save(dayCardDisburseLog);
                } else {
                    dayCardDisburseLog.setId(businessDayCardEarnLog.getId());
                    businessDayCardDisburseLogService.updateById(dayCardDisburseLog);
                }
            }
        }
    }
}




