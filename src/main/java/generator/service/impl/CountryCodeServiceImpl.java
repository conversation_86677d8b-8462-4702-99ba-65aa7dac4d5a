package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.CountryCode;
import generator.service.CountryCodeService;
import generator.mapper.CountryCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_country_code】的数据库操作Service实现
* @createDate 2024-07-26 16:19:59
*/
@Service
public class CountryCodeServiceImpl extends ServiceImpl<CountryCodeMapper, CountryCode>
    implements CountryCodeService{
    @Autowired
    CountryCodeMapper mapper;

    /**
     * 查询kyc国籍编码
     * @param country 国籍
     * @return list
     */
    @Override
    public List<CountryCode> country(String country) {
        return mapper.country(country);
    }
}




