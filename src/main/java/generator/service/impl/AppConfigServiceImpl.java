package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.AppConfigService;
import generator.domain.AppConfig;
import generator.mapper.AppConfigMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class AppConfigServiceImpl extends ServiceImpl<AppConfigMapper, AppConfig> implements AppConfigService {

}