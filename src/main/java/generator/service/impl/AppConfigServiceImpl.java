package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.AppConfig;
import generator.service.AppConfigService;
import generator.mapper.AppConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_app_config】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Service
public class AppConfigServiceImpl extends ServiceImpl<AppConfigMapper, AppConfig>
        implements AppConfigService {

    @Override
    public AppConfig selectByKey(String mixDepositAmount) {
        return baseMapper.selectByKey(mixDepositAmount);
    }
}




