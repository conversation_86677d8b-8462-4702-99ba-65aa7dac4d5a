package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PlatformDayInOutLogService;
import generator.domain.PlatformDayInOutLog;
import generator.mapper.PlatformDayInOutLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PlatformDayInOutLogServiceImpl extends ServiceImpl<PlatformDayInOutLogMapper, PlatformDayInOutLog> implements PlatformDayInOutLogService {

}