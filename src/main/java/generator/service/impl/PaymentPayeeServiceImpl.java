package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.PaymentPayeeSaveDto;
import com.qf.zpay.dto.req.PaymentPayeeVO;
import com.qf.zpay.dto.req.v2.PayeeDto;
import com.qf.zpay.util.DateUtil;
import generator.domain.PaymentPayConfig;
import generator.domain.PaymentPayee;
import generator.domain.User;
import generator.mapper.PaymentPayeeMapper;
import generator.service.PaymentPayConfigService;
import generator.service.PaymentPayeeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 支付收款人信息 Service 实现类
 */
@Service
public class PaymentPayeeServiceImpl extends ServiceImpl<PaymentPayeeMapper, PaymentPayee> implements PaymentPayeeService {

    @Autowired
    private PaymentPayeeMapper mapper;
    @Autowired
    PaymentPayConfigService paymentPayConfigService;


    /**
     * 删除收款人
     *
     * @param id 收款人id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletePayee(Integer id) {
        PaymentPayee paymentPayee = mapper.selectById(id);
        paymentPayee.setStatus(1);
        mapper.updateById(paymentPayee);
    }

    /**
     * 查询收款人
     *
     * @param lastName 姓
     * @return 收款人列表
     */
    @Override
    public List<PaymentPayeeVO> getPayee(Integer businessId, String lastName, String phone, String accountType) {
        List<PaymentPayeeVO> result = mapper.getPayee(businessId, lastName, phone, accountType);
        for (PaymentPayeeVO payee : result) {
            Map<String, String> countries = new HashMap<>();
            for (String currency : Arrays.asList(payee.getCurrency().split(","))) {
                countries.put(currency, payee.getPayeeId());
            }
            payee.setCountries(countries);
        }
        return result;
    }


    /**
     * 查询收款人
     *
     * @param payeeId 收款人payeeId
     * @return 收款人信息
     */
    @Override
    public PayeeDto queryPayeeByPayeeId(String payeeId) {
        return mapper.queryPayeeByPayeeId(payeeId);
    }

    @Override
    public PaymentPayee selectByPayeeId(String payeeId) {
        return mapper.selectByPayeeId(payeeId);
    }

    @Override
    public PaymentPayee updatePayeeV2(PaymentPayeeSaveDto payeeRequest) {
        PaymentPayee paymentPayee = new PaymentPayee();
        BeanUtils.copyProperties(payeeRequest, paymentPayee);
        PaymentPayee one = mapper.selectByPayeeId(paymentPayee.getPayeeId());
        paymentPayee.setId(one.getId());
        mapper.updateById(paymentPayee);
        paymentPayee.setTwoLetterCode(paymentPayee.getCountry());
        paymentPayee.setAccountType(one.getAccountType());
        return paymentPayee;
    }

    @Override
    public PaymentPayee getByAccountType(String paymentMethod, User user, String accountNo) {
        HashMap<String, String> productType = paymentPayConfigService.getProductType(paymentMethod);
        QueryWrapper<PaymentPayee> paymentPayeeQueryWrapper = new QueryWrapper<>();
        paymentPayeeQueryWrapper.eq("account_type", "E_WALLET");
        paymentPayeeQueryWrapper.eq("currency", productType.get("currency"));
        paymentPayeeQueryWrapper.eq("account_no", accountNo);
        PaymentPayee paymentPayee = null;//mapper.selectOne(paymentPayeeQueryWrapper);
        if (paymentPayee == null) {
            List<PaymentPayConfig> list = paymentPayConfigService.getCurrencyAndCountry(productType.get("currency"), productType.get("country"));
            if (!list.isEmpty()) {
                PaymentPayConfig paymentPayConfig = list.get(0);
                paymentPayee = mapper.selectById(289);
                paymentPayee.setId(null);
                paymentPayee.setBusinessId(user.getBusinessId());
                paymentPayee.setPayConfigId(paymentPayConfig.getId());
                paymentPayee.setCountry(paymentPayConfig.getCountryRegion());
                paymentPayee.setCurrency(paymentPayConfig.getSettlementCurrency());
                paymentPayee.setTwoLetterCode(paymentPayConfig.getTwoLetterCode());
                paymentPayee.setEnglishNameCountry(paymentPayConfig.getEnglishNameCountry());
                paymentPayee.setCreateTime(DateUtil.getUTCDate());
            }
        }
        paymentPayee.setAccountNo(accountNo);
        return paymentPayee;
    }

    @Override
    public PaymentPayee getPayeeByFirstNameAndLastName(String firstName, String lastName) {
        QueryWrapper<PaymentPayee> paymentPayeeQueryWrapper = new QueryWrapper<>();
        paymentPayeeQueryWrapper.eq("first_name", firstName);
        paymentPayeeQueryWrapper.eq("last_name", lastName);
        paymentPayeeQueryWrapper.eq("two_letter_code", "CN");
        paymentPayeeQueryWrapper.notIn("id", 289);
        return mapper.selectOne(paymentPayeeQueryWrapper);
    }

}