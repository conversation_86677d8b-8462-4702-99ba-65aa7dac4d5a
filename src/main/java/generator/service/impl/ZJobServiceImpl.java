package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.ZJob;
import generator.service.ZJobService;
import generator.mapper.ZJobMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_z_job(任务配置)】的数据库操作Service实现
 * @createDate 2024-10-16 14:20:34
 */
@Service
public class ZJobServiceImpl extends ServiceImpl<ZJobMapper, ZJob>
        implements ZJobService {

    @Override
    public List<ZJob> getEnableJobList() {
        return this.list(new LambdaQueryWrapper<>(new ZJob()).eq(ZJob::getStatus, true));
    }
}




