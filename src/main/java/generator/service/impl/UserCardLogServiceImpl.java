package generator.service.impl;


import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.CardTransactionStatementDto;
import com.qf.zpay.dto.req.UserCardLogDto;
import com.qf.zpay.dto.req.UserCardTradeStatDto;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.UserCardLog;
import generator.mapper.UserCardLogMapper;
import generator.service.UserCardLogService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Locale;


/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_log】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Log4j2
@Service
public class UserCardLogServiceImpl extends ServiceImpl<UserCardLogMapper, UserCardLog>
        implements UserCardLogService {

    @Autowired
    private UserCardLogMapper userCardLogMapper;

    /**
     * 查询卡片流水分页查询的结果
     *
     * @param businessId
     * @param cardId
     * @param page
     * @param pageSize
     * @param transactionType
     * @return
     */
    @Override
    public IPage<UserCardLog> cardLogs(Integer businessId, String cardId, Integer page, Integer pageSize, String transactionType) {
        // 构建查询条件
        QueryWrapper<UserCardLog> queryWrapper = new QueryWrapper<>();
        if (businessId != null) {
            queryWrapper.eq("business_id", businessId);
        }
        if (cardId != null && !cardId.isEmpty()) {
            queryWrapper.eq("card_id", cardId);
        }
        if (transactionType != null && !transactionType.isEmpty()) {
            queryWrapper.eq("transaction_type", transactionType);
        }

        // 构建分页对象
        Page<UserCardLog> userCardLogPage = new Page<>(page, pageSize);

        // 执行分页查询
        IPage<UserCardLog> iPage = userCardLogMapper.selectPage(userCardLogPage, queryWrapper);

        // 返回查询结果
        return iPage;

    }

    @Override
    public BigDecimal queryCardAmount(String formattedDate, String noticeType, Integer businessId, String status) {
        return userCardLogMapper.queryCardAmount(formattedDate, noticeType, businessId, status);
    }

    /**
     * 交易流水分页数据
     *
     * @param businessId 商户id
     * @param cardNumber 卡号
     * @param page       当前页
     * @param pageSize   条数
     * @return 分页数据
     */
    @Override
    public IPage<UserCardLog> cardLogsDetail(Integer businessId, String cardNumber, Integer page, Integer pageSize) {
        // 构建查询条件
        QueryWrapper<UserCardLog> queryWrapper = new QueryWrapper<>();
        if (businessId != null) {
            queryWrapper.eq("business_id", businessId);
        }
        if (cardNumber != null && !cardNumber.isEmpty()) {
            queryWrapper.eq("card_id", cardNumber);
        }
        queryWrapper.orderByDesc("create_time");
        // 构建分页对象
        Page<UserCardLog> userCardLogPage = new Page<>(page, pageSize);

        // 执行分页查询返回查询结果
        return userCardLogMapper.selectPage(userCardLogPage, queryWrapper);
    }

    /**
     * 卡片交易详情
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片交易分页数据详情
     */
    @Override
    public IPage<UserCardLogDto> getCardTransactionDetails(CardSettingsDto cardSettingsDto) {
        Page<UserCardLogDto> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return userCardLogMapper.getCardTransactionDetails(page, cardSettingsDto);
    }

    /**
     * 卡片明细
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片明细数据详情
     */
    @Override
    public IPage<UserCardLogDto> getShareCardsDetails(CardSettingsDto cardSettingsDto) {
        Page<UserCardLogDto> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return userCardLogMapper.getShareCardsDetails(page, cardSettingsDto);
    }

    /**
     * 共享or储值卡下载导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @Override
    public void export(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        List<CardTransactionStatementDto> list = getTransactionStatementDtos(cardSettingsDto);

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("createTime", ZHelperUtil.getI18nMessages("card_createTime"));
        writer.addHeaderAlias("cardNumber", ZHelperUtil.getI18nMessages("card_cardNumber"));
        writer.addHeaderAlias("fundsDirection", ZHelperUtil.getI18nMessages("card_fundsDirection"));
        writer.addHeaderAlias("authAmountCurrency", ZHelperUtil.getI18nMessages("card_authAmountCurrency"));
        writer.addHeaderAlias("transAmount", ZHelperUtil.getI18nMessages("card_transAmount"));
        writer.addHeaderAlias("authAmount", ZHelperUtil.getI18nMessages("card_authAmount"));
        writer.addHeaderAlias("status", ZHelperUtil.getI18nMessages("card_status"));
        writer.merge(6, ZHelperUtil.getI18nMessages("card_fileTitle"));
        // 设置标题
        String fileName = getCardModel(cardSettingsDto.getCardModel()) + ".xlsx";
        extracted(response, writer, list, fileName);
    }

    private List<CardTransactionStatementDto> getCardTransactionStatementDtosEn(CardSettingsDto cardSettingsDto) {
        List<CardTransactionStatementDto> list = userCardLogMapper.exportEn(cardSettingsDto);
        list.forEach(dto -> {
            if (dto.getTransAmount() == null) {
                dto.setTransAmount(dto.getAuthAmount());
            }
            if (dto.getFailureReason().isEmpty()) {
                dto.setFailureReason("succeed");
            }
        });
        return list;
    }

    /**
     * 卡片交易详情导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    @Override
    public void exportCardTransactionDetails(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        List<CardTransactionStatementDto> list = getTransactionStatementDtos(cardSettingsDto);
        for (CardTransactionStatementDto dto : list) {
            dto.setTransAmountStr(String.format("%s(%s)", String.valueOf(dto.getTransAmount()), dto.getTransAmountCurrency() == null ? "USD" : dto.getTransAmountCurrency()));
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("createTime", ZHelperUtil.getI18nMessages("card_createTime"));
        writer.addHeaderAlias("orderId", ZHelperUtil.getI18nMessages("orderId"));
        writer.addHeaderAlias("cardNumber", ZHelperUtil.getI18nMessages("cardNumber"));
        writer.addHeaderAlias("cardId", ZHelperUtil.getI18nMessages("cardId"));
        writer.addHeaderAlias("cardNickname", ZHelperUtil.getI18nMessages("cardNickname"));
        writer.addHeaderAlias("cardModel", ZHelperUtil.getI18nMessages("cardModel"));
        writer.addHeaderAlias("transactionType", ZHelperUtil.getI18nMessages("transactionType"));
        writer.addHeaderAlias("transAmountStr", ZHelperUtil.getI18nMessages("card_transAmount"));
        writer.addHeaderAlias("authAmount", ZHelperUtil.getI18nMessages("card_settleAmount_usd"));
        writer.addHeaderAlias("status", ZHelperUtil.getI18nMessages("status"));
        writer.addHeaderAlias("failureReason", ZHelperUtil.getI18nMessages("card_failureReason"));
        writer.addHeaderAlias("businessName", ZHelperUtil.getI18nMessages("card_businessName"));
        writer.merge(11, ZHelperUtil.getI18nMessages("card_fileTitle2"));
        String fileName = ZHelperUtil.getI18nMessages("card_fileTitle2") + ".xlsx";
        extracted(response, writer, list, fileName);
    }

    private List<CardTransactionStatementDto> getTransactionStatementDtos(CardSettingsDto cardSettingsDto) {
        Locale currentLocale = LocaleContextHolder.getLocale();
        List<CardTransactionStatementDto> list;
        // 判断语言环境
        if (currentLocale.getLanguage().equals("en")) {
            list = getCardTransactionStatementDtosEn(cardSettingsDto);
        } else if (currentLocale.getLanguage().equals("zh")) {
            list = getCardTransactionStatementDtos(cardSettingsDto);
        } else {
            list = getCardTransactionStatementDtosEn(cardSettingsDto);
        }
        return list;
    }

    /**
     * 卡片交易成功失败详情
     *
     * @param orderId 订单号
     * @param cardId  卡片id
     * @return 订单详情
     */
    @Override
    public List<UserCardLog> selectById(String orderId, String cardId) {
        QueryWrapper<UserCardLog> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("order_id", orderId);
        objectQueryWrapper.eq("card_id", cardId);
        return userCardLogMapper.selectList(objectQueryWrapper);
    }

    private List<CardTransactionStatementDto> getCardTransactionStatementDtos(CardSettingsDto cardSettingsDto) {
        List<CardTransactionStatementDto> list = userCardLogMapper.export(cardSettingsDto);
        list.forEach(dto -> {
            if (dto.getTransAmount() == null) {
                dto.setTransAmount(dto.getAuthAmount());
            }
            if (dto.getFailureReason().isEmpty()) {
                dto.setFailureReason("成功");
            }
        });
        return list;
    }


    private static void extracted(HttpServletResponse response, ExcelWriter writer, List<CardTransactionStatementDto> list, String fileName) throws IOException {
        //只保留别名的数据
        writer.setOnlyAlias(true);
        // 默认配置
        writer.write(list, true);
        // 设置单元格样式
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < list.size(); i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 20 / 10);
        }

        // 设置content—type
        response.setContentType("application/vnd.ms-excel;charset=utf-8");

        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Content-Disposition", "attachment;filename=" + Base64.getEncoder().encodeToString(fileName.getBytes("UTF-8")));

        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }


    public String getCardModel(String cardModel) {
        // Standard:标准卡,ShareBalance:共享卡
        return switch (cardModel) {
            case "recharge" -> ZHelperUtil.getI18nMessages("fileTitle_prepaid");
            case "share" -> ZHelperUtil.getI18nMessages("fileTitle_shared");
            default -> ZHelperUtil.getI18nMessages("fileTitle_physical");
        };
    }

    public IPage<UserCardLog> cardLogPage(
            String cardId,
            Integer page,
            Integer size
    ) {
        QueryWrapper<UserCardLog> query = new QueryWrapper<>();
        query.eq("card_id", cardId);
        query.orderByDesc("id");
        Page<UserCardLog> cardLogPage = new Page<>(page, size);
        return userCardLogMapper.selectPage(cardLogPage, query);
    }


    public List<UserCardLog> cardLogDetail(String orderId) {
        QueryWrapper<UserCardLog> query = new QueryWrapper<>();
        query.eq("order_id", orderId);
        return userCardLogMapper.selectList(query);
    }

    @Override
    public Boolean isOrderIdExists(String channelOrderNo) {
        QueryWrapper<UserCardLog> query = new QueryWrapper<>();
        query.eq("order_id", channelOrderNo);
        // 查询数据库中是否存在该 order_id
        Long count = userCardLogMapper.selectCount(query);
        return count > 0L;
    }

    @Override
    public void transFeeSave(UserCardLog userCardLog, BigDecimal platformFee, String fundsDirection) {
        // 写日志记录 平台手续费
        userCardLog.setId(null);
        userCardLog.setTransAmountCurrency(null);
        userCardLog.setProductCode(null);
        userCardLog.setTransAmount(null);
        userCardLog.setAuthAmount(platformFee);
        userCardLog.setSettledAmount(platformFee);
        userCardLog.setFundsDirection(fundsDirection);
        userCardLog.setTransactionType("TransFee");
        userCardLogMapper.insert(userCardLog);
    }

    @Override
    public UserCardTradeStatDto cardTransactionDetailsStat(CardSettingsDto dto) {
        List<String> list = Arrays.asList("Auth");
        List<UserCardLog> userCardLogs = userCardLogMapper.selectList(new LambdaQueryWrapper<UserCardLog>()
                .eq(UserCardLog::getBusinessId, dto.getBusinessId())
                .in(UserCardLog::getTransactionType, list)
                .between(dto.getDateRange() != null, UserCardLog::getCreateTime, dto.getDateRange(), dto.getEndDate()));
        if (userCardLogs != null && userCardLogs.isEmpty()) {
            return new UserCardTradeStatDto(0L, 0L, 0.0, 0.0);
        }
        int totalCount = userCardLogs.size();
        UserCardTradeStatDto statDto = new UserCardTradeStatDto();
        long success = userCardLogs.stream().filter(u -> "AuthSuccessed".equals(u.getStatus()) || "Settled".equals(u.getStatus())).count();
        long failure = userCardLogs.stream().filter(u -> "AuthFailure".equals(u.getStatus())).count();
        // 计算占比（保留两位小数）
        double successPercentage = totalCount > 0 ?
                Math.round((success * 100.0 / totalCount) * 100.0) / 100.0 : 0.0;

        double failurePercentage = totalCount > 0 ?
                Math.round((failure * 100.0 / totalCount) * 100.0) / 100.0 : 0.0;
        statDto.setAuthFailureSum(failure);
        statDto.setAuthSuccessSum(success);
        statDto.setAuthFailureRate(failurePercentage);
        statDto.setAuthSuccessRate(successPercentage);
        return statDto;
    }

}




