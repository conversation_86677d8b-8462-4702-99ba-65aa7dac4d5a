package generator.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.common.CodeDataDto;
import com.qf.zpay.dto.req.Code2TokenDto;
import com.qf.zpay.dto.req.SigninDto;
import com.qf.zpay.dto.req.UserInfoDto;
import com.qf.zpay.dto.req.UserPasswordDto;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.JsonResult;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.VerifyCodeService;
import generator.domain.User;
import generator.domain.UserDevConfig;
import generator.domain.UserWallet;
import generator.mapper.UserDevConfigMapper;
import generator.mapper.UserMapper;
import generator.mapper.UserWalletMapper;
import generator.service.PaymentUserConfigService;
import generator.service.UserCardManageService;
import generator.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【tb_user】的数据库操作Service实现
 * @createDate 2024-06-03 10:50:58
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

//    private final Logger Log = LoggerFactory.getLogger(this.getClass());

    private final UserMapper userMapper;

    @Autowired
    private VerifyCodeService codeService;

    @Autowired
    private UserDevConfigMapper userDevConfigMapper;

    @Autowired
    RsaService rsaService;

    @Autowired
    UserWalletMapper userWalletMapper;

    @Resource
    private UserCardManageService userCardManageService;

    @Resource
    private PaymentUserConfigService paymentUserConfigService;

    public UserServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }


    @Override
    public JsonResult signinByEmail(SigninDto signinDto) {
        User userOne = this.userMapper.getOneByEmail(signinDto.getEmail());
        if (userOne != null) {
            return JsonResult.ko(ResultCode.USER_EXIST);
        }
        String uuid = UUID.randomUUID().toString();
        String salt = uuid.substring(0, 6);
        Integer number = RandomUtil.randomInt(100000000, 999999999);
        BigDecimal zero = BigDecimal.ZERO;
        BigDecimal one = BigDecimal.valueOf(1);
        User user = new User();
        user.setEmail(signinDto.getEmail());
        user.setPassword(DigestUtil.md5Hex(signinDto.getPassword() + salt));
        user.setSalt(salt);
        user.setStatus(1);
        user.setMoney(zero);
        user.setShareBalance(zero);
        user.setBusinessId(number);
        user.setBusinessName(signinDto.getBusinessName());
        user.setBusinessNo("1");
        user.setBusinessStatus(1);
        user.setRechargeRate(one);
        user.setWithdrawRate(one);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        user.setSource(2);
        boolean save = this.save(user);
        if (save) {
            userCardManageService.initFeeConfig(user.getId());
            paymentUserConfigService.initChannel(user.getId());
        }
        return JsonResult.ok(save);
    }

    @Override
    public User loginByEmail(String email, String password, String ip) {
        User user = this.userMapper.getOneByEmail(email);
        if (user == null) {
            Assert.fail(ResultCode.USER_NOT_EXIST);
        }
        if (!DigestUtil.md5Hex(password + user.getSalt()).equals(user.getPassword())) {
            Assert.fail(ResultCode.USER_PASSWORD_ERROR);
        }
        user.setLastLoginTime(new Date());
        user.setLastLoginIp(ip);
        this.updateById(user);
        return user;
    }

    @Override
    public JsonResult<Object> forgotPassword(SigninDto signinDto) {
        User user = this.userMapper.getOneByEmail(signinDto.getEmail());
        String uuid = UUID.randomUUID().toString();
        String salt = uuid.substring(0, 6);
        if (user == null) {
            return JsonResult.ko(ResultCode.USER_NOT_EXIST);
        }
        user.setSalt(salt);
        user.setPassword(DigestUtil.md5Hex(signinDto.getPassword() + salt));
        boolean save = this.saveOrUpdate(user);
        if (save) {
            userCardManageService.initFeeConfig(user.getId());
            paymentUserConfigService.initChannel(user.getId());
        }
        return JsonResult.ok(save);
    }


    @Override
    public User code2user(Code2TokenDto codeDto) {
        UserDevConfig userDevConfig = this.userDevConfigMapper.getOneByAppId(codeDto.getAppId());
        if (userDevConfig == null) {
            Assert.fail(ResultCode.USER_NOT_EXIST);
        }
        User user = this.userMapper.selectById(userDevConfig.getUserId());
        CodeDataDto codeDataDto = rsaService.code2data(user.getId(), codeDto.getCode());
        if (!codeDataDto.getAppId().equals(userDevConfig.getAppId())
                || !codeDataDto.getAppSecret().equals(userDevConfig.getAppSecret())) {
            Assert.fail(ResultCode.PARAM_ERR);
        }
        return user;
    }

    @Override
    public User getUserByBusinessId(Integer businessId) {
        return userMapper.getOneByBusinessId(businessId);
    }


    @Override
    public List<User> queryAllUser() {
        return userMapper.selectAll();
    }

    /**
     * 验证二级密码
     *
     * @param id       用户id
     * @param password 二级密码
     * @return true or false
     */
    @Override
    public Boolean verify(Integer id, String password) {
        User user = userMapper.getOneById(id);
        if (!DigestUtil.md5Hex(password).equals(user.getPayPassword())) {
            Assert.fail(ResultCode.USER_PASSWORD_ERROR);
        }
        return Boolean.TRUE;
    }

    /**
     * 修改密码
     *
     * @param user            商户
     * @param userPasswordDto 密码数据dto
     * @return true
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changePassword(User user, UserPasswordDto userPasswordDto) {
        User user2 = userMapper.getOneById(user.getId());
        // 校验验证码
        boolean res = codeService.verifyCode(user2.getEmail(), userPasswordDto.getCode());
        if (!res) {
            throw new ApiException(ResultCode.VCODE_ERR);
        }
        // 确认密码
        if (!userPasswordDto.getPassword().equals(userPasswordDto.getVerifyPassword())) {
            throw new ApiException(ResultCode.USER_PASSWORD_ERROR);
        }
        // 修改密码
        String s = DigestUtil.md5Hex(userPasswordDto.getPassword() + user2.getSalt());
        user2.setPassword(s);
        userMapper.updateById(user2);
        return Boolean.TRUE;
    }

    /**
     * 修改二级密码
     *
     * @param user2           商户
     * @param userPasswordDto 密码数据dto
     * @return true
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changeSecondaryPassword(User user2, UserPasswordDto userPasswordDto) {
        // 校验验证码
        boolean res = codeService.verifyCode(user2.getEmail(), userPasswordDto.getCode());
        if (!res) {
            throw new ApiException(ResultCode.VCODE_ERR);
        }
        // 判断登录密码
        if (!userPasswordDto.getPassword().equals(userPasswordDto.getVerifyPassword())) {
            throw new ApiException(ResultCode.USER_PASSWORD_ERROR);
        }
        // 修改二级密码
        String s = DigestUtil.md5Hex(userPasswordDto.getPassword());
        user2.setPayPassword(s);
        userMapper.updateById(user2);
        return Boolean.TRUE;
    }

    /**
     * 商户详情接口
     *
     * @param user2 商户
     * @return 商户详情
     */
    @Override
    public UserInfoDto userInfo(User user2) {
        UserDevConfig userDev = userDevConfigMapper.getOneByUserId(user2.getId());
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setBusinessId(user2.getBusinessId());
        userInfoDto.setBusinessName(user2.getBusinessName());
        userInfoDto.setEmail(user2.getEmail());
        userInfoDto.setAppId(userDev.getAppId());
        userInfoDto.setAppSecret(userDev.getAppSecret());
        userInfoDto.setEnableModule(JSONUtil.parseArray(userDev.getEnableModule()));

        //获取公钥
        RMap<String, String> userRsaPair = rsaService.getUserRsaPair(user2.getId());
        String s = userRsaPair.get("public");
        userInfoDto.setSecretKey(s.substring(0, 10));
        return userInfoDto;
    }

    /**
     * 下载公钥
     *
     * @param user2    商户
     * @param response 响应数据
     */
    @Override
    public void writePemFile(User user2, HttpServletResponse response) {
        RMap<String, String> userRsaPair = rsaService.getUserRsaPair(user2.getId());
        String pemData = userRsaPair.get("public");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=public_key.pem");

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream()))) {
            // 写入 PEM 头部
            writer.write("-----BEGIN PUBLIC KEY-----");
            writer.newLine();

            // 将 PEM 数据按每行 64 个字符的格式写入
            int lineLength = 64;
            for (int i = 0; i < pemData.length(); i += lineLength) {
                int endIndex = Math.min(i + lineLength, pemData.length());
                writer.write(pemData.substring(i, endIndex));
                writer.newLine();
            }

            // 写入 PEM 尾部
            writer.write("-----END PUBLIC KEY-----");
            writer.newLine();
        } catch (IOException e) {
            throw new RuntimeException("写入 PEM 文件时出错: " + e.getMessage());
        }
    }


    @Override
    public UserWallet getWallet(Integer businessId) {
        return userWalletMapper.getOneByBusinessId(businessId);
    }

}




