package generator.service.impl;


import com.mybatisflex.spring.service.impl.ServiceImpl;
import generator.domain.User;
import generator.mapper.UserMapper;
import generator.service.UserService;
import org.springframework.stereotype.Service;

import static generator.domain.table.UserTableDef.USER;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public User getUserByBusinessId(Integer businessId) {
        return this.queryChain()
                .from(USER)
                .where(USER.BUSINESS_ID.eq(businessId))
                .one();
    }
}