package generator.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.res.v2.AdvanceFlowDto;
import com.qf.zpay.dto.res.v2.JournalAccountDto;
import generator.domain.UserWalletAdvanceLog;
import generator.mapper.UserWalletAdvanceLogMapper;
import generator.service.UserCardService;
import generator.service.UserWalletAdvanceLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserWalletAdvanceLog(卡片操作日志)】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Service
public class UserWalletAdvanceLogServiceImpl extends ServiceImpl<UserWalletAdvanceLogMapper, UserWalletAdvanceLog> implements UserWalletAdvanceLogService {

    @Autowired
    private UserCardService userCardService;

    @Override
    public AdvanceFlowDto advanceFlowStatistics(Integer businessId) {
        return this.baseMapper.advanceFlowStatistics(businessId);
    }

    @Override
    public IPage<UserWalletAdvanceLog> advancePage(JournalAccountDto dto, Integer businessId) {
        int pageSize = dto.getPageSize();
        int pageIndex = dto.getPageIndex();
        List<UserWalletAdvanceLog> userWalletAdvanceLogs = this.baseMapper.advancePage(dto, businessId);
        Page<UserWalletAdvanceLog> pzRouteColorPage = new Page<>(pageIndex, pageSize);
        pzRouteColorPage.setTotal(userWalletAdvanceLogs.size());
        int startIndex = ((pageIndex - 1) * pageSize);
        if (null == userWalletAdvanceLogs || userWalletAdvanceLogs.isEmpty() || startIndex > userWalletAdvanceLogs.size()) {
            pzRouteColorPage.setRecords(null);
        } else {
            int toIndex = (pageIndex * pageSize);
            pzRouteColorPage.setRecords(userWalletAdvanceLogs.subList(startIndex, toIndex > userWalletAdvanceLogs.size() ? userWalletAdvanceLogs.size() : toIndex));
        }
        return pzRouteColorPage;
    }
}






