package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.response.ResultCode;
import generator.domain.PaymentUserConfig;
import generator.domain.User;
import generator.mapper.PaymentUserConfigMapper;
import generator.mapper.UserMapper;
import generator.service.PaymentUserConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PaymentUserConfigServiceImpl extends ServiceImpl<PaymentUserConfigMapper, PaymentUserConfig> implements PaymentUserConfigService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public PaymentUserConfig updateUserConfig(PaymentUserConfig dto) {
        int fee = dto.getBusinessFee().compareTo(dto.getPlatformFee());
        if (fee < 0) {
            throw new ApiException(ResultCode.SERVICE_CHARGE_IS_TOO_SMALL);
        }
        int feeRate = dto.getBusinessFeeRate().compareTo(dto.getPlatformFeeRate());
        if (feeRate < 0) {
            throw new ApiException(ResultCode.RATE_IS_TOO_SMALL);
        }
        this.saveOrUpdate(dto);
        return dto;
    }

    @Override
    public void initChannel(Integer id) {
        User user = userMapper.getOneById(id);
        List<PaymentUserConfig> configTemps = this.list(new LambdaQueryWrapper<PaymentUserConfig>().eq(PaymentUserConfig::getBusinessId, 0));
        for (PaymentUserConfig config : configTemps) {
            long count = this.count(new LambdaQueryWrapper<PaymentUserConfig>().eq(PaymentUserConfig::getBusinessId, user.getBusinessId())
                    .eq(PaymentUserConfig::getChannelId, config.getChannelId()));
            if (count > 0) {
                continue;
            }
            PaymentUserConfig userConfig = new PaymentUserConfig();
            BeanUtils.copyProperties(config, userConfig);
            userConfig.setId(null);
            userConfig.setBusinessId(user.getBusinessId());
            this.save(userConfig);
        }
    }
}