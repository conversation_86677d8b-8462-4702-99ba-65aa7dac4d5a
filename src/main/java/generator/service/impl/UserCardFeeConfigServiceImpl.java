package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.UserCardFeeConfigService;
import generator.domain.UserCardFeeConfig;
import generator.mapper.UserCardFeeConfigMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 卡片费率配置表 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserCardFeeConfigServiceImpl extends ServiceImpl<UserCardFeeConfigMapper, UserCardFeeConfig> implements UserCardFeeConfigService {

}