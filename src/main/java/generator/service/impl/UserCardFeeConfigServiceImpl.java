package generator.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.res.v1.UserCardFeeConfigWithCardManageDTO;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import generator.domain.UserCardCategoryConfig;
import generator.domain.UserCardFeeConfig;
import generator.domain.UserCardManage;
import generator.mapper.UserCardCategoryConfigMapper;
import generator.mapper.UserCardFeeConfigMapper;
import generator.service.UserCardFeeConfigService;
import generator.service.UserCardService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_fee_config(卡片费率配置表)】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserCardFeeConfigServiceImpl extends ServiceImpl<UserCardFeeConfigMapper, UserCardFeeConfig>
        implements UserCardFeeConfigService {

    @Autowired
    private UserCardFeeConfigMapper userCardFeeConfigMapper;
    @Autowired
    private UserCardService UserCardService;
    @Autowired
    private UserCardCategoryConfigMapper userCardCategoryConfigMapper;

    /**
     * 根据businessId获取可用的卡片产品列表
     * 根据businessId查询UserCardFeeConfig表获得cardManageId，根据cardManageId查询UserCardManage表的多表联查，根据dto返回全部字段
     *
     * @param businessId
     * @return
     */
    @Override
    public List<UserCardFeeConfigWithCardManageDTO> ableCardProductList(String businessId) {
        // 根据businessId获取可用卡产品列表,根据businessId查询UserCardFeeConfig表获得cardManageId，根据cardManageId查询UserCardManage表的多表联查，根据dto返回全部字段
        return userCardFeeConfigMapper.getUsableCardProductListByBusinessId(Integer.valueOf(businessId));
    }

    /**
     * 根据businessId和cardManageId获取商户费率
     *
     * @param businessId
     * @param cardManageId
     * @return
     */
    @Override
    public UserCardFeeConfigWithCardManageDTO getRate(String businessId, String cardManageId) {
        // 根据businessId和cardManageId查询UserCardFeeConfig表
        return userCardFeeConfigMapper.getRateByBusinessIdAndCardManageId(Integer.valueOf(businessId), Integer.valueOf(cardManageId));
    }

    /**
     * 可用卡数量接口
     *
     * @param cardSettingsDto 筛选条件
     * @return userCardFeeConfigDtoIPage 可用卡数量详情
     */
    @Override
    public IPage<UserCardFeeConfigDto> inquireNumberOfAvailableCards(CardSettingsDto cardSettingsDto) {
        Page<UserCardFeeConfigDto> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        IPage<UserCardFeeConfigDto> userCardFeeConfigDtoIPage = userCardFeeConfigMapper.inquireNumberOfAvailableCards(page, cardSettingsDto);
        List<UserCardFeeConfigDto> records = userCardFeeConfigDtoIPage.getRecords();
        for (UserCardFeeConfigDto record : records) {
            List<Integer> list = UserCardService.getUserCardlist(record.getCardDuan(), cardSettingsDto.getBusinessId());
            record.setNumberOfCardsIssued(list.size());
        }
        // 根据 cardSettingsDto.getSort() 的值对 records 列表进行排序
        if (cardSettingsDto.getSort() == 1) {
            // 降序排列
            records.sort(Comparator.comparing(UserCardFeeConfigDto::getNumberOfCardsIssued).reversed());
        } else if (cardSettingsDto.getSort() == 2) {
            // 升序排列
            records.sort(Comparator.comparing(UserCardFeeConfigDto::getNumberOfCardsIssued));
        }
        return userCardFeeConfigDtoIPage.setRecords(records);
    }

    @Override
    public UserCardFeeConfig getAndCheckCardFeeConfig(Integer cardManageId, Integer businessId) {
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.getByBusinessIdAndCardManageId(businessId, cardManageId);
        if (userCardFeeConfig == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }
        if (!userCardFeeConfig.getIsAble().equals(1)) {
            Assert.fail(ResultCode.CARD_MANAGE_DISABLED);
        }
        return userCardFeeConfig;
    }


    @Override
    public UserCardFeeConfig getCardFeeConfig(Integer cardManageId, Integer businessId) {
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.getByBusinessIdAndCardManageId(businessId, cardManageId);
        if (userCardFeeConfig == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }
        return userCardFeeConfig;
    }


    /**
     * 卡段列表
     *
     * @param cardModel 卡段模式
     * @return 卡段列表分页数据
     */
    @Override
    public List<CardsSlotDto> listOfCardsSlot(Integer businessId, String cardModel) {
        return userCardFeeConfigMapper.listOfCardsSlot(businessId, cardModel);
    }

    /**
     * 剩余可用卡数量
     *
     * @param businessId 商户id
     * @return list
     */
    @Override
    public List<UserCardFeeConfigDto> numberOfAvailableCards(Integer businessId) {
        List<UserCardFeeConfigDto> list = userCardFeeConfigMapper.numberOfAvailableCards(businessId);
        for (UserCardFeeConfigDto record : list) {
            List<Integer> list2 = UserCardService.getUserCardlist(record.getCardDuan(), businessId);
            record.setNumberOfCardsIssued(list2.size());
        }
        return list;
    }

    /**
     * 可用卡段列表
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @Override
    public IPage<UserCardFeeConfigDetailVo> segmentSettingsList(CardSettingsDto cardSettingsDto) {
        Page<UserCardFeeConfigVo> objectPage = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        IPage<UserCardFeeConfigVo> userCardFeeConfigVoIPage = userCardFeeConfigMapper.segmentSettingsList(objectPage, cardSettingsDto);
        List<UserCardFeeConfigVo> records = userCardFeeConfigVoIPage.getRecords();

        // 获取卡段配置
        Map<Integer, UserCardCategoryConfig> userCardCategoryConfigMap = records.stream()
                .map(UserCardFeeConfigVo::getUserCardManage)
                .map(UserCardManage::getCardCategory)
                .distinct()
                .collect(Collectors.toMap(
                        Function.identity(),
                        id -> userCardCategoryConfigMapper.selectById(id),
                        (existing, newValue) -> existing
                ));

        records.forEach(record -> {
            //查询卡段的卡
            List<Integer> list2 = UserCardService.getUserCardlist(record.getCardDuan(), cardSettingsDto.getBusinessId());
            // 设置卡段开卡数
            record.setNumberOfCardsIssued(list2.size());
            // 设置卡段产品及支持结算币种
            UserCardCategoryConfig userCardCategoryConfig = Optional.ofNullable(userCardCategoryConfigMap.get(record.getUserCardManage().getCardCategory()))
                    .orElse(new UserCardCategoryConfig());
            record.setProductName(userCardCategoryConfig.getProductName());
            record.setCardCurrency(userCardCategoryConfig.getCardCurrency());
        });
        List<UserCardFeeConfigDetailVo> vos = new ArrayList<>();
        records.forEach(record -> {
            UserCardFeeConfigDetailVo detailVo = new UserCardFeeConfigDetailVo();
            BeanUtils.copyProperties(record, detailVo);
            if (record.getUserCardManage() != null) {
                UserCardManageDetail manageDetail = new UserCardManageDetail();
                BeanUtils.copyProperties(record.getUserCardManage(), manageDetail);
                detailVo.setUserCardManage(manageDetail);
            }
            vos.add(detailVo);
        });
        IPage<UserCardFeeConfigDetailVo> result = new Page<>();
        result.setPages(userCardFeeConfigVoIPage.getPages());
        result.setCurrent(userCardFeeConfigVoIPage.getCurrent());
        result.setSize(userCardFeeConfigVoIPage.getSize());
        result.setTotal(userCardFeeConfigVoIPage.getTotal());
        result.setRecords(vos);
        return (result);
    }

    /**
     * 获取卡配置，卡段开卡数
     *
     * @param businessId 商户id
     * @return 配置DTO
     */
    @Override
    public List<UserCardFeeConfigDto> numberOfAvailableCards2(Integer businessId) {
        // 获取卡配置
        List<UserCardFeeConfigDto> list = userCardFeeConfigMapper.numberOfAvailableCards2(businessId);
        // 获取卡段开卡数
        for (UserCardFeeConfigDto record : list) {
            List<Integer> list2 = UserCardService.getUserCardlist(record.getCardDuan(), businessId);
            record.setNumberOfCardsIssued(list2.size());
        }
        return list;
    }

    /**
     * @param cardModel    卡模式
     * @param cardBelongTo 卡归属
     * @return 开卡列表
     */
    @Override
    public List<UserCardFeeConfig> bankCard(String cardModel, String cardBelongTo, Integer businessId) {
        return userCardFeeConfigMapper.bankCard(cardModel, cardBelongTo, businessId);
    }

    /**
     * 编辑商户卡段费率
     *
     * @param businessRateDto
     * @param businessId
     */
    @Override
    public void updateBusinessRate(BusinessRateDto businessRateDto, Integer businessId) {
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.getByBusinessIdAndCardManageId(businessId, businessRateDto.getCardManageId());
        //商户费率必须大于平台费率
        if (businessRateDto.getBusinessOpenCardFee().compareTo(userCardFeeConfig.getPlatformOpenCardFee()) < 0 ||
                businessRateDto.getBusinessChargeRate().compareTo(userCardFeeConfig.getPlatformChargeRate()) < 0 ||
                businessRateDto.getBusinessCancelCardFee().compareTo(userCardFeeConfig.getPlatformCancelCardFee()) < 0) {
            throw new ApiException(ResultCode.BUSINESS_FEE_LIMIT);
        }
        BigDecimal rate = new BigDecimal("100");
        BigDecimal fee = new BigDecimal("9999");
        if (businessRateDto.getBusinessOpenCardFee().compareTo(fee) > 0 ||
                businessRateDto.getBusinessChargeRate().compareTo(rate) > 0 ||
                businessRateDto.getBusinessCancelCardFee().compareTo(fee) > 0) {
            throw new ApiException(ResultCode.FEE_ERR);
        }
        userCardFeeConfig.setBusinessOpenCardFee(businessRateDto.getBusinessOpenCardFee());
        userCardFeeConfig.setBusinessChargeRate(businessRateDto.getBusinessChargeRate());
        userCardFeeConfig.setBusinessCancelCardFee(businessRateDto.getBusinessCancelCardFee());
        userCardFeeConfig.setCardSingleLimit(businessRateDto.getCardSingleLimit());
        userCardFeeConfig.setCardMonthLimit(businessRateDto.getCardMonthLimit());
        userCardFeeConfig.setCardTotalLimit(businessRateDto.getCardTotalLimit());
        //设置更新时间为现在的时间
        userCardFeeConfig.setUpdateTime(new Date());
        userCardFeeConfigMapper.updateById(userCardFeeConfig);
    }
}




