package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.BusinessDayEarningsLog;
import generator.mapper.BusinessDayEarningsLogMapper;
import com.qf.zpay.util.DateUtil;
import generator.domain.User;
import generator.mapper.UserMapper;
import generator.mapper.UserMoneyLogMapper;
import generator.service.BusinessDayEarningsLogService;
import generator.service.UserMoneyLogService;
import generator.service.UserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
@Log4j2
@Service
public class BusinessDayEarningsLogServiceImpl extends ServiceImpl<BusinessDayEarningsLogMapper, BusinessDayEarningsLog>  implements BusinessDayEarningsLogService {

    @Autowired
    private UserMapper userService;

    @Autowired
    private UserMoneyLogMapper userMoneyLogService;

    @Autowired
    private BusinessDayEarningsLogMapper businessDayEarningsLogMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addEarningsLog() {
        log.info("-------------------统计收益流水——————————————开始");
        List<User> userList= userService.selectAll();
        int i =0;

        // 获取前一天的日期 格式化为YYYY-MM-DD格式
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = yesterday.format(formatter);

        for (User user : userList) {
            //今日开卡费
            /*举例商户设置用户开卡费5U，今日开了100张卡。
             今日开卡费为500U*/
            BigDecimal dayOpenCardFee = userMoneyLogService.queryCardMoneyAmount(formattedDate,user.getBusinessId(),32);
            //前一日开卡费收益
                /*举例商户设置用户开卡费5U，今日开了100张卡。
                今日开卡费为500U
                平台设置商户的开卡费为2U，开卡费支出为200U
                今日开卡费收益为300U*/
            BigDecimal pingtaiOpenCardFee = userMoneyLogService.queryCardMoneyAmount(formattedDate,user.getBusinessId(),33);
            BigDecimal dayOpenCardEarnings = pingtaiOpenCardFee!=null ? pingtaiOpenCardFee.add(dayOpenCardFee).setScale(2, RoundingMode.HALF_EVEN) :BigDecimal.ZERO;
            //今日手续费
                /*举例商户设置用户充值手续费8%，今日充值1万
                今日手续费费为800U*/
            BigDecimal dayRechargeChargeFee = userMoneyLogService.queryCardMoneyAmount(formattedDate,user.getBusinessId(),31);
            BigDecimal pingtaiChargeCardFee = userMoneyLogService.queryCardMoneyAmount(formattedDate,user.getBusinessId(),36);
            //今日手续费收益
                /*举例商户设置用户充值手续费8%，今日充值1万
                今日手续费费为800U
                平台设置给商户的充值手续费为2%
                今日手续费支出为200U
                今日手续费收益为600U*/
            BigDecimal dayChargeCardEarnings = dayRechargeChargeFee!=null ? dayRechargeChargeFee.add(pingtaiChargeCardFee).setScale(2, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
            // 销卡余额返回
            BigDecimal cardCancellationBalanceReturn = userMoneyLogService.queryCardMoneyAmount(formattedDate,user.getBusinessId(),37);
            //今日收益 = 今日开卡费收益+今日手续费收益+今日销卡收益+今日销卡余额返回
            List<Integer> subTypes = Arrays.asList(34, 35, 37);
            BigDecimal dayEarnings  = userMoneyLogService.getDayChargeTotal(formattedDate,user.getBusinessId(), subTypes);
            //历史总收益
            BigDecimal historyEarnings  = userMoneyLogService.getDayMoneyAmount(user.getBusinessId(), subTypes);

            BusinessDayEarningsLog businessDayEarningsLog = businessDayEarningsLogMapper.queryBusinessIdAndCreateDate(user.getBusinessId(),formattedDate);
            if (businessDayEarningsLog != null){
                setBusinessDayEarningsLog(user, dayOpenCardFee, dayOpenCardEarnings, dayRechargeChargeFee, dayChargeCardEarnings, cardCancellationBalanceReturn, dayEarnings, historyEarnings, businessDayEarningsLog);
                int updateById = businessDayEarningsLogMapper.updateById(businessDayEarningsLog);
                if (updateById > 0){
                    i++;
                }
            }else {
                BusinessDayEarningsLog businessDayEarningsLog1 = new BusinessDayEarningsLog();
                setBusinessDayEarningsLog(user, dayOpenCardFee, dayOpenCardEarnings, dayRechargeChargeFee, dayChargeCardEarnings, cardCancellationBalanceReturn, dayEarnings, historyEarnings, businessDayEarningsLog1);
                int insert = businessDayEarningsLogMapper.insert(businessDayEarningsLog1);
                if (insert > 0){
                    i++;
                }
            }

        }
        log.info("统计收益流水任务 结束--------------" + today +"汇总了" + formattedDate +"的"+ i +"条数据");

    }

    private void setBusinessDayEarningsLog(User user, BigDecimal dayOpenCardFee, BigDecimal dayOpenCardEarnings, BigDecimal dayRechargeChargeFee, BigDecimal dayChargeCardEarnings, BigDecimal cardCancellationBalanceReturn, BigDecimal dayEarnings, BigDecimal historyEarnings, BusinessDayEarningsLog businessDayEarningsLog1) {
        businessDayEarningsLog1.setBusinessId(user.getBusinessId());
        businessDayEarningsLog1.setDayOpenCardFee(dayOpenCardFee);
        businessDayEarningsLog1.setDayOpenCardEarnings(dayOpenCardEarnings);
        businessDayEarningsLog1.setDayRechargeChargeFee(dayRechargeChargeFee);
        businessDayEarningsLog1.setDayRechargeChargeCardEarnings(dayChargeCardEarnings);
        businessDayEarningsLog1.setDayEarnings(dayEarnings);
        businessDayEarningsLog1.setHistoryEarnings(historyEarnings);
        businessDayEarningsLog1.setCreateTime(new Date());
        businessDayEarningsLog1.setCreateDate(DateUtil.getDate(new Date()));
        businessDayEarningsLog1.setCardCancellationBalanceReturn(cardCancellationBalanceReturn);
    }
}
