package generator.service.impl;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.dto.req.*;
import com.qf.zpay.dto.res.v1.UserCardAndFeeConfigAndManageDTO;
import com.qf.zpay.exception.ApiException;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.FTPService;
import com.qf.zpay.util.ZHelperUtil;
import generator.domain.*;
import generator.mapper.UserCardCategoryConfigMapper;
import generator.mapper.UserCardFeeConfigMapper;
import generator.mapper.UserCardManageMapper;
import generator.mapper.UserCardMapper;
import generator.service.CardApplyOrderService;
import generator.service.CardHolderService;
import generator.service.MailingAddressService;
import generator.service.UserCardService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserCardServiceImpl extends ServiceImpl<UserCardMapper, UserCard>
        implements UserCardService {

    @Autowired
    private UserCardMapper userCardMapper;

    @Autowired
    private UserCardFeeConfigMapper userCardFeeConfigMapper;

    @Autowired
    private UserCardManageMapper userCardManageMapper;

    @Autowired
    private UserCardCategoryConfigMapper userCardCategoryConfigMapper;
    @Autowired
    private CardHolderService cardHolderService;
    @Autowired
    MailingAddressService mailingAddressService;

    /**
     * 三表(user_card,user_card_fee_config,user_card_manage)联查信息
     *
     * @param businessId
     * @param cardId
     * @return
     */
    @Override
    public UserCardAndFeeConfigAndManageDTO getMemberCardDetail(String businessId, String cardId) {
        // 创建一个UserCardAndFeeConfigAndManageDTO对象
        UserCardAndFeeConfigAndManageDTO userCardAndFeeConfigAndManageDTO = new UserCardAndFeeConfigAndManageDTO();
        // 根据cardId查询UserCard表
        UserCard userCard = userCardMapper.selectByCardId(cardId);
        // 如果查询不到userCard，则返回一个空的UserCardDTO对象
        if (userCard == null) {
            Assert.fail(ResultCode.USER_CARD_ISNULL);
        }
        // 根据businessId和cardManageId查询UserCardFeeConfig表
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.getByBusinessIdAndCardManageId(Integer.valueOf(businessId), userCard.getCardManageId());
        //根据cardManageId查询UserCardManage表
        UserCardManage userCardManage = userCardManageMapper.selectById(userCard.getCardManageId());

        // 将查询到的数据复制到UserCardDTO对象中
        BeanUtils.copyProperties(userCard, userCardAndFeeConfigAndManageDTO);
        BeanUtils.copyProperties(userCardFeeConfig, userCardAndFeeConfigAndManageDTO);
        BeanUtils.copyProperties(userCardManage, userCardAndFeeConfigAndManageDTO);
        userCardAndFeeConfigAndManageDTO.setCardManageStatus(userCardManage.getCardStatus());

        // 返回UserCardDTO对象
        return userCardAndFeeConfigAndManageDTO;
    }

    /**
     * 卡片管理
     *
     * @param cardSettingsDto 筛选条件
     * @return 分页数据
     */
    @Override
    public IPage<ListOfCardsDetailDto> getListOfCards(CardSettingsDto cardSettingsDto) {
        Page<ListOfCardsDto> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        IPage<ListOfCardsDto> listOfCards = userCardMapper.getListOfCards(page, cardSettingsDto);

        Set<Integer> cardManageSet = listOfCards.getRecords().stream()
                .map(ListOfCardsDto::getUserCard)
                .map(UserCard::getCardManageId)
                .collect(Collectors.toSet());

        Map<Integer, UserCardFeeConfig> userCardFeeConfigMap = cardManageSet.isEmpty() ? Collections.emptyMap() :
                userCardFeeConfigMapper.selectList(
                                new QueryWrapper<UserCardFeeConfig>()
                                        .in("card_manage_id", cardManageSet)
                                        .eq("business_id", cardSettingsDto.getBusinessId())
                        )
                        .stream()
                        .collect(Collectors.toMap(
                                UserCardFeeConfig::getCardManageId,
                                Function.identity(),
                                (existing, newValue) -> newValue
                        ));

        List<ListOfCardsDetailDto> dtos = new ArrayList<>();
        for (ListOfCardsDto record : listOfCards.getRecords()) {
            ListOfCardsDetailDto listOfCardsDetailDto = new ListOfCardsDetailDto();
            if (record.getUserCardManage() != null) {
                UserCardManageDetail userCardManageDetail = new UserCardManageDetail();
                BeanUtils.copyProperties(record.getUserCardManage(), userCardManageDetail);
                listOfCardsDetailDto.setUserCardManage(userCardManageDetail);
            }
            if (record.getUserCard() != null) {
                UserCardDetail userCardDetail = new UserCardDetail();
                BeanUtils.copyProperties(record.getUserCard(), userCardDetail);
                listOfCardsDetailDto.setUserCard(userCardDetail);
            }
            if (record.getUserCardCategoryConfig() != null) {
                UserCardCategoryConfigDetail cardCategoryConfigDto = new UserCardCategoryConfigDetail();
                BeanUtils.copyProperties(record.getUserCardCategoryConfig(), cardCategoryConfigDto);
                listOfCardsDetailDto.setUserCardCategoryConfig(cardCategoryConfigDto);
            }
            listOfCardsDetailDto.setMailingAddress(record.getMailingAddress());
            UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMap.get(record.getUserCard().getCardManageId());
            if (userCardFeeConfig != null) {
                UserCardFeeConfigDetail userCardFeeConfigDetail = new UserCardFeeConfigDetail();
                BeanUtils.copyProperties(userCardFeeConfig, userCardFeeConfigDetail);
                listOfCardsDetailDto.setUserCardFeeConfig(userCardFeeConfigDetail);
            }
            dtos.add(listOfCardsDetailDto);
        }
//        listOfCards.getRecords().forEach(
//                record -> record.setUserCardFeeConfig(userCardFeeConfigMap.get(record.getUserCard().getCardManageId()))
//        );

        IPage<ListOfCardsDetailDto> result = new Page<>();
        result.setPages(page.getPages());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setRecords(dtos);
        return result;
    }

    @Override
    public void exportListOfCards(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        List<UserCard> userCards = userCardMapper.getListOfCardsAll(cardSettingsDto);
        for (UserCard userCard : userCards) {
            // 共享卡使用
            if ("share".equals(userCard.getCardModel())) {
                userCard.setAmount(userCard.getVirtualAmt());
            }
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("cardNumber", ZHelperUtil.getI18nMessages("cardNumber"));
        writer.addHeaderAlias("cardExpirationMmyy", ZHelperUtil.getI18nMessages("createTime"));
        writer.addHeaderAlias("amount", ZHelperUtil.getI18nMessages("card_transAmount"));
        writer.merge(2, ZHelperUtil.getI18nMessages("card_fileTitle"));
        String fileName = ZHelperUtil.getI18nMessages("card_fileTitle") + ".xlsx";
        extracted(response, writer, userCards, fileName);
    }

    @Override
    public String getUpUid(String cardId) {
        UserCard byCardId = getByCardId(cardId);
        return byCardId.getHolderId().toString();
    }


    /**
     * 修改卡片昵称
     *
     * @param cardId   卡id
     * @param cardName 卡片昵称
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCardId(String cardId, String cardName) {
        UserCard userCard = userCardMapper.selectByCardId(cardId);
        userCard.setCardNickname(cardName);
        userCardMapper.updateById(userCard);
    }

    public UserCard getByCardId(String cardId) {
        return this.getOne(new LambdaQueryWrapper<UserCard>().eq(UserCard::getCardId, cardId));
    }

    private static final Integer USER_CARD_ENABLE = 1;
    private static final Integer USER_CARD_DISABLE = 2;

    /**
     * 卡片禁用or启用接口
     *
     * @param cardManageId 卡片管理配置id
     * @param businessId   商户id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setCardEnabledOrDisabled(Integer cardManageId, Integer businessId) {
        QueryWrapper<UserCardFeeConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("card_manage_id", cardManageId).eq("business_id", businessId);
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.selectOne(queryWrapper);
        if (userCardFeeConfig.getIsAble().equals(USER_CARD_DISABLE)) {
            throw new ApiException(ResultCode.CARD_BIN_NOT_ENABLE);
        }
        userCardFeeConfig.setIsAble(USER_CARD_DISABLE);

//        if (Objects.equals(userCardFeeConfig.getIsAble(), USER_CARD_STATUS_1)) {
//            userCardFeeConfig.setIsAble(USER_CARD_STATUS_2);
//        } else {
//            userCardFeeConfig.setIsAble(USER_CARD_STATUS_1);
//        }
        userCardFeeConfigMapper.updateById(userCardFeeConfig);
    }

    @Override
    public List<Integer> getUserCardlist(String cardDuan, Integer businessId) {
        return userCardMapper.getUserCardlist(cardDuan, businessId);
    }

    @Autowired
    MailingAddressService service;

    /**
     * 卡片详情
     *
     * @param cardId 卡片id
     * @return 卡片数据详情
     */
    @Override
    public ListOfCardsDetailDto getByMemberCardDetail(Integer businessId, String cardId) {
        // 根据cardId查询UserCard表
        UserCard userCard = userCardMapper.selectByCardId(cardId);
        // 如果查询不到userCard，则返回一个空的UserCardDTO对象
        if (userCard == null) {
            Assert.fail(ResultCode.USER_CARD_ISNULL);
        }
        // 根据businessId和cardManageId查询UserCardFeeConfig表手续费
        UserCardFeeConfig userCardFeeConfig = userCardFeeConfigMapper.getByBusinessIdAndCardManageId(businessId, userCard.getCardManageId());
        //根据cardManageId查询UserCardManage表
        UserCardManage userCardManage = userCardManageMapper.selectById(userCard.getCardManageId());
        UserCardCategoryConfig userCardCategoryConfig = userCardCategoryConfigMapper.selectById(userCardManage.getCardCategory());
        ListOfCardsDto listOfCardsDto = new ListOfCardsDto();
        MailingAddress mailingAddress = service.getMailingAddress(userCard.getCardId());
        if (userCard.getHolderId() != null) {
            CardHolder byId = cardHolderService.getById(userCard.getHolderId());
            listOfCardsDto.setCardHolder(byId);
        }
//        listOfCardsDto.setUserCardManage(userCardManage);
//        listOfCardsDto.setUserCard(userCard);
//        listOfCardsDto.setUserCardFeeConfig(userCardFeeConfig);
//        listOfCardsDto.setUserCardCategoryConfig(userCardCategoryConfig);
//        listOfCardsDto.setMailingAddress(mailingAddress);

        ListOfCardsDetailDto listOfCardsDetailDto = new ListOfCardsDetailDto();
        UserCardManageDetail userCardManageDetail = new UserCardManageDetail();
        BeanUtils.copyProperties(userCardManage, userCardManageDetail);
        listOfCardsDetailDto.setUserCardManage(userCardManageDetail);
        UserCardDetail userCardDetail = new UserCardDetail();
        BeanUtils.copyProperties(userCard, userCardDetail);
        listOfCardsDetailDto.setUserCard(userCardDetail);
        UserCardFeeConfigDetail configDetail = new UserCardFeeConfigDetail();
        BeanUtils.copyProperties(userCardFeeConfig, configDetail);
        listOfCardsDetailDto.setUserCardFeeConfig(configDetail);
        UserCardCategoryConfigDetail cardCategoryConfigDto = new UserCardCategoryConfigDetail();
        BeanUtils.copyProperties(userCardCategoryConfig, cardCategoryConfigDto);
        listOfCardsDetailDto.setUserCardCategoryConfig(cardCategoryConfigDto);
        listOfCardsDetailDto.setMailingAddress(mailingAddress);
        return listOfCardsDetailDto;
    }

    @Override
    public List<UserCard> getUserShareCardList(Integer businessId) {
        return userCardMapper.getUserShareCardList(businessId);
    }


    @Override
    public UserCard getOneEmptyCard(String productCode) {
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_code", productCode);
        queryWrapper.isNull("open_card_time");
        queryWrapper.isNull("uid");
        queryWrapper.last("LIMIT 1");
        return userCardMapper.selectOne(queryWrapper);
    }

    @Override
    public UserCard getOneEmptyCard(String productCode, CardSchemeEnum cardScheme) {
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_code", productCode);
        queryWrapper.isNull("open_card_time");
        queryWrapper.isNull("uid");
        queryWrapper.eq("card_scheme", cardScheme.getCode());
        queryWrapper.last("LIMIT 1");
        return userCardMapper.selectOne(queryWrapper);
    }

    @Override
    public UserCard getOneEmptyCard(String productCode, Integer cardManageId) {
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_code", productCode);
        queryWrapper.isNull("open_card_time");
        queryWrapper.isNull("uid");
        queryWrapper.eq("card_manage_id", cardManageId);
        queryWrapper.last("LIMIT 1");
        return userCardMapper.selectOne(queryWrapper);
    }

    @Override
    public Long totalCardBought(Integer businessId, Integer cardManageId) {
        return this.count(new LambdaQueryWrapper<>(UserCard.class)
                .eq(UserCard::getCardManageId, cardManageId)
                .eq(UserCard::getUid, businessId)
                .eq(UserCard::getUidChild, -1)
                .isNull(UserCard::getOpenCardTime)
        );
    }

    @Override
    public UserCard getOneEmptyCardBought(Integer businessId, Integer cardManageId) {
        return userCardMapper.selectOne(new LambdaQueryWrapper<>(UserCard.class)
                .eq(UserCard::getCardManageId, cardManageId)
                .eq(UserCard::getUid, businessId)
                .eq(UserCard::getUidChild, -1)
                .isNull(UserCard::getOpenCardTime)
                .last("LIMIT 1")
        );
    }

    @Override
    public IPage<UserCard> pageCardList(
            Integer businessId,
            String cardModel,
            String cardScheme,
            String cardStatus,
            Integer page,
            Integer size
    ) {
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", businessId);
        if (cardModel != null) {
            queryWrapper.eq("card_model", cardModel);
        }
        if (cardScheme != null) {
            queryWrapper.eq("card_scheme", cardScheme);
        }
        if (cardStatus != null) {
            queryWrapper.eq("card_status", cardStatus);
        }
        Page<UserCard> userCardPage = new Page<>(page, size);
        return userCardMapper.selectPage(userCardPage, queryWrapper);
    }

    @Autowired
    FTPService ftpService;
    @Autowired
    CardApplyOrderService cardApplyOrderService;


    @Override
    public UserCard getByCardNumber(String pan) {
        return userCardMapper.getOneByCardNumber(pan);
    }

    @Override
    public List<UserCard> getProductCode(String productCode) {
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_code", productCode);
        queryWrapper.isNull("open_card_time");
        queryWrapper.isNull("uid");
        return userCardMapper.selectList(queryWrapper);
    }

    @Override
    public UserCard getByCardNumberLike(String cardEnd) {
        return userCardMapper.selectOne(
                new LambdaQueryWrapper<>(UserCard.class)
                        .likeLeft(UserCard::getCardNumber, cardEnd)
        );
    }

    @Override
    public UserCard getByCardNumberLike(String carBin, String cardEnd) {
        return userCardMapper.selectOne(
                new LambdaQueryWrapper<>(UserCard.class)
                        .likeRight(UserCard::getCardNumber, carBin)
                        .likeLeft(UserCard::getCardNumber, cardEnd)
        );
    }


    private static void extracted(HttpServletResponse response, ExcelWriter writer, List<UserCard> list, String fileName) throws IOException {
        //只保留别名的数据
        writer.setOnlyAlias(true);
        // 默认配置
        writer.write(list, true);
        // 设置单元格样式
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < list.size(); i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 20 / 10);
        }

        // 设置content—type
        response.setContentType("application/vnd.ms-excel;charset=utf-8");

        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Content-Disposition", "attachment;filename=" + Base64.getEncoder().encodeToString(fileName.getBytes("UTF-8")));

        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }
}




