package generator.service.impl;


import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.zpay.admin.job.dto.StatCardNumDto;
import generator.domain.UserCard;
import generator.mapper.UserCardMapper;
import generator.service.UserCardService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static generator.domain.table.UserCardTableDef.USER_CARD;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserCardServiceImpl extends ServiceImpl<UserCardMapper, UserCard> implements UserCardService {

    @Override
    public UserCard getOneByCardId(String cardId) {
        QueryWrapper query = new QueryWrapper();
        query.where(USER_CARD.CARD_ID.eq(cardId));
        return this.getOne(query);
    }

    @Override
    public UserCard getOneByOrderNo(String orderNo) {
        return this.queryChain().from(USER_CARD)
                .where(USER_CARD.ORDER_NO.eq(orderNo))
                .one();
    }

    @Override
    public List<UserCard> getCardIdByCardNo(String cardNo) {
        return this.queryChain().select(USER_CARD.CARD_ID, USER_CARD.ORDER_NO).from(USER_CARD)
                .where(USER_CARD.CARD_MODEL.eq("physical"))
                .where(USER_CARD.CARD_NUMBER.likeRight(cardNo))
                .list();
    }

    @Override
    public StatCardNumDto statCardHistory(Integer businessId) {
        StatCardNumDto stat = new StatCardNumDto();
        QueryWrapper query = QueryWrapper.create().from(USER_CARD);
        query.select(USER_CARD.ID, USER_CARD.CARD_MODEL, USER_CARD.CARD_STATUS, USER_CARD.AMOUNT);
        query.where(USER_CARD.OPEN_CARD_DATE.isNotNull());
        query.where(USER_CARD.UID.isNotNull());
        if (businessId != null) {
            query.where(USER_CARD.UID.eq(businessId));
        }
//        List<UserCard> userCardList = this.queryChain()
//                .select(USER_CARD.CARD_ID)
//                .from(USER_CARD)
//                .where(USER_CARD.OPEN_CARD_DATE.isNotNull())
//                .list();
        List<UserCard> cardList = this.list(query);
        stat.setTotalCardNum(cardList.size());

        BigDecimal totalCardAmount = cardList.stream()
                .map(UserCard::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stat.setTotalCardAmount(totalCardAmount);

        long activeCardNum = cardList.stream().filter(card -> "Active".equals(card.getCardStatus())).count();
        long rechargeCardNum = cardList.stream().filter(card -> "recharge".equals(card.getCardModel())).count();
        long shareCardNum = cardList.stream().filter(card -> "share".equals(card.getCardModel())).count();
        long physicalCardNum = cardList.stream().filter(card -> "physical".equals(card.getCardModel())).count();
        stat.setActiveCardNum((int) activeCardNum);
        stat.setRechargeCardNum((int) rechargeCardNum);
        stat.setShareCardNum((int) shareCardNum);
        stat.setPhysicalCardNum((int) physicalCardNum);

        BigDecimal activeCardAmount = cardList.stream()
                .filter(card -> "Active".equals(card.getCardStatus()))
                .map(UserCard::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stat.setActiveCardAmount(activeCardAmount);
        return stat;
    }

    @Override
    public StatCardNumDto statCardPlatformHistory() {
        return statCardHistory(null);
    }
}