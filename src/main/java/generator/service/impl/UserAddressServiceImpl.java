package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.User;
import generator.mapper.UserAddressMapper;
import generator.mapper.UserMapper;
import generator.service.UserAddressService;
import generator.domain.UserAddress;
import generator.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_address】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress>
        implements UserAddressService {

    @Autowired
    UserAddressMapper userAddressMapper;

    @Autowired
    UserMapper userMapper;

    @Override
    public UserAddress getBusinessAddress(Integer userId) {
        return userAddressMapper.getOneByUserId(userId);
    }

    @Override
    public User getUserByAddress(String address, Integer type) {
        // 1    ERC20 ETH
        // 56  BEP20 BSC
        // 137 MATIC POLYGON
        // 195 TRC20 TRON
        // 200 Solana Solana
        List eth = List.of(1, 56, 137);
        List trc = List.of(195);
        QueryWrapper<UserAddress> query = new QueryWrapper<>();
        if (eth.contains(type)) {
            query.eq("erc_address", address);
        } else if (type.equals(195)) {
            query.eq("trc_address", address);
        }
        UserAddress userAddress = this.getOne(query);
        return userMapper.getOneById(userAddress.getUserId());
    }


}




