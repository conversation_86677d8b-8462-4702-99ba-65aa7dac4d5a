package generator.service.impl;


import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import generator.domain.UserCardManage;
import generator.mapper.UserCardManageMapper;
import generator.service.UserCardManageService;
import org.springframework.stereotype.Service;

import static generator.domain.table.UserCardManageTableDef.USER_CARD_MANAGE;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserCardManageServiceImpl extends ServiceImpl<UserCardManageMapper, UserCardManage> implements UserCardManageService {

    @Override
    public UserCardManage getCardManageByCardModelAndCardBin(String cardModel, String cardBin) {
        QueryWrapper query = new QueryWrapper();
        query.where(USER_CARD_MANAGE.CARD_MODEL.eq(cardModel));
        query.where(USER_CARD_MANAGE.CARD_DUAN.eq(cardBin));
        return this.getMapper().selectOneByQuery(query);
    }
}