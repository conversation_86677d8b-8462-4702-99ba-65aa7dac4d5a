package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.common.CardProductDto;
import com.qf.zpay.exception.Assert;
import com.qf.zpay.response.ResultCode;
import generator.domain.*;
import generator.mapper.UserCardManageMapper;
import generator.mapper.UserCardMapper;
import generator.mapper.UserMapper;
import generator.service.UserCardCategoryConfigService;
import generator.service.UserCardFeeConfigService;
import generator.service.UserCardManageService;
import generator.vo.CardManageWithFeeVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_manage】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserCardManageServiceImpl extends ServiceImpl<UserCardManageMapper, UserCardManage>
        implements UserCardManageService {

    @Autowired
    private UserCardMapper userCardMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    UserCardFeeConfigService cardFeeConfigService;

    @Autowired
    UserCardCategoryConfigService cardCategoryConfigService;

    @Autowired
    UserCardManageMapper userCardManageMapper;

    /**
     * 获取卡片列表
     *
     * @param businessId
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public IPage<UserCard> getCardList(Integer businessId, Integer page, Integer pageSize) {
        // 构建查询条件
        QueryWrapper<UserCard> queryWrapper = new QueryWrapper<>();
        if (businessId != null) {
            queryWrapper.eq("uid", businessId);
        }
        // 构建分页对象
        Page<UserCard> userCardPage = new Page<>(page, pageSize);
        // 执行分页查询返回查询结果
        IPage<UserCard> iPage = userCardMapper.selectPage(userCardPage, queryWrapper);
        return iPage;
    }

    @Override
    public UserCardManage getAndCheckCardManage(Integer cardManageId) {
        UserCardManage cardManage = this.getById(cardManageId);
        if (cardManage == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }
        if (cardManage.getCardStatus().equals(0)) {
            Assert.fail(ResultCode.CARD_MANAGE_DISABLED);
        }
        return cardManage;
    }

    @Override
    public UserCardManage getCardManage(Integer cardManageId) {
        UserCardManage cardManage = this.getById(cardManageId);
        if (cardManage == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }
        return cardManage;
    }


    @Override
    public CardProductDto getAndCheckCardProductInfo(Integer cardManageId, Integer businessId) {
        CardProductDto product = getCardProductInfo(cardManageId, businessId);
        if (!product.getCardStatus()) {
            Assert.fail(ResultCode.CARD_MANAGE_DISABLED);
        }
        if (!product.getIsAble()) {
            Assert.fail(ResultCode.CARD_PRODUCT_DISABLED);
        }
        return product;
    }

    @Override
    public CardProductDto getCardProductInfo(Integer cardManageId, Integer businessId) {
        UserCardManage cardManage = getCardManage(cardManageId);
        UserCardFeeConfig cardFeeConfig = cardFeeConfigService.getCardFeeConfig(cardManageId, businessId);
        UserCardCategoryConfig cardCategoryConfig = cardCategoryConfigService.getAndCheckCardCategoryConfig(cardManage.getCardCategory());
        CardProductDto product = new CardProductDto();
        product.setCardManageId(cardManageId);
        product.setBusinessId(businessId);
        product.setProductCode(cardCategoryConfig.getProductCode());
        product.setChannel(cardCategoryConfig.getChannel());
        product.setCardModel(cardManage.getCardModel());
        product.setCardStatus(cardManage.getCardStatus() != 0);
        product.setPlatformOpenCardFee(cardFeeConfig.getPlatformOpenCardFee());
        product.setPlatformChargeRate(cardFeeConfig.getPlatformChargeRate());
        product.setPlatformCancelCardFee(cardFeeConfig.getPlatformCancelCardFee());
        product.setFirstRecharge(cardFeeConfig.getFirstRecharge());
        product.setCostOpenCardFee(cardFeeConfig.getCostOpenCardFee());
        product.setCostChargeRate(cardFeeConfig.getCostChargeRate());
        product.setCostCancelCardFee(cardFeeConfig.getCostCancelCardFee());
        product.setBusinessOpenCardFee(cardFeeConfig.getBusinessOpenCardFee());
        product.setBusinessChargeRate(cardFeeConfig.getBusinessChargeRate());
        product.setBusinessCancelCardFee(cardFeeConfig.getBusinessCancelCardFee());
        product.setCardScheme(cardManage.getCardBelongTo());
        product.setIsAble(cardFeeConfig.getIsAble().equals(1));
        product.setTotalOpenCardNum(cardFeeConfig.getNumberOfCardsThatCanBeOpened());
        return product;
    }


    @Override
    public List<CardManageWithFeeVo> getBusinessCardManageWithFee(Integer businessId) {
        return userCardManageMapper.getBusinessCardManageWithFee(businessId);
    }

    @Override
    public CardManageWithFeeVo getOneBusinessCardManageWithFee(Integer businessId, Integer cardManageId) {
        CardManageWithFeeVo cardManageWithFeeVo = userCardManageMapper.getOneBusinessCardManageWithFee(businessId, cardManageId);
        if (cardManageWithFeeVo == null) {
            Assert.fail(ResultCode.CARD_MANAGE_NOT_EXIST);
        }
        return cardManageWithFeeVo;
    }

    @Override
    public Integer getBusinessCardManageOpenCards(Integer businessId, Integer cardManageId) {
        return userCardManageMapper.getBusinessCardManageOpenCards(businessId, cardManageId);
    }


    @Override
    public void initFeeConfig(Integer id) {
        User user = userMapper.getOneById(id);
        List<UserCardFeeConfig> configTemps = cardFeeConfigService.list(new LambdaQueryWrapper<UserCardFeeConfig>().eq(UserCardFeeConfig::getBusinessId, 0));
        for (UserCardFeeConfig config : configTemps) {
            List<UserCardFeeConfig> feeConfigs = cardFeeConfigService.list(new LambdaQueryWrapper<UserCardFeeConfig>()
                    .eq(UserCardFeeConfig::getBusinessId, user.getBusinessId()).eq(UserCardFeeConfig::getCardManageId, config.getCardManageId()));
            if (!feeConfigs.isEmpty()) {
                continue;
            }
            UserCardFeeConfig feeConfig = new UserCardFeeConfig();
            BeanUtils.copyProperties(config, feeConfig);
            feeConfig.setId(null);
            feeConfig.setBusinessId(user.getBusinessId());
            cardFeeConfigService.save(feeConfig);
        }
    }
}




