package generator.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.CardHolderRequestDto;
import com.qf.zpay.service.ImageService;
import com.qf.zpay.util.DateUtil;
import generator.domain.CardHolder;
import generator.domain.User;
import generator.mapper.CardHolderMapper;
import generator.service.CardHolderService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_data】的数据库操作Service实现
 * @createDate 2024-07-09 09:11:54
 */
@Log4j2
@Service
public class CardHolderServiceImpl extends ServiceImpl<CardHolderMapper, CardHolder>
        implements CardHolderService {

    @Autowired
    private ImageService imageService;

    @Autowired
    private CardHolderMapper cardHolderMapper;

    /**
     * 用户kyc
     *
     * @param userData userData 用户kyc数据
     * @param user     商户
     * @return 用户kyc信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CardHolder add(CardHolder userData, User user) {
        userData.setUpdateTime(DateUtil.getUTCDate());
        userData.setCreateTime(DateUtil.getUTCDate());
        userData.setUserId(user.getId());
        userData.setBusinessId(user.getBusinessId());
        userData.setRealNameStatus(3);
        userData.setStatus(0);
        this.save(userData);
        // 抓取图片写入本地服务
        imageService.saveImageFromUrl(userData);
        return userData;
    }

    /**
     * 用卡人列表
     *
     * @param dto 筛选条件
     * @return 用卡人分页列表
     */
    @Override
    public IPage<CardHolder> pageList(CardHolderRequestDto dto) {
        Page<CardHolder> objectPage = new Page<>(dto.getPage(), dto.getPageSize());
        return cardHolderMapper.pageList(objectPage, dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(Integer integer, Integer i) {
        cardHolderMapper.updateStatus(integer, i);
    }

}
