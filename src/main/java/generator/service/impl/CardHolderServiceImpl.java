package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.CardHolderService;
import generator.domain.CardHolder;
import generator.mapper.CardHolderMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class CardHolderServiceImpl extends ServiceImpl<CardHolderMapper, CardHolder> implements CardHolderService {

}