package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.BusinessDayPaymentFlowLog;
import generator.mapper.BusinessDayPaymentFlowLogMapper;
import generator.service.BusinessDayPaymentFlowLogService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tb_business_day_payment_flow_log(代付流水)】的数据库操作Service实现
 * @createDate 2024-10-16 10:10:17
 */
@Service
public class BusinessDayPaymentFlowLogServiceImpl extends ServiceImpl<BusinessDayPaymentFlowLogMapper, BusinessDayPaymentFlowLog> implements BusinessDayPaymentFlowLogService {


}
