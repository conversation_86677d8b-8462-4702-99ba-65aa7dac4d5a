package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.UserPaymentConfig;
import generator.domain.UserWalletLog;
import generator.mapper.UserPaymentConfigMapper;
import generator.mapper.UserWalletLogMapper;
import generator.service.UserPaymentConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserPaymentConfigServiceImpl extends ServiceImpl<UserPaymentConfigMapper,UserPaymentConfig> implements UserPaymentConfigService {
   @Autowired
    UserPaymentConfigMapper mapper;
    @Override
    public UserPaymentConfig selectOne(Integer businessId) {
        QueryWrapper<UserPaymentConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_id", businessId);
        return mapper.selectOne(queryWrapper);
    }
}
