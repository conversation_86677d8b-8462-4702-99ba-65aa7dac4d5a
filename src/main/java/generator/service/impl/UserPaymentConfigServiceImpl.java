package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.UserPaymentConfigService;
import generator.domain.UserPaymentConfig;
import generator.mapper.UserPaymentConfigMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserPaymentConfigServiceImpl extends ServiceImpl<UserPaymentConfigMapper, UserPaymentConfig> implements UserPaymentConfigService {

}