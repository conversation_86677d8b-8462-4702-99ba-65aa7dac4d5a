package generator.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.User;
import generator.domain.UserDevConfig;
import generator.mapper.UserDevConfigMapper;
import generator.mapper.UserMapper;
import generator.service.UserDevConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_dev_config】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:58
 */
@Service
public class UserDevConfigServiceImpl extends ServiceImpl<UserDevConfigMapper, UserDevConfig>
        implements UserDevConfigService {

    private final String ZNET_SECRET = "E@jtLW%eTCjy6rH0C2K^";

    private final List<String> DEFAULT_MODULES = List.of("card", "payment");

    @Autowired
    UserDevConfigMapper userDevConfigMapper;

    @Autowired
    UserMapper userMapper;


    @Override
    public void genUserDevConfig(Integer userId) {
        UserDevConfig userDevConfig = userDevConfigMapper.getOneByUserId(userId);
        User user = userMapper.getOneById(userId);
        if (userDevConfig == null) {
            UserDevConfig devConfig = new UserDevConfig();
            devConfig.setUserId(user.getId());
            devConfig.setBusinessId(user.getBusinessId());
            devConfig.setAppId(DigestUtil.md5Hex(user.getId() + ZNET_SECRET).toUpperCase());
            devConfig.setAppSecret(DigestUtil.md5Hex(user.getBusinessId() + ZNET_SECRET).toUpperCase());
            devConfig.setEnableModule(JSONUtil.toJsonStr(DEFAULT_MODULES));
            userDevConfigMapper.insert(devConfig);
        } else {
            if (userDevConfig.getAppId() == null) {
                userDevConfig.setAppId(DigestUtil.md5Hex(user.getId() + ZNET_SECRET).toUpperCase());
                userDevConfig.setAppSecret(DigestUtil.md5Hex(user.getBusinessId() + ZNET_SECRET).toUpperCase());
                userDevConfigMapper.updateById(userDevConfig);
            }
        }
    }
}




