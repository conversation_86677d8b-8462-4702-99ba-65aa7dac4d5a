package generator.service.impl;


import com.mybatisflex.spring.service.impl.ServiceImpl;
import generator.domain.UserDevConfig;
import generator.mapper.UserDevConfigMapper;
import generator.service.UserDevConfigService;
import org.springframework.stereotype.Service;

import static generator.domain.table.UserDevConfigTableDef.USER_DEV_CONFIG;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserDevConfigServiceImpl extends ServiceImpl<UserDevConfigMapper, UserDevConfig> implements UserDevConfigService {

    @Override
    public UserDevConfig getUserDevConfigByBusinessId(Integer businessId) {
        return this.queryChain()
                .from(USER_DEV_CONFIG)
                .where(USER_DEV_CONFIG.BUSINESS_ID.eq(businessId))
                .one();
    }
}