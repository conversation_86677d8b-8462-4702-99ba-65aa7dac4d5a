package generator.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gsalary.sdk.secure.DigestUtils;
import com.gsalary.sdk.tools.StringTools;
import com.qf.zpay.constants.*;
import com.qf.zpay.service.CardHelper;
import com.qf.zpay.service.CardService;
import com.qf.zpay.service.EmailService;
import com.qf.zpay.service.RsaService;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.ChannelConfig;
import com.qf.zpay.service.card.channel.Photon;
import com.qf.zpay.service.pojo.CardTransDto;
import com.qf.zpay.util.UniqueIdGeneratorUtil;
import generator.domain.*;
import generator.mapper.*;
import generator.service.CbLogService;
import generator.service.PaymentPayOrderService;
import generator.service.PaymentPayeeAccountService;
import generator.service.UserCardLogService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 针对表【tb_cb_log(回调记录)】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Log4j2
@Service
public class CbLogServiceImpl extends ServiceImpl<CbLogMapper, CbLog>
        implements CbLogService {

    @Value("${card.channel.asinx.appSecret}")
    private String appSecret;
    @Autowired
    private UserCardLogMapper userCardLogMapper;
    @Autowired
    private UserCardMapper userCardMapper;
    @Autowired
    private CbLogMapper cbLogMapper;
    @Autowired
    private RsaService rsaService;
    @Autowired
    private CardFactory cardFactory;
    @Autowired
    private PaymentPayOrderService paymentPayOrderService;
    @Autowired
    private PaymentPayeeAccountService paymentPayeeAccountService;
    @Autowired
    private UserWalletMapper userWalletMapper;
    @Autowired
    private UserWalletLogMapper userWalletLogMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private CardService cardService;
    @Autowired
    private EmailService emailService;
    @Autowired
    CardHelper cardHelper;


    @Override
    public JSONObject photon(HttpServletRequest request, String responseBody) throws IOException {
        String type = request.getHeader("X-PD-NOTIFICATION-CATAGORY");
        String subType = request.getHeader("X-PD-NOTIFICATION-TYPE");
        String sign = request.getHeader("X-PD-SIGN");
        cardFactory.getBuilder(CardChannelEnum.Photon);
        cardFactory.checkSign(responseBody, sign);
        JSONObject jsonObject = new JSONObject(responseBody);
        StringBuilder headerString = getStringBuilder(request);
        Integer id = log2db(CardChannelEnum.Photon.getCode(), jsonObject, headerString.toString());
        Date createdAt8 = DateUtil.offsetHour(jsonObject.getDate("createdAt"), 8);

        JSONObject entries = new JSONObject();
        entries.putOpt("roger", true);
        UserCard userCard = userCardMapper.getOneByCardId(jsonObject.getStr("cardId"));
        if (userCard == null) {
            return entries;
        }
        try {
            switch (type) {
                case "issuing":    // 发卡交易
                    Map<String, String> statusMap = new HashMap<>();
                    statusMap.put("succeed", "Settled");
                    statusMap.put("failed", "AuthFailure");

                    BigDecimal cardBalance = jsonObject.getStr("cardType").equals("recharge") ? jsonObject.getBigDecimal("cardBalance") : jsonObject.getBigDecimal("availableTransactionLimit");

                    if (cardBalance.compareTo(BigDecimal.ZERO) >= 0 && "Active".equals(userCard.getCardStatus().getCode())) {
                        userCard.setAmount(cardBalance);
                        userCardMapper.updateById(userCard);
                    }

                    if (statusMap.containsKey(jsonObject.getStr("status"))) {
                        List<UserCardLog> transactionId = userCardLogMapper.getOrderIdDetails(jsonObject.getStr("transactionId"));
                        if (!transactionId.isEmpty()) {
                            return entries;
                        }
                        CardTransDto dto = new CardTransDto();
                        dto.setUpAmount(cardBalance);
                        dto.setOrderId(jsonObject.getStr("transactionId"));
                        dto.setAuthTime(createdAt8);
                        dto.setTransAmountCurrency(jsonObject.getStr("transactionCurrency"));
                        dto.setTransAmount(jsonObject.getBigDecimal("transactionAmount"));
                        dto.setAuthAmountCurrency(jsonObject.getStr("txnPrincipalChangeCurrency"));
                        dto.setAuthAmount(jsonObject.getStr("transactionType").equals("service_fee") ? jsonObject.getBigDecimal("transactionAmount").abs() : jsonObject.getBigDecimal("txnPrincipalChangeAmount").abs());
                        dto.setSettledAmount(BigDecimal.ZERO);
                        dto.setCardId(jsonObject.getStr("cardId"));
                        dto.setStatus(statusMap.get(jsonObject.getStr("status")));
                        dto.setFundsDirection(jsonObject.getDouble("txnPrincipalChangeAmount") < 0 ? "Expenditure" : jsonObject.getDouble("txnPrincipalChangeAmount") == 0 ? "Nochange" : "Income"); // $log['txnPrincipalChangeAmount'] < 0 ? 'Expenditure' : 'Income',
                        BigDecimal availableCredit = userCard.getAmount();
                        if (cardBalance.compareTo(BigDecimal.ZERO) >= 0 && "Active".equals(userCard.getCardStatus().getCode())) {
                            availableCredit = jsonObject.getBigDecimal("availableTransactionLimit");
                            dto.setUpAmount(availableCredit);
                        }
                        dto.setAvailableCredit(availableCredit);
                        dto.setTransactionTypeEnum(ChannelConfig.transactionTypeSwitchStatus(CardChannelEnum.Photon.getCode(), jsonObject.getStr("transactionType")));
                        dto.setMerchantName(jsonObject.getStr("merchantNameLocation"));
                        if (jsonObject.getStr("status").equals("failed")) {
                            dto.setFailureReason(jsonObject.getStr("msg"));
                        }
                        BigDecimal totalFee = new BigDecimal(0.0);
                        if (!jsonObject.getStr("transactionType").equals("service_fee") && jsonObject.getStr("status").equals("succeed")) {
                            BigDecimal transFee = jsonObject.getBigDecimal("feeDeductionAmount");
                            BigDecimal feeReturn = jsonObject.getBigDecimal("feeReturnAmount");
                            totalFee = transFee.add(feeReturn);
                            String fdCurr = jsonObject.getStr("feeDeductionCurrency");
                            dto.setTotalFeeCurrency(StringUtils.isEmpty(fdCurr) ? jsonObject.getStr("feeReturnCurrency") : fdCurr);
                        }
                        if (!jsonObject.isNull("feeReturnDetailJson")) {
                            dto.setBusinessFeeDetail(jsonObject.getStr("feeReturnDetailJson"));
                        }
                        if (!jsonObject.isNull("feeDetailJson")) {
                            dto.setBusinessFeeDetail(jsonObject.getStr("feeDetailJson"));
                        }
                        dto.setTotalFee(totalFee);
                        cardService.doCardTrans(dto);
                    }
                    if (userCard.getCardModel().equals(CardModelEnum.SHARE)) {
                        if ("succeed".equals(jsonObject.getStr("status"))) {
                            BigDecimal authAmount = jsonObject.getStr("transactionType").equals("service_fee")
                                    ? jsonObject.getBigDecimal("transactionAmount").abs()
                                    : jsonObject.getBigDecimal("txnPrincipalChangeAmount").abs();
                            BigDecimal transFee = BigDecimal.ZERO;
                            if (!jsonObject.getStr("transactionType").equals("service_fee") && jsonObject.getStr("status").equals("succeed")) {
                                transFee = jsonObject.getBigDecimal("feeDeductionAmount").abs();
                            }
                            BigDecimal totalAmount = authAmount.add(transFee).negate();
                            if (jsonObject.getStr("transactionType").equals("refund")) {
                                // 退款 正数
                                totalAmount = totalAmount.negate();
                            }
                            userCard.setVirtualAmt(userCard.getVirtualAmt().add(totalAmount));
                            changeShareCardAmount(userCard, totalAmount);
                        }
                    }
                    break;
                case "issuing_card":
                    // 虚拟卡状态通知
                    if (subType.equals("card_status_update")) {
                        String cardStatusNow = Photon.statusSwitcher.get(jsonObject.getStr("cardStatus"));
//                        UserCard userCard2 = userCardMapper.getOneByCardId(jsonObject.getStr("cardId"));
                        userCard.setCardStatus(CardStatusEnum.valueOf(cardStatusNow));
                        userCardMapper.updateById(userCard);
                    }
                    break;
                default:
                    break;
            }
            logOk(id, entries);
            return entries;
        } catch (Exception e) {
            log.error("光子易回调报错: {}", e.getMessage(), e);
            return entries;
        }
    }


    private void changeShareCardAmount(UserCard userCard, BigDecimal negate) {

        UserWallet userWallet = userWalletMapper.getOneByBusinessId(userCard.getUid());
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userWallet.getUserId());
        userWalletLog.setBusinessId(userWallet.getBusinessId());
        userWalletLog.setChangeMoney(negate);
        userWalletLog.setAction(WalletTypeEnum.CONSUME.getCode());
        userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_SHARE);
        userWalletLog.setChangeBefore(userWallet.getShareWallet());
        userWalletLog.setChangeAfter(userWallet.getShareWallet().add(negate));
        userWalletLog.setCreateTime(new Date());
        userWalletLog.setNote("共享卡消费");
        if (negate.compareTo(BigDecimal.ZERO) > 0) {
            userWalletLog.setChangeMoney(negate);
            userWalletLog.setNote("共享卡消费退款");
            userWalletLog.setAction(WalletTypeEnum.CONSUME_RETURN.getCode());
        }
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_NUMBER));
        userWalletLogMapper.insert(userWalletLog);
        userWallet.setShareWallet(userWallet.getShareWallet().add(negate));
        userWalletMapper.updateById(userWallet);

        cardHelper.changeBusinessShareCardAmount(userCard.getUid(), negate, userCard.getCardId());
    }

    public String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return cardNumber;
        }
        int len = cardNumber.length();
        return cardNumber.substring(0, 4) + "*".repeat(len - 8) + cardNumber.substring(len - 4);
    }

    private Boolean insertUserCardLog(UserCardLog userCardLog) {
        UserCard cardId = userCardMapper.getOneByCardId(userCardLog.getCardId());
        if (cardId.getUid() == null) {
            return Boolean.FALSE;
        }
        userCardLog.setBusinessId(cardId.getUid());
        int insert = userCardLogMapper.insert(userCardLog);
        if (insert > 0) {
            return Boolean.TRUE;
        }
        log.error("插入消费记录失败{}", userCardLog);
        return Boolean.FALSE;
    }

    private void logOk(Integer id, JSONObject entries) {
        CbLog logEntry = new CbLog();
        logEntry.setId(id);
        logEntry.setResTime(new Date());
        logEntry.setResBody(entries.toString());
        logEntry.setStatus(1);
        cbLogMapper.updateById(logEntry);
    }

    @Autowired
    UserCardLogService userCardLogService;


    private Integer log2db(String code, JSONObject jsonObject, String string) {
        CbLog cbLog = new CbLog();
        cbLog.setChannel(code);
        cbLog.setReqTime(new Date());
        cbLog.setReqBody(jsonObject.toString());
        cbLog.setReqHeader(string);
        cbLogMapper.insert(cbLog);
        return cbLog.getId();
    }


    @Override
    public void paymentPayOrder(HttpServletRequest request, String body) {
        String appid = request.getHeader("x-appid");
        String authorization = request.getHeader("authorization");
        JSONObject jsonObject = new JSONObject(body);
        StringBuilder headerString = getStringBuilder(request);
        Integer i = log2db("GSalary", jsonObject, headerString.toString());
        boolean status = false;
        try {
            Map<String, String> stringStringMap = parseAuthHeader(authorization);
            String bodyHash = Base64.getEncoder().encodeToString(DigestUtils.sha256(body.getBytes(StandardCharsets.UTF_8)));
            String signBase = String.format("%s %s\n%s\n%s\n%s\n", request.getMethod(), request.getRequestURI(), appid, stringStringMap.get("time"), bodyHash);
            Boolean verify = rsaService.checkSign("gsalary", signBase, stringStringMap.get("signature"), SignAlgorithm.SHA256withRSA);
            if (!verify) {
                throw new RuntimeException("回调验签失败");
            }
            String businessType = jsonObject.getStr("business_type");
            JSONObject data = jsonObject.getJSONObject("data");
            switch (businessType) {
                // 付款订单失败
                case "REMITTANCE_FAIL":
                    QueryWrapper<PaymentPayOrder> paymentPayOrderQueryWrapper = new QueryWrapper<>();
                    paymentPayOrderQueryWrapper.eq("order_id", data.getStr("client_order_id"));
                    PaymentPayOrder one = paymentPayOrderService.getOne(paymentPayOrderQueryWrapper);
                    if (!one.getStatus().isEmpty() && !one.getStatus().equals(PayOrderStatusEnum.FAILED.getCode())) {
                        one.setStatus(data.getStr("status"));
                        one.setFailureReason(data.getStr("error_message"));
                        // 退款
                        paymentPayOrderService.refund(one);
                        status = paymentPayOrderService.updateById(one);
                    }
                    break;
                // 付款订单完成
                case "REMITTANCE_COMPLETE":
                    QueryWrapper<PaymentPayOrder> pqw = new QueryWrapper<>();
                    pqw.eq("order_id", data.getStr("client_order_id"));
                    PaymentPayOrder paymentPayOrder = paymentPayOrderService.getOne(pqw);
                    paymentPayOrder.setStatus(data.getStr("status"));
                    status = paymentPayOrderService.updateById(paymentPayOrder);
                    if (PayOrderStatusEnum.COMPLETED.getCode().equals(paymentPayOrder.getStatus())) {
                        // 回调将商户收益返还至商户代付钱包 - 添加日志
                        User user = userMapper.getOneByBusinessId(paymentPayOrder.getBusinessId());
                        UserWallet userWallet = userWalletMapper.getOneByBusinessId(paymentPayOrder.getBusinessId());
                        // 盈利 = 商户手续费 - 平台手续费
                        BigDecimal changeMoney = paymentPayOrder.getBusinessFeeTotal().subtract(paymentPayOrder.getPlatformFeeTotal());
                        createUserWalletLog(user.getId(), paymentPayOrder, userWallet, WalletTypeEnum.PAYMENT_EARN.getCode(), changeMoney, "代付收益");
                    }
                    break;
                // 收款人账户启用
                case "PAYEE_ACCOUNT_ACTIVE":
                    QueryWrapper<PaymentPayeeAccount> objectQueryWrapper = new QueryWrapper<>();
                    objectQueryWrapper.eq("account_id", data.getStr("account_id"));
                    PaymentPayeeAccount one1 = paymentPayeeAccountService.getOne(objectQueryWrapper);
                    paymentPayeeAccountService.getOne(objectQueryWrapper);
                    one1.setStatus(data.getStr("status"));
                    paymentPayeeAccountService.updateById(one1);
                    break;
                // 卡交易
                case "CARD_TRANSACTION":
                    UserCard userCardOne = userCardMapper.getOneByCardId(data.getStr("card_id"));
                    if (userCardOne == null) {
                        return;
                    }
                    // Date createdAt8 = DateUtil.offsetHour(data.getDate("transaction_time"), 8);
                    String dateTimeStr = data.getStr("transaction_time");
                    // 使用ISO 8601的格式解析字符串
                    Instant instant = Instant.parse(dateTimeStr);
                    // 将Instant转换为java.util.Date
                    Date createdAt8 = Date.from(instant);

                    List<UserCardLog> alreadySave = userCardLogMapper.getOrderIdDetails(jsonObject.getStr("transaction_id"));
                    if (!alreadySave.isEmpty()) {
                        return;
                    }
                    UserCard cardId = cardService.cardInfo(data.getStr("card_id"), true);
                    CardTransDto dto = new CardTransDto();
                    dto.setOrderId(data.getStr("transaction_id"));
                    dto.setAuthTime(createdAt8);
                    JSONObject transactionAmount = data.getJSONObject("transaction_amount");
                    dto.setTransAmountCurrency(transactionAmount.getStr("currency"));
                    dto.setTransAmount(transactionAmount.getBigDecimal("amount").abs());
                    JSONObject accountingAmount = data.getJSONObject("accounting_amount");
                    dto.setAuthAmountCurrency(accountingAmount.getStr("currency"));
                    dto.setAuthAmount(accountingAmount.getBigDecimal("amount").abs());
                    dto.setSettledAmount(accountingAmount.getBigDecimal("amount").abs());
                    dto.setCardId(data.getStr("card_id"));
                    dto.setMerchantName(data.getStr("merchant_name"));
                    dto.setMerchantCountryCode(data.getStr("merchant_region"));
                    String gSalaryStatus = gSalaryStatus(data.getStr("status"));
                    dto.setStatus(gSalaryStatus);
                    dto.setFailureReason("AuthFailure".equals(gSalaryStatus) ? data.getStr("status_description") : "");
                    // 结算日志不记录
                    HashMap<String, String> stringStringHashMap = gSalaryType(data.getStr("biz_type"));
                    if (stringStringHashMap.isEmpty()) {
                        return;
                    }
                    dto.setFundsDirection(stringStringHashMap.get("2"));
                    dto.setTransactionTypeEnum(ChannelConfig.transactionTypeSwitchStatus(CardChannelEnum.GSalary.getCode(), data.getStr("biz_type")));
                    dto.setAvailableCredit(cardId.getAmount());
                    if (!data.isNull("surcharge")) {
                        JSONObject feeObj = data.getJSONObject("surcharge");
                        BigDecimal totalFee = feeObj.getBigDecimal("amount");
                        String currency = feeObj.getStr("currency");
                        dto.setTotalFeeCurrency(currency);
                        dto.setTotalFee(totalFee);
                    }
                    dto.setUpAmount(cardId.getUpAmount());
                    cardService.doCardTrans(dto);

                    UserCard userCard = userCardMapper.getOneByCardId(data.getStr("card_id"));
                    if (userCard.getCardModel().equals(CardModelEnum.SHARE)) {
                        userCard.setVirtualAmt(userCard.getVirtualAmt().add(accountingAmount.getBigDecimal("amount")));
                        changeShareCardAmount(userCard, accountingAmount.getBigDecimal("amount"));
                        userCardMapper.updateById(userCard);
                    }
                    break;
                // 卡状态更新
                case "CARD_STATUS_UPDATE":
                    String cardStatusNow = data.getStr("status");
                    UserCard userCard2 = userCardMapper.getOneByCardId(data.getStr("card_id"));
                    if (userCard2 != null) {
                        CardStatusEnum cardStatusEnum = ChannelConfig.switchStatus(CardChannelEnum.GSalary.getCode(), cardStatusNow);
                        userCard2.setCardStatus(cardStatusEnum);
                        status = userCardMapper.updateById(userCard2) > 0;
                    }
                    break;
            }
            logOk2(i, status);
        } catch (Exception e) {
            log.error("GSalary回调报错 data:{}", jsonObject, e);
        }
    }


    @Override
    public void asinx(HttpServletRequest request, HttpServletResponse response) {
        String appId = request.getParameter("appId");
        String uniqueNo = request.getParameter("uniqueNo");
        String type = request.getParameter("type");
        String result = request.getParameter("result");
        AES aes = SecureUtil.aes(HexUtil.decodeHex(appSecret));
        String dataStr = cn.hutool.core.codec.Base64.decodeStr(aes.decryptStr(result, CharsetUtil.CHARSET_UTF_8));
        JSONObject jsonObject = new JSONObject(dataStr);
        StringBuilder headerString = getStringBuilder(request);
        Integer i = log2db("asinx", jsonObject, headerString.toString());
        switch (type) {
            case "TRANSACTION_VERIFICATION_CODE":
                UserCard userCard = userCardMapper.getOneByCardId(jsonObject.getStr("userBankcardId"));
                String code = jsonObject.getStr("code");
                String subject = "ZNetwork verification code";
                String text = String.format("Your transaction verification code is <strong>%s</strong>. ", code);
                text += "Please complete the verification within 5 minutes. If you did not make this request, please disregard this message. <br/><br/>ZNetwork";
                emailService.sendSimpleMessage(userCard.getContact(), subject, text);
                break;
            case "CARD_STATUS_CHANGE":
                break;
            // 交易
            case "TRANSACTION_CREATED":
                UserCard cardId = userCardMapper.getOneByCardId(jsonObject.getStr("userBankcardId"));
                if (cardId == null) {
                    return;
                }
                // Date createdAt8 = DateUtil.offsetHour(data.getDate("transaction_time"), 8);
                long dateTimeStr = jsonObject.getLong("createAt");
                // 使用ISO 8601的格式解析字符串
                Date createdAt8 = new Date(dateTimeStr);
                List<UserCardLog> alreadySave = userCardLogMapper.getOrderIdDetails(jsonObject.getStr("recordNo"));
                if (!alreadySave.isEmpty()) {
                    return;
                }
                JSONObject data = jsonObject.getJSONObject("transaction");
                // 余额
                BigDecimal balance = data.getBigDecimal("balance");
                if (balance.compareTo(BigDecimal.ZERO) > 0) {
                    cardId.setAmount(balance);
                    userCardMapper.updateById(cardId);
                }
                cardId.setUpAmount(balance);
                CardTransDto dto = new CardTransDto();
                dto.setOrderId(data.getStr("recordNo"));
                dto.setAuthTime(createdAt8);
                dto.setTransAmountCurrency(data.getStr("transCurrency"));
                dto.setTransAmount(data.getBigDecimal("transCurrencyAmt").abs());
                dto.setAuthAmountCurrency(data.getStr("localCurrency"));
                dto.setAuthAmount(data.getBigDecimal("localCurrencyAmt").abs());
                dto.setSettledAmount(data.getBigDecimal("localCurrencyAmt").abs());
                dto.setCardId(jsonObject.getStr("userBankcardId"));
                dto.setMerchantName(data.getStr("merchantName"));
//                dto.setMerchantCountryCode(data.getStr("merchant_region"));
                String gSalaryStatus = gSalaryStatus(data.getStr("transStatus"));
                dto.setStatus(gSalaryStatus);
//                dto.setFailureReason("AuthFailure".equals(gSalaryStatus) ? data.getStr("respCodeDesc") : "");
                // 结算日志不记录
                HashMap<String, String> stringStringHashMap = gSalaryType(data.getStr("transType"));
                if (stringStringHashMap.isEmpty()) {
                    return;
                }
                dto.setFundsDirection(stringStringHashMap.get("2"));
                dto.setTransactionTypeEnum(ChannelConfig.transactionTypeSwitchStatus(CardChannelEnum.AsinxPhysical.getCode(), data.getStr("transType")));
                dto.setAvailableCredit(cardId.getAmount());
                dto.setTotalFeeCurrency(data.getStr("feeCurrency"));
                dto.setTotalFee(data.getBigDecimal("feeAmount"));
                dto.setUpAmount(cardId.getUpAmount());
                cardService.doCardTrans(dto);
                break;
            case "CARD_RECHARGE_RESULT":
                break;
            case "CARD_CLOSE_RESULT":
                break;
            case "MERCHANT_TRADE_FEE":
                break;
            case "CARD_3DS_AUTH_RESULT":
                break;
            case "USER_KYC_STATUS_CHANGE":
                break;
            case "COIN_RECHARGE_RESULT":
                break;
            case "ACTIVATION_CODE":
                break;
        }
    }


    /**
     * 盈利写进钱包  添加日志
     *
     * @param userId          用户id
     * @param paymentPayOrder 订单id
     * @param userWallet      商户钱包
     * @param action          编号
     * @param changeMoney     金额
     * @param note            描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void createUserWalletLog(Integer userId, PaymentPayOrder paymentPayOrder, UserWallet userWallet, Integer action, BigDecimal changeMoney, String note) {
        UserWalletLog userWalletLog = new UserWalletLog();
        userWalletLog.setUserId(userId);
        userWalletLog.setBusinessId(paymentPayOrder.getBusinessId());
        userWalletLog.setAction(action);
        userWalletLog.setWalletType(WalletTypeConstants.STWALLET_TYPE_PAYMENT);
        userWalletLog.setChangeBefore(userWallet.getPaymentWallet());
        userWalletLog.setChangeMoney(changeMoney);
        userWalletLog.setChangeAfter(userWallet.getPaymentWallet().add(changeMoney));
        userWalletLog.setNote(note);
        userWalletLog.setOrderId(UniqueIdGeneratorUtil.generateUniqueId(WalletTypeConstants.SERIAL_PAYMENT));
        userWalletLog.setCreateTime(new Date());
        userWalletLogMapper.insert(userWalletLog);

        userWallet.setPaymentWallet(userWallet.getPaymentWallet().add(changeMoney));
        userWalletMapper.updateById(userWallet);
    }

    private void addTransFee(UserCardLog userCardLog, JSONObject data) {
        JSONObject feeObj = data.getJSONObject("surcharge");
        userCardLog.setId(null);
        userCardLog.setTransAmount(null);
        userCardLog.setTransAmountCurrency(null);
        BigDecimal transFee = feeObj.getBigDecimal("amount").abs();
        userCardLog.setSettledAmount(transFee);
        userCardLog.setAuthAmountCurrency(feeObj.getStr("currency"));
        userCardLog.setAuthAmount(transFee);
        userCardLog.setTransactionType(CardTransTypeEnum.TransFee.getCode());
        userCardLogService.save(userCardLog);
    }

    private static StringBuilder getStringBuilder(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        StringBuilder headerString = new StringBuilder();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerString.append(headerName).append(": ").append(headerValue).append("\n");
        }
        return headerString;
    }

    public Map<String, String> parseAuthHeader(String authHeader) {
        return Arrays.stream(authHeader.split(","))
                .map(String::trim)
                .filter(kvPair -> kvPair.contains("="))
                .map(kvPair -> {
                    String key = StringTools.substringBefore(kvPair, "=");
                    String value = StringTools.substringAfter(kvPair, "=");
                    return new AbstractMap.SimpleEntry<>(key, urlDecode(value));
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> replacement, HashMap::new));
    }

    private static String urlDecode(String value) {
        try {
            return URLDecoder.decode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void logOk2(int id, boolean body) {
        CbLog logEntry = new CbLog();
        logEntry.setId(id);
        logEntry.setResTime(new Date());
        logEntry.setResBody(String.valueOf(body));
        logEntry.setStatus(1);
        cbLogMapper.updateById(logEntry);
    }

    public String gSalaryStatus(String status) {
        // AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算

        switch (status) {
            case "PENDING", "AUTHORIZED", "PROCESSING", "posted", "APPROVED":
                return "AuthSuccessed";
            case "SUCCEED", "VOID":
                return "Settled";
            case "REJECTED", "FAILED", "vodi", "declined", "pending", "DECLINED", "CANCEL":
                return "AuthFailure";
            default:
                return status;
        }
    }

    public HashMap<String, String> gSalaryType(String status) {
        /**AUTH 	交易扣款
         CORRECTIVE_AUTH 	交易扣款修正
         VERIFICATION 	验证交易，通常金额为0
         VOID 	交易撤单
         REFUND 	交易退款
         CORRECTIVE_REFUND 	退款修正
         CORRECTIVE_REFUND_VOID 	退款修正取消
         REFUND_REVERSAL 	撤销退款
         SERVICE_FEE 	卡服务费
         */
        //Income:收入、Expenditure:支出、自己加的 Nochange不变化
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        switch (status) {
            case "AUTH", "topup", "transfer":
                stringStringHashMap.put("1", CardTransTypeEnum.Auth.getCode());
                stringStringHashMap.put("2", "Expenditure");
                break;
            case "CORRECTIVE_AUTH", "CORRECTIVE_REFUND", "charge", "refund":
                stringStringHashMap.put("1", CardTransTypeEnum.CorrectiveRefund.getCode());
                stringStringHashMap.put("2", "Income");
                break;
            case "VERIFICATION":
                stringStringHashMap.put("1", CardTransTypeEnum.Verification.getCode());
                stringStringHashMap.put("2", "Nochange");
                break;
            case "VOID":
                stringStringHashMap.put("1", CardTransTypeEnum.CorrectiveAuth.getCode());
                stringStringHashMap.put("2", "Income");
                break;
            case "REFUND":
                stringStringHashMap.put("1", CardTransTypeEnum.Refund.getCode());
                stringStringHashMap.put("2", "Income");
                break;
            case "SETTLE":
//                stringStringHashMap.put("1", CardTransTypeEnum.Auth.getCode());
//                stringStringHashMap.put("2", "Expenditure");
                break;
            case "CORRECTIVE_REFUND_VOID":
                stringStringHashMap.put("1", CardTransTypeEnum.CorrectiveRefundVoid.getCode());
                stringStringHashMap.put("2", "Expenditure");
                break;
            case "REFUND_REVERSAL":
                stringStringHashMap.put("1", CardTransTypeEnum.RefundReversal.getCode());
                stringStringHashMap.put("2", "Expenditure");
                break;
            case "SERVICE_FEE", "FEE", "fee":
                stringStringHashMap.put("1", CardTransTypeEnum.ServiceFee.getCode());
                stringStringHashMap.put("2", "Expenditure");
                break;
            default:
                break;
        }
        return stringStringHashMap;
    }

}





