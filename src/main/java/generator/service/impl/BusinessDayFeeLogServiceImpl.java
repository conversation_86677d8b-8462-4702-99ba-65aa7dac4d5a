package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.WalletTypeConstants;
import com.qf.zpay.constants.WalletTypeEnum;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.util.DateUtil;
import generator.domain.BusinessDayFeeLog;
import generator.domain.User;
import generator.domain.UserWallet;
import generator.mapper.BusinessDayFeeLogMapper;
import generator.mapper.UserMapper;
import generator.mapper.UserWalletLogMapper;
import generator.mapper.UserWalletMapper;
import generator.service.BusinessDayFeeLogService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Log4j2
@Service("businessDayFundLogService")
public class BusinessDayFeeLogServiceImpl extends ServiceImpl<BusinessDayFeeLogMapper, BusinessDayFeeLog> implements BusinessDayFeeLogService {
    @Autowired
    private UserMapper userService;
    @Autowired
    private BusinessDayFeeLogMapper businessDayFeeLogMapper;

    @Autowired
    private UserWalletLogMapper userWalletLogMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addFundLog() {

        log.info("-------------------资金流水——————————————开始");
        // 查询所有用户
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("status", 1);
        List<User> userList = userService.selectList(userQueryWrapper);
        int i = 0;

        // 获取前一天的日期 格式化为YYYY-MM-DD格式
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String format = today.format(formatter);
        String formattedDate = yesterday.format(formatter);
        List<String> walletTypes = Arrays.asList(
                WalletTypeConstants.STWALLET_TYPE_MAIN,
                WalletTypeConstants.STWALLET_TYPE_STORE,
                WalletTypeConstants.STWALLET_TYPE_SHARE,
                WalletTypeConstants.STWALLET_TYPE_PHYSICAL,
                WalletTypeConstants.STWALLET_TYPE_PAYMENT,
                WalletTypeConstants.STWALLET_TYPE_USDT,
                WalletTypeConstants.STWALLET_TYPE_TOKEN
        );
        if (userList != null) {
            for (User user : userList) {
                UserWallet userWallet = userWalletMapper.getOneByBusinessId(user.getBusinessId());
                log.error(userWallet);
                if (userWallet == null) {
                    continue;
                }
                //今日用户充值
                BigDecimal dayRecharge = userWalletLogMapper.queryCardAmount(formattedDate, null, user.getBusinessId(), WalletTypeEnum.RECHARGE.getCode());
                //今日用户充值手续费
                BigDecimal dayRechargeCharge = userWalletLogMapper.queryCardAmount(formattedDate, null, user.getBusinessId(), WalletTypeEnum.RECHARGE_FEE.getCode());
                //今日用户开卡手续费
                BigDecimal dayOpenCardCharge = userWalletLogMapper.queryCardAmount(formattedDate, null, user.getBusinessId(), WalletTypeEnum.OPEN_CARD_FEE.getCode());
                log.error("======================================");
                //今日支出（用户实际充值的值+商户支付给平台的手续费+商户支付给平台的开卡费+商户支付给平台的销卡费支出）
                List<Integer> subTypes = Arrays.asList(WalletTypeEnum.OPEN_CARD_FEE.getCode(), WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(), WalletTypeEnum.WITHDRAW_FEE.getCode());
                BigDecimal dayBusinessTotalExpenditure = userWalletLogMapper.queryCardMoneyAmount(formattedDate, null, user.getBusinessId(), subTypes);
                // 今日手续费支出根据平台设置给商户手续费
                List<Integer> list2 = Arrays.asList(WalletTypeEnum.RECHARGE_FEE.getCode(), WalletTypeEnum.COMMISSION_FEE.getCode(), WalletTypeEnum.CARD_CANCEL_FEE.getCode(), WalletTypeEnum.WITHDRAW_FEE.getCode());
                BigDecimal todayBusinessChargeExpenditure = userWalletLogMapper.queryCardMoneyAmount(formattedDate, null, user.getBusinessId(), list2);

                //今日开卡费支出
                /*根据平台设置给商户开卡费，依会员开卡数量进行数据采集。
                举例：平台设置给商户1U开卡费，商户设置给用户7U开卡费
                今日开卡200张
                今日开卡费支出为 1U x200张=200U（商户需要给平台的开卡费用成本）*/
                BigDecimal todayBusinessOpenCardFeeExpenditure = userWalletLogMapper.queryCardMoneyAmount(formattedDate, null, user.getBusinessId(), Collections.singletonList(WalletTypeEnum.OPEN_CARD_FEE.getCode()));

                // 商户充值
                BigDecimal merchantDeposit = userWalletLogMapper.queryCardAmount3(null, user.getBusinessId(), WalletTypeEnum.RECHARGE.getCode());

                // 商户提现
                BigDecimal merchantWithdrawal = userWalletLogMapper.queryCardAmount3(null, user.getBusinessId(), WalletTypeEnum.WITHDRAW.getCode());

                // 销卡余额返回
                BigDecimal cardCancellationBalanceReturn = userWalletLogMapper.queryCardAmount3(null, user.getBusinessId(), WalletTypeEnum.CARD_CANCEL_RETURN.getCode());

                // 销卡手续费
                BigDecimal cardCancellationFee = userWalletLogMapper.queryCardAmount3(null, user.getBusinessId(), WalletTypeEnum.CARD_CANCEL_FEE.getCode());
                QueryWrapper<BusinessDayFeeLog> objectQueryWrapper = new QueryWrapper<>();
                objectQueryWrapper.eq("business_id", user.getBusinessId()).eq("create_date", format);
                // 查询旧纪录
                BusinessDayFeeLog businessDayFeeLog = businessDayFeeLogMapper.selectOne(objectQueryWrapper);
                if (businessDayFeeLog != null) {
                    SetBusinessDayFeeLog(user, dayRecharge, dayRechargeCharge, dayOpenCardCharge, dayBusinessTotalExpenditure, todayBusinessChargeExpenditure, todayBusinessOpenCardFeeExpenditure, merchantDeposit, merchantWithdrawal, cardCancellationBalanceReturn, cardCancellationFee, businessDayFeeLog);
                    int i1 = businessDayFeeLogMapper.updateById(businessDayFeeLog);
                    if (i1 > 0) {
                        i++;
                    }
                } else {
                    BusinessDayFeeLog businessDayFeeLog1 = new BusinessDayFeeLog();
                    SetBusinessDayFeeLog(user, dayRecharge, dayRechargeCharge, dayOpenCardCharge, dayBusinessTotalExpenditure, todayBusinessChargeExpenditure, todayBusinessOpenCardFeeExpenditure, merchantDeposit, merchantWithdrawal, cardCancellationBalanceReturn, cardCancellationFee, businessDayFeeLog1);
                    int insert = businessDayFeeLogMapper.insert(businessDayFeeLog1);
                    if (insert > 0) {
                        i++;
                    }
                }
            }
        }
        log.info("统计资金流水任务 结束--------------" + today + "汇总了====" + formattedDate + "====的" + i + "条数据");
    }

    @Autowired
    private UserWalletMapper userWalletMapper;

    @Override
    public IPage<BusinessDayFeeLog> getFlowDetails(CardSettingsDto cardSettingsDto) {
        Page<BusinessDayFeeLog> page = new Page<>(cardSettingsDto.getPage(), cardSettingsDto.getPageSize());
        return businessDayFeeLogMapper.getFlowDetails(page, cardSettingsDto);
    }

    private void SetBusinessDayFeeLog(User user, BigDecimal dayRecharge, BigDecimal dayRechargeCharge,
                                      BigDecimal dayOpenCardCharge, BigDecimal dayBusinessTotalExpenditure, BigDecimal
                                              todayBusinessChargeExpenditure, BigDecimal todayBusinessOpenCardFeeExpenditure,
                                      BigDecimal merchantDeposit, BigDecimal merchantWithdrawal, BigDecimal cardCancellationBalanceReturn,
                                      BigDecimal cardCancellationFee, BusinessDayFeeLog businessDayFeeLog) {
        QueryWrapper<UserWallet> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("business_id", user.getBusinessId());
        UserWallet userWallet = userWalletMapper.selectOne(userQueryWrapper);
        businessDayFeeLog.setBusinessId(user.getBusinessId());
        businessDayFeeLog.setDayRecharge(dayRecharge);
        businessDayFeeLog.setDayRechargeCharge(dayRechargeCharge != null ? dayRechargeCharge.negate() : null);
        businessDayFeeLog.setDayOpenCardCharge(dayOpenCardCharge != null ? dayOpenCardCharge.negate() : null);
        businessDayFeeLog.setDayBusinessTotalExpenditure(dayBusinessTotalExpenditure != null ? dayBusinessTotalExpenditure.negate() : null);
        businessDayFeeLog.setDayBusinessChargeExpenditure(todayBusinessChargeExpenditure != null ? todayBusinessChargeExpenditure.negate() : null);
        businessDayFeeLog.setDayBusinessOpenCardFeeExpenditure(todayBusinessOpenCardFeeExpenditure != null ? todayBusinessOpenCardFeeExpenditure.negate() : null);
        businessDayFeeLog.setDayBusinessBalance(userWallet == null ? BigDecimal.ZERO : userWallet.getMainWallet());
        // 获取当前日期
        Date today = new Date();

        // 使用 Calendar 类减一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date yesterday = calendar.getTime();
        businessDayFeeLog.setCreateTime(yesterday);
        businessDayFeeLog.setCreateDate(DateUtil.getDate(new Date()));
        businessDayFeeLog.setMerchantDeposit(merchantDeposit);
        businessDayFeeLog.setMerchantWithdrawal(merchantWithdrawal);
        businessDayFeeLog.setCardCancellationBalanceReturn(cardCancellationBalanceReturn);
        businessDayFeeLog.setCardCancellationFee(cardCancellationFee != null ? cardCancellationFee.negate() : null);
    }


    public List<BusinessDayFeeLog> flowOfFunds(Integer businessId) {
        return businessDayFeeLogMapper.flowOfFunds(businessId);
    }
}
