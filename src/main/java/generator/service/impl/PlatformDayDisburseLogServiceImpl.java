package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.PlatformDayDisburseLog;
import generator.service.PlatformDayDisburseLogService;
import generator.mapper.PlatformDayDisburseLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tb_platform_day_disburse_log】的数据库操作Service实现
* @createDate 2024-06-05 10:16:39
*/
@Service
public class PlatformDayDisburseLogServiceImpl extends ServiceImpl<PlatformDayDisburseLogMapper, PlatformDayDisburseLog>
    implements PlatformDayDisburseLogService{

}




