package generator.service.impl;


import org.springframework.stereotype.Service;
import generator.service.PlatformDayDisburseLogService;
import generator.domain.PlatformDayDisburseLog;
import generator.mapper.PlatformDayDisburseLogMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PlatformDayDisburseLogServiceImpl extends ServiceImpl<PlatformDayDisburseLogMapper, PlatformDayDisburseLog> implements PlatformDayDisburseLogService {

}