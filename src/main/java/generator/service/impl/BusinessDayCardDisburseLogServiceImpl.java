package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.res.v2.ExpendDto;
import generator.domain.BusinessDayCardDisburseLog;
import generator.domain.CardActionLog;
import generator.mapper.BusinessDayCardDisburseLogMapper;
import generator.mapper.CardActionLogMapper;
import generator.service.BusinessDayCardDisburseLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BusinessDayCardDisburseLogServiceImpl extends ServiceImpl<BusinessDayCardDisburseLogMapper, BusinessDayCardDisburseLog> implements BusinessDayCardDisburseLogService {

    @Autowired
    private CardActionLogMapper cardActionLogMapper;

    @Override
    public ExpendDto expendStatistics(Integer businessId) {
        ExpendDto dto = new ExpendDto();
        // 总支出
        CardActionLog log = cardActionLogMapper.findExpendAll(businessId);
        // 开卡、手续费、销卡
        List<CardActionLog> expendAllByTypes = cardActionLogMapper.findExpendAllByType(businessId, 1);
        List<CardActionLog> cancelCard1 = expendAllByTypes.stream().filter(m -> 1 == (m.getType())).collect(Collectors.toList());
        List<CardActionLog> cancelCard2 = expendAllByTypes.stream().filter(m -> 2 == (m.getType())).collect(Collectors.toList());
        List<CardActionLog> cancelCard3 = expendAllByTypes.stream().filter(m -> 3 == (m.getType())).collect(Collectors.toList());
        dto.setBusinessId(businessId);
        dto.setChargeTotalExpenditure(null == log ? new BigDecimal(0.0) : log.getPlatformFee());
        dto.setOpenCardFeeExpenditure(cancelCard1.isEmpty() ? new BigDecimal(0.0) : cancelCard1.get(0).getPlatformFee());
        dto.setRechargeChargeCardExpenditure(cancelCard2.isEmpty() ? new BigDecimal(0.0) : cancelCard2.get(0).getPlatformFee());
        dto.setCardCancellationExpenditure(cancelCard3.isEmpty() ? new BigDecimal(0.0) : cancelCard3.get(0).getPlatformFee());
        return dto;
    }
}
