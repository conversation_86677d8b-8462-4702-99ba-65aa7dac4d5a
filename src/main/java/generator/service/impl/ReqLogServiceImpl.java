package generator.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.constants.CardStatusEnum;
import com.qf.zpay.exception.UnAuthException;
import com.qf.zpay.response.ResultCode;
import com.qf.zpay.service.card.CardBuilder;
import com.qf.zpay.service.card.CardFactory;
import com.qf.zpay.service.card.ChannelConfig;
import com.qf.zpay.service.card.channel.Photon;
import generator.domain.CbLog;
import generator.domain.ReqLog;
import generator.domain.UserCard;
import generator.domain.UserCardLog;
import generator.mapper.CbLogMapper;
import generator.mapper.UserCardLogMapper;
import generator.mapper.UserCardMapper;
import generator.service.ReqLogService;
import generator.mapper.ReqLogMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.core.type.TypeReference;


/**
* <AUTHOR>
* @description 针对表【tb_req_log(回调记录)】的数据库操作Service实现
* @createDate 2024-06-05 10:16:39
*/
@Log4j2
@Service
public class ReqLogServiceImpl extends ServiceImpl<ReqLogMapper, ReqLog>
    implements ReqLogService{


}




