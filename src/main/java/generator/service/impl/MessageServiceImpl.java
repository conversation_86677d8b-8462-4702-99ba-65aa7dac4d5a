package generator.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserCardFeeConfigDto;
import generator.domain.Message;
import generator.service.MessageService;
import generator.mapper.MessageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tb_message】的数据库操作Service实现
* @createDate 2024-06-05 10:16:39
*/
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message>
    implements MessageService{
    @Autowired
    private MessageMapper messageMapper;
    @Override
    public IPage<Message> getAnnouncementList(CardSettingsDto cardSettingsDto) {
        Page<UserCardFeeConfigDto> page = new Page<>(cardSettingsDto.getPage(),cardSettingsDto.getPageSize());
        return messageMapper.getAnnouncementList(page,cardSettingsDto);
    }
}




