package generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.service.pojo.CardTransDto;
import generator.domain.CardTransFeeLog;
import generator.domain.UserCard;
import generator.mapper.CardTransFeeLogMapper;
import generator.service.CardTransFeeLogService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_action_log(卡片操作日志)】的数据库操作Service实现
 * @createDate 2024-06-05 10:16:39
 */
@Service
public class CardTransFeeLogServiceImpl extends ServiceImpl<CardTransFeeLogMapper, CardTransFeeLog> implements CardTransFeeLogService {


    /**
     * 手续费日志添加
     *
     * @param dto
     * @param userCard
     * @param platformFee
     * @param rechargeMoney
     */
    @Override
    public void cardTransFeeLogSave(CardTransDto dto, UserCard userCard, BigDecimal platformFee, BigDecimal rechargeMoney) {
        CardTransFeeLog one = this.getOne(new LambdaQueryWrapper<CardTransFeeLog>()
                .eq(CardTransFeeLog::getCardId, dto.getCardId())
                .eq(CardTransFeeLog::getBusinessId, userCard.getUid())
                .orderByDesc(CardTransFeeLog::getCreateTime)
                .last("limit 1"));
        Date date = new Date();
        CardTransFeeLog cardTransFeeLog = new CardTransFeeLog();
        cardTransFeeLog.setMoney(dto.getAuthAmount());
        cardTransFeeLog.setAccruingAmounts(one == null ? dto.getChangeFee() : one.getAccruingAmounts().add(dto.getChangeFee()));
        cardTransFeeLog.setChangeFee(dto.getChangeFee());
        cardTransFeeLog.setCardId(userCard.getCardId());
        cardTransFeeLog.setBusinessId(userCard.getUid());
        cardTransFeeLog.setCostFee(dto.getTotalFee());
        cardTransFeeLog.setPlatformFee(platformFee);
        cardTransFeeLog.setPlatformEarn(platformFee.compareTo(BigDecimal.valueOf(0.00)) == 0 ? platformFee : rechargeMoney.negate());
        cardTransFeeLog.setOrderId(dto.getOrderId());
        cardTransFeeLog.setCardModel(userCard.getCardModel());
        cardTransFeeLog.setCreateTime(date);
        cardTransFeeLog.setCreateDate(date);
        cardTransFeeLog.setStatus("success");
        cardTransFeeLog.setTransactionType(dto.getTransactionTypeEnum().getCode());
        cardTransFeeLog.setPlatformFeeDetail(dto.getPlatformFeeDetail());
        cardTransFeeLog.setBusinessFeeDetail(dto.getBusinessFeeDetail());
        this.save(cardTransFeeLog);
    }
}




