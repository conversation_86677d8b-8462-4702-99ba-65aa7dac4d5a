package generator.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qf.zpay.dto.res.v2.EarningsDto;
import generator.domain.BusinessDayCardEarnLog;
import generator.domain.CardActionLog;
import generator.mapper.BusinessDayCardEarnLogMapper;
import generator.mapper.CardActionLogMapper;
import generator.mapper.UserWalletLogMapper;
import generator.service.BusinessDayCardEarnLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BusinessDayCardEarnLogServiceImpl extends ServiceImpl<BusinessDayCardEarnLogMapper, BusinessDayCardEarnLog> implements BusinessDayCardEarnLogService {

    @Autowired
    private UserWalletLogMapper userWalletLogMapper;
    @Autowired
    private CardActionLogMapper cardActionLogMapper;

    @Override
    public EarningsDto journalAccountStatistics(Integer businessId) {
        // 创建一个Calendar实例，并设置为当前时间和时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        Date previousDay = calendar.getTime();
        String startTime = DateUtil.format(previousDay, "yyyy-MM-dd 00:00:00"); //"2024-10-10 00:00:00";//
        String endTime = DateUtil.format(previousDay, "yyyy-MM-dd 23:59:59");//"2024-10-10 23:59:59";//
        // 卡模式     RECHARGE("recharge", "充值"),SHARE("share", "分享"),PHYSICAL("physical", "实体卡");
        List<String> models = Arrays.asList("recharge", "share", "physical");
        EarningsDto dto = new EarningsDto();
        // 今日收益
        BigDecimal dayEarnings = new BigDecimal(0.0);
        // 开卡收益
        BigDecimal dayOpenCardEarningsSum = new BigDecimal(0.0);
        // 手续费收益
        BigDecimal dayRechargeChargeCardEarningsSum = new BigDecimal(0.0);
        for (String model : models) {
            List<CardActionLog> modelList = cardActionLogMapper.findListByModel(businessId, startTime, endTime, model);
            List<CardActionLog> openCard = modelList.stream().filter(m -> 1 == (m.getType())).collect(Collectors.toList());
            // 开卡（卡费收益）
            BigDecimal dayOpenCardEarnings = new BigDecimal(0.0);
            if (!openCard.isEmpty()) {
                CardActionLog cardActionLog = openCard.get(0);
                dayOpenCardEarnings = cardActionLog.getBusinessEarn();
            }
            // 充值（手续费收益）
            BigDecimal dayRechargeChargeCardEarnings = new BigDecimal(0.0);
            List<CardActionLog> topUp = modelList.stream().filter(m -> 2 == (m.getType())).collect(Collectors.toList());
            if (!topUp.isEmpty()) {
                CardActionLog cardActionLog = topUp.get(0);
                dayRechargeChargeCardEarnings = cardActionLog.getBusinessEarn();
            }
            // 3销卡收益
            BigDecimal cancelCardEarnings = new BigDecimal(0.0);
            List<CardActionLog> cancelCard = modelList.stream().filter(m -> 3 == (m.getType())).collect(Collectors.toList());
            if (!cancelCard.isEmpty()) {
                CardActionLog cardActionLog = cancelCard.get(0);
                cancelCardEarnings = cardActionLog.getBusinessEarn();
            }
            // 今日总收益
            dayEarnings = dayEarnings.add(dayOpenCardEarnings).add(dayRechargeChargeCardEarnings).add(cancelCardEarnings);
            // 开卡收益
            dayOpenCardEarningsSum = dayOpenCardEarningsSum.add(dayOpenCardEarnings);
            // 手续费收益
            dayRechargeChargeCardEarningsSum = dayRechargeChargeCardEarningsSum.add(dayRechargeChargeCardEarnings);

        }
        // 历史总收益
        String historyEarningsStr = cardActionLogMapper.findTotalRevenueAll(businessId);
        BigDecimal historyEarnings = StringUtils.isEmpty(historyEarningsStr) ? new BigDecimal(0.0) : new BigDecimal(historyEarningsStr);
        dto.setBusinessId(businessId);
        dto.setDayOpenCardEarnings(dayOpenCardEarningsSum);
        dto.setDayRechargeChargeCardEarnings(dayRechargeChargeCardEarningsSum);
        dto.setDayEarnings(dayEarnings);
        dto.setHistoryEarnings(historyEarnings);
        return dto;
    }
}
