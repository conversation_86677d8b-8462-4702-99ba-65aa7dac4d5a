package generator.service;


import com.mybatisflex.core.service.IService;
import com.zpay.admin.job.dto.StatInOutDto;
import generator.domain.CoinInOut;

import java.util.HashMap;

/**
 * 服务层。
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface CoinInOutService extends IService<CoinInOut> {

    /**
     * 统计指定日期商户冲提
     *
     * @param dateStr 日期
     * @return 数据
     */
    HashMap<Integer, StatInOutDto> stat(String dateStr);


    /**
     * 冲提统计按商户日期
     *
     * @return businessId -> date -> data
     */
    HashMap<Integer, HashMap<String, StatInOutDto>> statOrderByBusiness();

    /**
     * 冲提统计按日期、商户
     *
     * @return date -> businessId -> data
     */
    HashMap<String, HashMap<Integer, StatInOutDto>> statOrderByDate();
}