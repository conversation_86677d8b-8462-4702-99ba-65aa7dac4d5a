package generator.service;

import cn.hutool.json.JSONObject;
import generator.domain.CoinInOut;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【tb_coin_in_out】的数据库操作Service
 * @createDate 2024-07-19 14:59:27
 */
public interface CoinInOutService extends IService<CoinInOut> {

    /**
     * 处理充值
     *
     * @param data 充值返回数据
     */
    void rechargeNow(JSONObject data);

}
