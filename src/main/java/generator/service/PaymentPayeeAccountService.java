package generator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import generator.domain.PaymentPayeeAccount;
import generator.domain.User;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【tb_payment_payee_account】的数据库操作Service
* @createDate 2024-07-08 09:37:14
*/
public interface PaymentPayeeAccountService extends IService<PaymentPayeeAccount> {
    List<PaymentPayeeAccount> accountList(User user, String payeeId);

    PaymentPayeeAccount getByAccountId(String accountId);

    void deleteAccount(String accountId);

    PaymentPayeeAccount getByAccountNoAndBusinessId(Integer businessId, String accountNo, String paymentMethod);

    PaymentPayeeAccount getByAccountAndPaymentMethod(String accountNo, String paymentMethod);
}
