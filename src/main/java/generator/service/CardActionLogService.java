package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardActionLogDto;
import com.qf.zpay.dto.req.CardSettingsDto;
import generator.domain.CardActionLog;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 针对表【tb_card_action_log(卡片操作日志)】的数据库操作Service
 * @createDate 2024-06-05 10:16:39
 */
public interface CardActionLogService extends IService<CardActionLog> {

    /**
     * 卡片历史
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片历史分页数据详情
     */
    IPage<CardActionLogDto> getCardHistory(CardSettingsDto cardSettingsDto);

    /**
     * 卡片历史导出
     *
     * @param cardSettingsDto 筛选条件
     * @param response        请求对象
     * @param request         响应数据
     * @throws IOException ；
     */
    void exportCardHistory(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException;


    /**
     * 开卡流水统计入库
     */
    void journalAccountStatistics();

    /**
     * 支出流水统计入库
     */
    void expendStatistics();

}
