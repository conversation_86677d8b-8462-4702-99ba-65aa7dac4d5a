package generator.service;


import com.mybatisflex.core.service.IService;
import com.zpay.admin.job.dto.StatCardFundDto;
import generator.domain.CardActionLog;

/**
 * 卡片操作日志 服务层。
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface CardActionLogService extends IService<CardActionLog> {

    /**
     * 统计卡片服务数据
     *
     * @param statDate   统计时间
     * @param businessId 商户ID
     * @return StatCardDto
     */
    StatCardFundDto statCard(String statDate, Integer businessId);

    StatCardFundDto statPlatformCard(String statDate);

    StatCardFundDto statPlatformCardToday();

    StatCardFundDto statBusinessCardToday(Integer businessId);
}