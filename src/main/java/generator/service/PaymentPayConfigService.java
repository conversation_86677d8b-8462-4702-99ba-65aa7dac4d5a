package generator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import generator.domain.PaymentPayConfig;

import java.util.HashMap;
import java.util.List;

public interface PaymentPayConfigService extends IService<PaymentPayConfig> {
    List<PaymentPayConfig> availablePaymentMethods(String country);

    HashMap<String, String> getProductType(String product);

    List<PaymentPayConfig> getCurrencyAndCountry(String currency, String country);
}