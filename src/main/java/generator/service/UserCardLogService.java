package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.dto.req.CardSettingsDto;
import com.qf.zpay.dto.req.UserCardLogDto;
import com.qf.zpay.dto.req.UserCardTradeStatDto;
import generator.domain.UserCardLog;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_log】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserCardLogService extends IService<UserCardLog> {

    IPage<UserCardLog> cardLogs(Integer businessId, String cardId, Integer page, Integer pageSize, String transactionType);

    BigDecimal queryCardAmount(String formattedDate, String noticeType, Integer businessId, String status);

    /**
     * 交易流水分页数据
     *
     * @param businessId 商户id
     * @param cardNumber 卡号
     * @param page       当前页
     * @param pageSize   条数
     * @return 分页数据
     */
    IPage<UserCardLog> cardLogsDetail(Integer businessId, String cardNumber, Integer page, Integer pageSize);

    /**
     * 卡片交易详情
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片交易分页数据详情
     */
    IPage<UserCardLogDto> getCardTransactionDetails(CardSettingsDto cardSettingsDto);

    /**
     * 卡片明细
     *
     * @param cardSettingsDto 筛选条件
     * @return 卡片明细数据详情
     */
    IPage<UserCardLogDto> getShareCardsDetails(CardSettingsDto cardSettingsDto);

    /**
     * 共享or储值卡下载导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    void export(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException;

    /**
     * 卡片交易详情导出
     *
     * @param cardSettingsDto 筛选条件
     * @param request         请求
     * @param response        响应
     * @throws IOException ；
     */
    void exportCardTransactionDetails(CardSettingsDto cardSettingsDto, HttpServletResponse response, HttpServletRequest request) throws IOException;

    /**
     * 卡片交易成功失败详情
     *
     * @param orderId 订单号
     * @param cardId  卡片id
     * @return 订单详情
     */
    List<UserCardLog> selectById(String orderId, String cardId);

    /**
     * 卡片日志分页
     *
     * @param cardId 卡状态
     * @param page   页码
     * @param size   每页数量
     * @return IPage<UserCard>
     */
    IPage<UserCardLog> cardLogPage(
            String cardId,
            Integer page,
            Integer size
    );

    /**
     * 根据 orderId 获取关联日志（包含手续费
     *
     * @param orderId 交易号
     * @return List<UserCardLog>
     */
    List<UserCardLog> cardLogDetail(String orderId);

    Boolean isOrderIdExists(String channelOrderNo);

    void transFeeSave(UserCardLog userCardLog, BigDecimal platformFee, String fundsDirection);

    UserCardTradeStatDto cardTransactionDetailsStat(CardSettingsDto cardSettingsDto);
}
