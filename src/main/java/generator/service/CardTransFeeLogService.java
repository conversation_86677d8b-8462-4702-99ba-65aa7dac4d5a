package generator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qf.zpay.service.pojo.CardTransDto;
import generator.domain.CardTransFeeLog;
import generator.domain.UserCard;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【CardTansFeeLog(卡片操作日志)】的数据库操作Service
 * @createDate 2024-06-05 10:16:39
 */
public interface CardTransFeeLogService extends IService<CardTransFeeLog> {

    /**
     * @param dto
     * @param userCard
     * @param platformFee   平台费用
     * @param rechargeMoney 收益
     */
    void cardTransFeeLogSave(CardTransDto dto, UserCard userCard, BigDecimal platformFee, BigDecimal rechargeMoney);

}
