package generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qf.zpay.dto.common.CardProductDto;
import generator.domain.UserCard;
import generator.domain.UserCardManage;
import com.baomidou.mybatisplus.extension.service.IService;
import generator.vo.CardManageWithFeeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_card_manage】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserCardManageService extends IService<UserCardManage> {

    /**
     * 获取卡片列表
     *
     * @param businessId
     * @param page
     * @param pageSize
     * @return
     */
    IPage<UserCard> getCardList(Integer businessId, Integer page, Integer pageSize);

    /**
     * 检查并获取卡片产品信息
     *
     * @param cardManageId 卡片产品ID
     * @return UserCardManage
     */
    UserCardManage getAndCheckCardManage(Integer cardManageId);


    /**
     * 获取卡片产品信息
     *
     * @param cardManageId 卡片产品ID
     * @return UserCardManage
     */
    UserCardManage getCardManage(Integer cardManageId);

    /**
     * 获取卡片产品信息 包含渠道、费率、管理等信息
     *
     * @param cardManageId 卡片产品ID
     * @param businessId   商户ID
     * @return CardProductDto
     */
    CardProductDto getAndCheckCardProductInfo(Integer cardManageId, Integer businessId);


    CardProductDto getCardProductInfo(Integer cardManageId, Integer businessId);


    /**
     * 获取商户全部卡段和费率配置
     *
     * @param businessId 商户ID
     * @return List<CardManageWithFeeVo>
     */
    List<CardManageWithFeeVo> getBusinessCardManageWithFee(Integer businessId);

    /**
     * 获取商户全部卡段和费率配置
     *
     * @param businessId   商户ID
     * @param cardManageId 卡片产品ID
     * @return CardManageWithFeeVo
     */
    CardManageWithFeeVo getOneBusinessCardManageWithFee(Integer businessId, Integer cardManageId);


    Integer getBusinessCardManageOpenCards(Integer businessId, Integer cardManageId);


    /**
     * 初始化商户卡段
     *
     * @param id 商户id
     */
    void initFeeConfig(Integer id);
}
