package generator.service;

import generator.domain.UserMoneyLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_user_money_log】的数据库操作Service
* @createDate 2024-06-05 10:16:58
*/
public interface UserMoneyLogService extends IService<UserMoneyLog> {

    BigDecimal queryCardMoneyAmount(String formattedDate, Integer businessId, Integer subType);

    BigDecimal getDayChargeTotal(String formattedDate, Integer businessId, List<Integer> subTypes);

    BigDecimal getDayMoneyAmount(Integer businessId, List<Integer> subTypes);

    BigDecimal queryCardMoneyAmountSum(Integer businessId, Integer subType);
}
