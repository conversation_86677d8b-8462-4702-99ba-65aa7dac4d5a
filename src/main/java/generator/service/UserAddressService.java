package generator.service;

import generator.domain.User;
import generator.domain.UserAddress;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【tb_user_address】的数据库操作Service
 * @createDate 2024-06-05 10:16:58
 */
public interface UserAddressService extends IService<UserAddress> {

    UserAddress getBusinessAddress(Integer userId);


    User getUserByAddress(String address, Integer type);
}
