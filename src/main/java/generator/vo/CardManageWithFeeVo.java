package generator.vo;

import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @updateTime 2024/7/10 15:24
 */
@Data
public class CardManageWithFeeVo {

    /**
     * 卡片类别
     */
    private Integer cardCategory;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡归属
     */
    private CardSchemeEnum cardBelongTo;

    /**
     * 应用场景
     */
    private String cardChangjing;

    /**
     * 发卡国家
     */
    private String cardSourceCountry;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    private BigDecimal cardChuzhiMax;

    /**
     * 卡背图
     */
    private String cardImg;

    /**
     *
     */
    private CardModelEnum cardModel;

    /**
     *
     */
    private Integer cardStatus;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    /**
     * 卡段
     */
    private String cardBin;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;
    /**
     * 开卡数
     */
    private Integer numberOfCardsThatCanBeOpened;

    /**
     * 成本开卡费
     */
    private BigDecimal costOpenCardFee;

    /**
     * 成本充值手续费%
     */
    private BigDecimal costChargeRate;

    /**
     * 成本销卡费
     */
    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;


    private BigDecimal firstRecharge;

    /**
     * 上游渠道
     */
    private CardChannelEnum channel;


    /**
     * 产品编码
     */
    private String productCode;


}
