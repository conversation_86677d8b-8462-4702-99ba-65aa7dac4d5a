package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_payment_pay_order")
public class PaymentPayOrder {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 订单号
     */
    @Column(value = "order_id")
    private String orderId;

    /**
     * 收款人id
     */
    @Column(value = "payee_id")
    private Integer payeeId;

    /**
     * 付款方式
     */
    @Column(value = "payment_method")
    private String paymentMethod;

    /**
     * 到账金额
     */
    @Column(value = "amount_received")
    private BigDecimal amountReceived;

    /**
     * 付款金额
     */
    @Column(value = "payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 手续费
     */
    @Column(value = "fee")
    private BigDecimal fee;

    /**
     * 单笔手续费
     */
    @Column(value = "total_fee")
    private BigDecimal totalFee;

    /**
     * 付款费率
     */
    @Column(value = "payment_rate")
    private BigDecimal paymentRate;

    /**
     * 上游总收费
     */
    @Column(value = "cost_total_money")
    private BigDecimal costTotalMoney;

    /**
     * 上游手续费
     */
    @Column(value = "cost_fee")
    private BigDecimal costFee;

    /**
     * 付款人
     */
    @Column(value = "payer")
    private String payer;

    /**
     * 付款用途
     */
    @Column(value = "payment_purpose")
    private String paymentPurpose;

    /**
     * 交易状态
     */
    @Column(value = "status")
    private String status;

    /**
     * 失败原因
     */
    @Column(value = "failure_reason")
    private String failureReason;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 付款时间
     */
    @Column(value = "payment_time")
    private Date paymentTime;

    /**
     * 锁汇过期时间
     */
    @Column(value = "completion_time")
    private Date completionTime;

    /**
     * 锁汇id
     */
    @Column(value = "quote_id")
    private String quoteId;

    @Column(value = "business_id")
    private String businessId;

    /**
     * 上游订单号
     */
    @Column(value = "upstream_order_id")
    private String upstreamOrderId;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    @Column(value = "cost_payment_rate")
    private BigDecimal costPaymentRate;


}
