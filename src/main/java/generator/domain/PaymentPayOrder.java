package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付订单实体类
 */
@Data
@TableName("tb_payment_pay_order")
public class PaymentPayOrder implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 收款人id
     */
    private Integer payeeId;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 上游付款汇率
     */
    private BigDecimal paymentRate;

    /**
     * 到账币种，默认为CNY
     */
    private String currencyReceived;

    /**
     * 到账金额
     */
    private BigDecimal amountReceived;

    /**
     * 付款金额（包含手续费）
     */
    private BigDecimal paymentAmount;

    /**
     * 上游手续费
     */
    private BigDecimal costFee;

    /**
     * 上游总收费
     */
    private BigDecimal costTotalMoney;

    /**
     * 平台单笔手续费
     */
    private BigDecimal platformFee;

    /**
     * 平台手续费率
     */
    private BigDecimal platformFeeRate;

    /**
     * 平台总手续费
     */
    private BigDecimal platformFeeTotal;

    /**
     * 商户手续费
     */
    private BigDecimal businessFee;

    /**
     * 商户费率
     */
    private BigDecimal businessFeeRate;

    /**
     * 商户总手续费
     */
    private BigDecimal businessFeeTotal;

    /**
     * 付款人
     */
    private String payer;

    /**
     * 付款用途
     */
    private String paymentPurpose;

    /**
     * 交易状态
     * CREATED	已创建
     * PAID	已付款
     * REVIEWING 审核中
     * PENDING	付款渠道处理中
     * REJECTED	已拒绝
     * COMPLETED 已完成
     * FAILED 付款失败
     * CANCELLED 已取消
     */
    private String status;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 锁汇过期时间
     */
    private Date completionTime;

    /**
     * 锁汇id
     */
    private String quoteId;

    /**
     * 业务ID
     */
    private Integer businessId;

    /**
     * 上游订单号
     */
    private String upstreamOrderId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 成本付款汇率
     */
    private BigDecimal costPaymentRate;

    /**
     * 付款账户
     */
    private String paymentAccount;
}