package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 钱包垫付操作日志
 *
 * @TableName user_wallet_advance_log
 */
@TableName(value = "tb_user_wallet_advance_log")
@Data
public class UserWalletAdvanceLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 卡片Id
     */
    private String cardId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 商户id
     */
    private Integer businessId;


    /**
     * 成本费
     */
    private BigDecimal costFee;

    /**
     * 平台费
     */
    private BigDecimal platformFee;
    /**
     * 变动
     */
    private BigDecimal changeFee;
    /**
     * 平台费详情
     */
    private String platformFeeDetail;
    /**
     * 上游费详情
     */
    private String businessFeeDetail;
    /**
     * 累计
     */
    private BigDecimal accruingAmounts;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;


    /**
     * 扣款状态（0：待扣款，1：已扣款，2：垫付）
     */
    private Integer state;
    /**
     * 钱包流水好
     */
    private String orderId;


}