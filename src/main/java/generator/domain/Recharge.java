package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_recharge")
public class Recharge {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 用户id
     */
    @Column(value = "user_id")
    private Integer userId;

    /**
     * 币种类型 1:BEP    2:ERC   3:TRC
     */
    @Column(value = "coin_type")
    private Integer coinType;

    @Column(value = "from_address")
    private String fromAddress;

    @Column(value = "tron_private_key")
    private String tronPrivateKey;

    /**
     * 1已同步充值数据2已充值到用户钱包+已转手续费 3已归集 入账 4:忽略不用处理
     */
    @Column(value = "is_status")
    private Integer isStatus;

    /**
     * 归集金额
     */
    @Column(value = "amount")
    private BigDecimal amount;

    /**
     * 充值hash
     */
    @Column(value = "hash")
    private String hash;

    @Column(value = "charge_hash")
    private String chargeHash;

    /**
     * 归hash
     */
    @Column(value = "deal_hash")
    private String dealHash;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "is_error")
    private Integer isError;


}
