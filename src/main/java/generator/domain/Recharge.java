package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_recharge
 */
@TableName(value ="tb_recharge")
@Data
public class Recharge implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 币种类型 1:BEP    2:ERC   3:TRC
     */
    private Integer coinType;

    /**
     * 
     */
    private String fromAddress;

    /**
     * 
     */
    private String tronPrivateKey;

    /**
     * 1已同步充值数据2已充值到用户钱包+已转手续费 3已归集 入账 4:忽略不用处理
     */
    private Integer isStatus;

    /**
     * 归集金额
     */
    private BigDecimal amount;

    /**
     * 充值hash
     */
    private String hash;

    /**
     * 
     */
    private String chargeHash;

    /**
     * 归hash
     */
    private String dealHash;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 
     */
    private Integer isError;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Recharge other = (Recharge) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCoinType() == null ? other.getCoinType() == null : this.getCoinType().equals(other.getCoinType()))
            && (this.getFromAddress() == null ? other.getFromAddress() == null : this.getFromAddress().equals(other.getFromAddress()))
            && (this.getTronPrivateKey() == null ? other.getTronPrivateKey() == null : this.getTronPrivateKey().equals(other.getTronPrivateKey()))
            && (this.getIsStatus() == null ? other.getIsStatus() == null : this.getIsStatus().equals(other.getIsStatus()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getHash() == null ? other.getHash() == null : this.getHash().equals(other.getHash()))
            && (this.getChargeHash() == null ? other.getChargeHash() == null : this.getChargeHash().equals(other.getChargeHash()))
            && (this.getDealHash() == null ? other.getDealHash() == null : this.getDealHash().equals(other.getDealHash()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsError() == null ? other.getIsError() == null : this.getIsError().equals(other.getIsError()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCoinType() == null) ? 0 : getCoinType().hashCode());
        result = prime * result + ((getFromAddress() == null) ? 0 : getFromAddress().hashCode());
        result = prime * result + ((getTronPrivateKey() == null) ? 0 : getTronPrivateKey().hashCode());
        result = prime * result + ((getIsStatus() == null) ? 0 : getIsStatus().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getHash() == null) ? 0 : getHash().hashCode());
        result = prime * result + ((getChargeHash() == null) ? 0 : getChargeHash().hashCode());
        result = prime * result + ((getDealHash() == null) ? 0 : getDealHash().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsError() == null) ? 0 : getIsError().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", coinType=").append(coinType);
        sb.append(", fromAddress=").append(fromAddress);
        sb.append(", tronPrivateKey=").append(tronPrivateKey);
        sb.append(", isStatus=").append(isStatus);
        sb.append(", amount=").append(amount);
        sb.append(", hash=").append(hash);
        sb.append(", chargeHash=").append(chargeHash);
        sb.append(", dealHash=").append(dealHash);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isError=").append(isError);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}