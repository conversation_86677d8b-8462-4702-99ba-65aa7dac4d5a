package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_coin_in_out")
public class CoinInOut {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户 ID
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 1 充值 2 提现
     */
    @Column(value = "trans_type")
    private Integer transType;

    /**
     * 1    ERC20 ETH56  BEP20 BSC137 MATIC POLYGON195 TRC20 TRON200 Solana Solana
     */
    @Column(value = "coin_type")
    private Integer coinType;

    /**
     * 来源地址
     */
    @Column(value = "from_address")
    private String fromAddress;

    /**
     * 到账地址
     */
    @Column(value = "to_address")
    private String toAddress;

    /**
     * 交易Hash
     */
    @Column(value = "hash")
    private String hash;

    /**
     * 金额
     */
    @Column(value = "amount")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @Column(value = "fee")
    private BigDecimal fee;

    /**
     * 0 待审核 1已驳回  2 处理中  9 成功
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(value = "update_time")
    private Date updateTime;


}
