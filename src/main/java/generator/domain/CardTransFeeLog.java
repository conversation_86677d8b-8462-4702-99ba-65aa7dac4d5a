package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qf.zpay.constants.CardModelEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片操作日志
 *
 * @TableName tb_card_trans_fee_log
 */
@TableName(value = "tb_card_trans_fee_log")
@Data
public class CardTransFeeLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 记录金额
     */
    private BigDecimal money;

    /**
     * 卡片Id
     */
    private String cardId;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 成本费
     */
    private BigDecimal costFee;


    /**
     * 平台费用
     */
    private BigDecimal platformFee;
    /**
     * 平台费详情
     */
    private String platformFeeDetail;
    /**
     * 上游费详情
     */
    private String businessFeeDetail;

    /**
     * dsinessRate;
     * <p>
     * /**
     * 平台收益
     */
    private BigDecimal platformEarn;

    /**
     * 交易Id
     */
    private String orderId;
    /**
     * 状态 success 成功 failed 失败 doing 进行中
     */
    private String status;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建日期
     */
    private Date createDate;


    /**
     * 累计
     */
    private BigDecimal accruingAmounts;

    /**
     * 变动金额
     */
    private BigDecimal changeFee;

    /**
     * 预授权时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date authTime;
}