package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片费率配置表
 *
 * @TableName tb_user_card_fee_config
 */
@TableName(value = "tb_user_card_fee_config")
@Data
public class UserCardFeeConfig implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    private Integer cardManageId;

    /**
     * 卡段
     */
    private String cardBin;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    private Integer isAble;
    /**
     * 开卡数
     */
    private Integer numberOfCardsThatCanBeOpened;

    /**
     * 成本开卡费
     */
    private BigDecimal costOpenCardFee;

    /**
     * 成本充值手续费%
     */
    private BigDecimal costChargeRate;

    /**
     * 成本销卡费
     */
    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    private BigDecimal businessCancelCardFee;

    /**
     * 卡段创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String updateBy;

    private BigDecimal firstRecharge;

    private BigDecimal cardMonthFee;
    private BigDecimal cardSingleLimit;
    private BigDecimal cardMonthLimit;
    private BigDecimal cardTotalLimit;

    /**
     * 授权手续费
     */
    private BigDecimal preAuthFee;
    /**
     * 跨境手续费率%
     */
    private BigDecimal crossBroadFeeRate;
    /**
     * 交易手续费率%
     */
    private BigDecimal transactionFeeRate;
    /**
     * 退款手续费
     */
    private BigDecimal refundFee;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserCardFeeConfig other = (UserCardFeeConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
                && (this.getCardManageId() == null ? other.getCardManageId() == null : this.getCardManageId().equals(other.getCardManageId()))
                && (this.getCardBin() == null ? other.getCardBin() == null : this.getCardBin().equals(other.getCardBin()))
                && (this.getIsAble() == null ? other.getIsAble() == null : this.getIsAble().equals(other.getIsAble()))
                && (this.getCostOpenCardFee() == null ? other.getCostOpenCardFee() == null : this.getCostOpenCardFee().equals(other.getCostOpenCardFee()))
                && (this.getCostChargeRate() == null ? other.getCostChargeRate() == null : this.getCostChargeRate().equals(other.getCostChargeRate()))
                && (this.getCostCancelCardFee() == null ? other.getCostCancelCardFee() == null : this.getCostCancelCardFee().equals(other.getCostCancelCardFee()))
                && (this.getPlatformOpenCardFee() == null ? other.getPlatformOpenCardFee() == null : this.getPlatformOpenCardFee().equals(other.getPlatformOpenCardFee()))
                && (this.getPlatformChargeRate() == null ? other.getPlatformChargeRate() == null : this.getPlatformChargeRate().equals(other.getPlatformChargeRate()))
                && (this.getPlatformCancelCardFee() == null ? other.getPlatformCancelCardFee() == null : this.getPlatformCancelCardFee().equals(other.getPlatformCancelCardFee()))
                && (this.getBusinessOpenCardFee() == null ? other.getBusinessOpenCardFee() == null : this.getBusinessOpenCardFee().equals(other.getBusinessOpenCardFee()))
                && (this.getBusinessChargeRate() == null ? other.getBusinessChargeRate() == null : this.getBusinessChargeRate().equals(other.getBusinessChargeRate()))
                && (this.getBusinessCancelCardFee() == null ? other.getBusinessCancelCardFee() == null : this.getBusinessCancelCardFee().equals(other.getBusinessCancelCardFee()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getFirstRecharge() == null ? other.getFirstRecharge() == null : this.getFirstRecharge().equals(other.getFirstRecharge()))
                && (this.getCardMonthFee() == null ? other.getCardMonthFee() == null : this.getCardMonthFee().equals(other.getCardMonthFee()))
                && (this.getCardSingleLimit() == null ? other.getCardSingleLimit() == null : this.getCardSingleLimit().equals(other.getCardSingleLimit()))
                && (this.getCardMonthLimit() == null ? other.getCardMonthLimit() == null : this.getCardMonthLimit().equals(other.getCardMonthLimit()))
                && (this.getCardTotalLimit() == null ? other.getCardTotalLimit() == null : this.getCardTotalLimit().equals(other.getCardTotalLimit()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCardManageId() == null) ? 0 : getCardManageId().hashCode());
        result = prime * result + ((getCardBin() == null) ? 0 : getCardBin().hashCode());
        result = prime * result + ((getIsAble() == null) ? 0 : getIsAble().hashCode());
        result = prime * result + ((getCostOpenCardFee() == null) ? 0 : getCostOpenCardFee().hashCode());
        result = prime * result + ((getCostChargeRate() == null) ? 0 : getCostChargeRate().hashCode());
        result = prime * result + ((getCostCancelCardFee() == null) ? 0 : getCostCancelCardFee().hashCode());
        result = prime * result + ((getPlatformOpenCardFee() == null) ? 0 : getPlatformOpenCardFee().hashCode());
        result = prime * result + ((getPlatformChargeRate() == null) ? 0 : getPlatformChargeRate().hashCode());
        result = prime * result + ((getPlatformCancelCardFee() == null) ? 0 : getPlatformCancelCardFee().hashCode());
        result = prime * result + ((getBusinessOpenCardFee() == null) ? 0 : getBusinessOpenCardFee().hashCode());
        result = prime * result + ((getBusinessChargeRate() == null) ? 0 : getBusinessChargeRate().hashCode());
        result = prime * result + ((getBusinessCancelCardFee() == null) ? 0 : getBusinessCancelCardFee().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getFirstRecharge() == null) ? 0 : getFirstRecharge().hashCode());
        result = prime * result + ((getCardMonthFee() == null) ? 0 : getCardMonthFee().hashCode());
        result = prime * result + ((getCardSingleLimit() == null) ? 0 : getCardSingleLimit().hashCode());
        result = prime * result + ((getCardMonthLimit() == null) ? 0 : getCardMonthLimit().hashCode());
        result = prime * result + ((getCardTotalLimit() == null) ? 0 : getCardTotalLimit().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", cardManageId=").append(cardManageId);
        sb.append(", cardBin=").append(cardBin);
        sb.append(", isAble=").append(isAble);
        sb.append(", costOpenCardFee=").append(costOpenCardFee);
        sb.append(", costChargeRate=").append(costChargeRate);
        sb.append(", costCancelCardFee=").append(costCancelCardFee);
        sb.append(", platformOpenCardFee=").append(platformOpenCardFee);
        sb.append(", platformChargeRate=").append(platformChargeRate);
        sb.append(", platformCancelCardFee=").append(platformCancelCardFee);
        sb.append(", businessOpenCardFee=").append(businessOpenCardFee);
        sb.append(", businessChargeRate=").append(businessChargeRate);
        sb.append(", businessCancelCardFee=").append(businessCancelCardFee);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", firstRecharge=").append(firstRecharge);
        sb.append(", cardMonthFee=").append(cardMonthFee);
        sb.append(", cardSingleLimit=").append(cardSingleLimit);
        sb.append(", cardMonthLimit=").append(cardMonthLimit);
        sb.append(", cardTotalLimit=").append(cardTotalLimit);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}