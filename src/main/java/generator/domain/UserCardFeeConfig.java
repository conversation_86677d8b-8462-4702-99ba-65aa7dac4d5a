package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 卡片费率配置表 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_card_fee_config")
public class UserCardFeeConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 卡片配置id(tb_user_card_manage表的id)
     */
    @Column(value = "card_manage_id")
    private Integer cardManageId;

    /**
     * 卡段
     */
    @Column(value = "card_bin")
    private String cardBin;

    /**
     * 卡段是否可用 1：可用 2：不可用
     */
    @Column(value = "is_able")
    private Integer isAble;

    /**
     * 成本开卡费
     */
    @Column(value = "cost_open_card_fee")
    private BigDecimal costOpenCardFee;

    /**
     * 成本充值手续费%
     */
    @Column(value = "cost_charge_rate")
    private BigDecimal costChargeRate;

    /**
     * 成本销卡费
     */
    @Column(value = "cost_cancel_card_fee")
    private BigDecimal costCancelCardFee;

    /**
     * 平台开卡费
     */
    @Column(value = "platform_open_card_fee")
    private BigDecimal platformOpenCardFee;

    /**
     * 平台接口手续费百分比（%）
     */
    @Column(value = "platform_charge_rate")
    private BigDecimal platformChargeRate;

    /**
     * 平台销卡费
     */
    @Column(value = "platform_cancel_card_fee")
    private BigDecimal platformCancelCardFee;

    /**
     * 商户开卡费
     */
    @Column(value = "business_open_card_fee")
    private BigDecimal businessOpenCardFee;

    /**
     * 商户手续费百分比（%）
     */
    @Column(value = "business_charge_rate")
    private BigDecimal businessChargeRate;

    /**
     * 商户销卡费
     */
    @Column(value = "business_cancel_card_fee")
    private BigDecimal businessCancelCardFee;

    /**
     * 卡段创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "create_by")
    private String createBy;

    @Column(value = "update_by")
    private String updateBy;

    /**
     * 可开卡数
     */
    @Column(value = "number_of_cards_that_can_be_opened")
    private Integer numberOfCardsThatCanBeOpened;

    /**
     * 首充金额
     */
    @Column(value = "first_recharge")
    private BigDecimal firstRecharge;


}
