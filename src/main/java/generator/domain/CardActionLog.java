package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 卡片操作日志 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_card_action_log")
public class CardActionLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 1 开卡 2 充值 3 销卡
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 记录金额
     */
    @Column(value = "money")
    private BigDecimal money;

    /**
     * 卡片Id
     */
    @Column(value = "card_id")
    private String cardId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 成本费
     */
    @Column(value = "cost_fee")
    private BigDecimal costFee;

    /**
     * 成本费率
     */
    @Column(value = "cost_rate")
    private BigDecimal costRate;

    /**
     * 平台费用
     */
    @Column(value = "platform_fee")
    private BigDecimal platformFee;

    /**
     * 平台费率
     */
    @Column(value = "platform_rate")
    private BigDecimal platformRate;

    /**
     * 商户费用
     */
    @Column(value = "business_fee")
    private BigDecimal businessFee;

    /**
     * 商户费率
     */
    @Column(value = "business_rate")
    private BigDecimal businessRate;

    /**
     * 平台收益
     */
    @Column(value = "platform_earn")
    private BigDecimal platformEarn;

    /**
     * 交易id
     */
    @Column(value = "order_id")
    private String orderId;

    /**
     * 商户收益
     */
    @Column(value = "business_earn")
    private BigDecimal businessEarn;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 创建日期
     */
    @Column(value = "create_date")
    private Date createDate;

    /**
     * 状态
     */
    @Column(value = "status")
    private String status;

    /**
     * 备注
     */
    @Column(value = "note")
    private String note;

    /**
     * 卡模式
     */
    @Column(value = "card_model")
    private String cardModel;


}
