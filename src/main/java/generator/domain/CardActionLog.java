package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qf.zpay.constants.CardModelEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡片操作日志
 *
 * @TableName tb_card_action_log
 */
@TableName(value = "tb_card_action_log")
@Data
public class CardActionLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 1 开卡 2 充值 3 销卡 4 交易
     */
    private Integer type;

    /**
     * 记录金额
     */
    private BigDecimal money;

    /**
     * 卡片Id
     */
    private String cardId;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 成本费
     */
    private BigDecimal costFee;

    /**
     * 成本费率
     */
    private BigDecimal costRate;

    /**
     * 平台费用
     */
    private BigDecimal platformFee;

    /**
     * 平台费率
     */
    private BigDecimal platformRate;

    /**
     * 商户费用
     */
    private BigDecimal businessFee;

    /**
     * 商户费率
     */
    private BigDecimal businessRate;

    /**
     * 平台收益
     */
    private BigDecimal platformEarn;

    /**
     * 商户收益
     */
    private BigDecimal businessEarn;
    /**
     * 交易Id
     */
    private String orderId;
    /**
     * 状态 success 成功 failed 失败 doing 进行中
     */
    private String status;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建日期
     */
    private Date createDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardActionLog other = (CardActionLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
                && (this.getMoney() == null ? other.getMoney() == null : this.getMoney().equals(other.getMoney()))
                && (this.getCardId() == null ? other.getCardId() == null : this.getCardId().equals(other.getCardId()))
                && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
                && (this.getCostFee() == null ? other.getCostFee() == null : this.getCostFee().equals(other.getCostFee()))
                && (this.getCostRate() == null ? other.getCostRate() == null : this.getCostRate().equals(other.getCostRate()))
                && (this.getPlatformFee() == null ? other.getPlatformFee() == null : this.getPlatformFee().equals(other.getPlatformFee()))
                && (this.getPlatformRate() == null ? other.getPlatformRate() == null : this.getPlatformRate().equals(other.getPlatformRate()))
                && (this.getBusinessFee() == null ? other.getBusinessFee() == null : this.getBusinessFee().equals(other.getBusinessFee()))
                && (this.getBusinessRate() == null ? other.getBusinessRate() == null : this.getBusinessRate().equals(other.getBusinessRate()))
                && (this.getPlatformEarn() == null ? other.getPlatformEarn() == null : this.getPlatformEarn().equals(other.getPlatformEarn()))
                && (this.getBusinessEarn() == null ? other.getBusinessEarn() == null : this.getBusinessEarn().equals(other.getBusinessEarn()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getMoney() == null) ? 0 : getMoney().hashCode());
        result = prime * result + ((getCardId() == null) ? 0 : getCardId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCostFee() == null) ? 0 : getCostFee().hashCode());
        result = prime * result + ((getCostRate() == null) ? 0 : getCostRate().hashCode());
        result = prime * result + ((getPlatformFee() == null) ? 0 : getPlatformFee().hashCode());
        result = prime * result + ((getPlatformRate() == null) ? 0 : getPlatformRate().hashCode());
        result = prime * result + ((getBusinessFee() == null) ? 0 : getBusinessFee().hashCode());
        result = prime * result + ((getBusinessRate() == null) ? 0 : getBusinessRate().hashCode());
        result = prime * result + ((getPlatformEarn() == null) ? 0 : getPlatformEarn().hashCode());
        result = prime * result + ((getBusinessEarn() == null) ? 0 : getBusinessEarn().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", type=").append(type);
        sb.append(", money=").append(money);
        sb.append(", cardId=").append(cardId);
        sb.append(", businessId=").append(businessId);
        sb.append(", costFee=").append(costFee);
        sb.append(", costRate=").append(costRate);
        sb.append(", platformFee=").append(platformFee);
        sb.append(", platformRate=").append(platformRate);
        sb.append(", businessFee=").append(businessFee);
        sb.append(", businessRate=").append(businessRate);
        sb.append(", platformEarn=").append(platformEarn);
        sb.append(", businessEarn=").append(businessEarn);
        sb.append(", createTime=").append(createTime);
        sb.append(", createDate=").append(createDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}