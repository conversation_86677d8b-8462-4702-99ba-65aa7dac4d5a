package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支出列表
 */
@TableName(value = "tb_business_day_card_disburse_log")
@Data
public class BusinessDayCardDisburseLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 钱包类型
     */
    private String cardModel;
    /**
     * 商户今日开卡费支出
     */
    private BigDecimal dayOpenCardFeeExpenditure;
    /**
     * 卡注销支出
     */
    private BigDecimal cardCancellationExpenditure;
    /**
     * 商户今日手续费支出
     */
    private BigDecimal dayRechargeChargeCardExpenditure;

    /**
     * 充值支出
     */
    private BigDecimal dayRechargeCardExpenditure;
    /**
     * 商户余额
     */
    private BigDecimal historyBalance;
    /**
     * 今日总支出
     */
    private BigDecimal dayChargeTotalExpenditure;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date statDate;


}
