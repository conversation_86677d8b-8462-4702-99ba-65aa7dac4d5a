package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_platform_day_disburse_log")
public class PlatformDayDisburseLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 总开卡费支出---根据历史商户/用户开卡状况，计算需支付给上游多少开卡费用。
     举例上游渠道开卡费1U，历史开卡300张，历史开卡费支出为300
     */
    @Column(value = "open_card_fee")
    private BigDecimal openCardFee;

    /**
     * 充值手续费支出---根据历史商户/用户充值状况，计算需支付给上游多少开卡费用。
     举例上游渠道设置充值手续费0.5%，历史充值1万，历史手续费支出为50
     */
    @Column(value = "recharge_fee")
    private BigDecimal rechargeFee;

    /**
     * 销卡费支出
     */
    @Column(value = "cancel_card_fee")
    private BigDecimal cancelCardFee;

    /**
     * 今日支出---根据历史商户/用户开卡与充值状况，计算历史支付给上游多少费用。
     开卡费支出+充值手续费支出
     */
    @Column(value = "day_disburse")
    private BigDecimal dayDisburse;

    /**
     * 历史总支出---根据历史商户/用户开卡与充值状况，计算历史支付给上游多少费用。
     开卡费支出+充值手续费支出
     */
    @Column(value = "history_disburse")
    private BigDecimal historyDisburse;

    /**
     * 统计日期
     */
    @Column(value = "stat_date")
    private Date statDate;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;


}
