package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_platform_day_disburse_log
 */
@TableName(value ="tb_platform_day_disburse_log")
@Data
public class PlatformDayDisburseLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 总开卡费支出---根据历史商户/用户开卡状况，计算需支付给上游多少开卡费用。
举例上游渠道开卡费1U，历史开卡300张，历史开卡费支出为300
     */
    private BigDecimal openCardFee;

    /**
     * 充值手续费支出---根据历史商户/用户充值状况，计算需支付给上游多少开卡费用。
举例上游渠道设置充值手续费0.5%，历史充值1万，历史手续费支出为50
     */
    private BigDecimal rechargeFee;

    /**
     * 销卡费支出
     */
    private BigDecimal cancelCardFee;

    /**
     * 今日支出---根据历史商户/用户开卡与充值状况，计算历史支付给上游多少费用。
开卡费支出+充值手续费支出
     */
    private BigDecimal dayDisburse;

    /**
     * 历史总支出---根据历史商户/用户开卡与充值状况，计算历史支付给上游多少费用。
开卡费支出+充值手续费支出
     */
    private BigDecimal historyDisburse;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PlatformDayDisburseLog other = (PlatformDayDisburseLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOpenCardFee() == null ? other.getOpenCardFee() == null : this.getOpenCardFee().equals(other.getOpenCardFee()))
            && (this.getRechargeFee() == null ? other.getRechargeFee() == null : this.getRechargeFee().equals(other.getRechargeFee()))
            && (this.getCancelCardFee() == null ? other.getCancelCardFee() == null : this.getCancelCardFee().equals(other.getCancelCardFee()))
            && (this.getDayDisburse() == null ? other.getDayDisburse() == null : this.getDayDisburse().equals(other.getDayDisburse()))
            && (this.getHistoryDisburse() == null ? other.getHistoryDisburse() == null : this.getHistoryDisburse().equals(other.getHistoryDisburse()))
            && (this.getStatDate() == null ? other.getStatDate() == null : this.getStatDate().equals(other.getStatDate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOpenCardFee() == null) ? 0 : getOpenCardFee().hashCode());
        result = prime * result + ((getRechargeFee() == null) ? 0 : getRechargeFee().hashCode());
        result = prime * result + ((getCancelCardFee() == null) ? 0 : getCancelCardFee().hashCode());
        result = prime * result + ((getDayDisburse() == null) ? 0 : getDayDisburse().hashCode());
        result = prime * result + ((getHistoryDisburse() == null) ? 0 : getHistoryDisburse().hashCode());
        result = prime * result + ((getStatDate() == null) ? 0 : getStatDate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", openCardFee=").append(openCardFee);
        sb.append(", rechargeFee=").append(rechargeFee);
        sb.append(", cancelCardFee=").append(cancelCardFee);
        sb.append(", dayDisburse=").append(dayDisburse);
        sb.append(", historyDisburse=").append(historyDisburse);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}