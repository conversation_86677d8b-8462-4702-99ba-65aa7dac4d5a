package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_app_config")
public class AppConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "key")
    private String key;

    @Column(value = "value")
    private String value;

    @Column(value = "remark")
    private String remark;

    @Column(value = "create_at")
    private Date createAt;

    @Column(value = "update_time")
    private Date updateTime;


}
