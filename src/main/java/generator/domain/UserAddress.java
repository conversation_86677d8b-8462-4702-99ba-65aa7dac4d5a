package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_address")
public class UserAddress {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "user_id")
    private Integer userId;

    @Column(value = "erc_address")
    private String ercAddress;

    @Column(value = "erc_prikey")
    private String ercPrikey;

    @Column(value = "erc_word")
    private String ercWord;

    @Column(value = "bep_address")
    private String bepAddress;

    @Column(value = "bep_prikey")
    private String bepPrikey;

    @Column(value = "bep_word")
    private String bepWord;

    @Column(value = "trc_address")
    private String trcAddress;

    @Column(value = "trc_prikey")
    private String trcPrikey;

    @Column(value = "trc_word")
    private String trcWord;

    @Column(value = "trc_hexAddress")
    private String trcHexAddress;

    @Column(value = "create_at")
    private Date createAt;


}
