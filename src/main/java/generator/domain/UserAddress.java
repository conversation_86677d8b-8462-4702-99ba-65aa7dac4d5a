package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_user_address
 */
@TableName(value ="tb_user_address")
@Data
public class UserAddress implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Integer userId;

    /**
     * 
     */
    private String ercAddress;

    /**
     * 
     */
    private String ercPrikey;

    /**
     * 
     */
    private String ercWord;

    /**
     * 
     */
    private String bepAddress;

    /**
     * 
     */
    private String bepPrikey;

    /**
     * 
     */
    private String bepWord;

    /**
     * 
     */
    private String trcAddress;

    /**
     * 
     */
    private String trcPrikey;

    /**
     * 
     */
    private String trcWord;

    /**
     * 
     */
    private String trcHexaddress;

    /**
     * 
     */
    private Date createAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserAddress other = (UserAddress) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getErcAddress() == null ? other.getErcAddress() == null : this.getErcAddress().equals(other.getErcAddress()))
            && (this.getErcPrikey() == null ? other.getErcPrikey() == null : this.getErcPrikey().equals(other.getErcPrikey()))
            && (this.getErcWord() == null ? other.getErcWord() == null : this.getErcWord().equals(other.getErcWord()))
            && (this.getBepAddress() == null ? other.getBepAddress() == null : this.getBepAddress().equals(other.getBepAddress()))
            && (this.getBepPrikey() == null ? other.getBepPrikey() == null : this.getBepPrikey().equals(other.getBepPrikey()))
            && (this.getBepWord() == null ? other.getBepWord() == null : this.getBepWord().equals(other.getBepWord()))
            && (this.getTrcAddress() == null ? other.getTrcAddress() == null : this.getTrcAddress().equals(other.getTrcAddress()))
            && (this.getTrcPrikey() == null ? other.getTrcPrikey() == null : this.getTrcPrikey().equals(other.getTrcPrikey()))
            && (this.getTrcWord() == null ? other.getTrcWord() == null : this.getTrcWord().equals(other.getTrcWord()))
            && (this.getTrcHexaddress() == null ? other.getTrcHexaddress() == null : this.getTrcHexaddress().equals(other.getTrcHexaddress()))
            && (this.getCreateAt() == null ? other.getCreateAt() == null : this.getCreateAt().equals(other.getCreateAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getErcAddress() == null) ? 0 : getErcAddress().hashCode());
        result = prime * result + ((getErcPrikey() == null) ? 0 : getErcPrikey().hashCode());
        result = prime * result + ((getErcWord() == null) ? 0 : getErcWord().hashCode());
        result = prime * result + ((getBepAddress() == null) ? 0 : getBepAddress().hashCode());
        result = prime * result + ((getBepPrikey() == null) ? 0 : getBepPrikey().hashCode());
        result = prime * result + ((getBepWord() == null) ? 0 : getBepWord().hashCode());
        result = prime * result + ((getTrcAddress() == null) ? 0 : getTrcAddress().hashCode());
        result = prime * result + ((getTrcPrikey() == null) ? 0 : getTrcPrikey().hashCode());
        result = prime * result + ((getTrcWord() == null) ? 0 : getTrcWord().hashCode());
        result = prime * result + ((getTrcHexaddress() == null) ? 0 : getTrcHexaddress().hashCode());
        result = prime * result + ((getCreateAt() == null) ? 0 : getCreateAt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", ercAddress=").append(ercAddress);
        sb.append(", ercPrikey=").append(ercPrikey);
        sb.append(", ercWord=").append(ercWord);
        sb.append(", bepAddress=").append(bepAddress);
        sb.append(", bepPrikey=").append(bepPrikey);
        sb.append(", bepWord=").append(bepWord);
        sb.append(", trcAddress=").append(trcAddress);
        sb.append(", trcPrikey=").append(trcPrikey);
        sb.append(", trcWord=").append(trcWord);
        sb.append(", trcHexaddress=").append(trcHexaddress);
        sb.append(", createAt=").append(createAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}