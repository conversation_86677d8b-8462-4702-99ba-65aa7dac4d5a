package generator.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user")
public class User {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "email")
    private String email;

    @Column(value = "mobile")
    private String mobile;

    /**
     * 盐值
     */
    @Column(value = "salt")
    private String salt;

    @Column(value = "password")
    private String password;

    @Column(value = "pay_password")
    private String payPassword;

    /**
     * 总钱包
     */
    @Column(value = "money")
    private BigDecimal money;

    @Column(value = "share_balance")
    private BigDecimal shareBalance;

    /**
     * 1正常 2冻结
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 商户id
     */
    @Id(keyType = KeyType.Auto)
    private Integer businessId;

    /**
     * 商户名称
     */
    @Column(value = "business_name")
    private String businessName;

    /**
     * 客服编号
     */
    @Column(value = "business_no")
    private String businessNo;

    /**
     * 1开启   2关闭
     */
    @Column(value = "business_status")
    private Integer businessStatus;

    /**
     * 商户提现手续费
     */
    @Column(value = "withdraw_rate")
    private BigDecimal withdrawRate;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 最后登录时间
     */
    @Column(value = "last_login_time")
    private Date lastLoginTime;

    /**
     * 最后登录 IP
     */
    @Column(value = "last_login_ip")
    private String lastLoginIp;


}
