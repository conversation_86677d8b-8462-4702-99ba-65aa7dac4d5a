package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value ="tb_business_day_fund_log")
@Data
public class BusinessDayFundLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 商户id
     */
    private Integer businessId;

    private String walletType;
    /**
     * 商户今日开卡费支出(USD）商户需支付给平台的开卡费，也就是平台设置给商户的开卡费。
     * 举例：平台设置给商户的开卡费为15U，今天商户激活了20张卡，则商户开卡费支出为300
     */
    private BigDecimal dayOpenCardFeeExpenditure;
    /**
     * 商户今日充值支出(USD）当天商户的用户充值进到卡片的实际金额，表示为商户需要从余额扣除多少资金，充值到卡片
     * 例：用户充值100，平台设置给商户2%，商户设置用户5%手续费，实际到账95，则商户需要从它的余额支出95商户充值支出为95
     * 这个例子接续使用
     */
    private BigDecimal dayRechargeCardExpenditure;
    /**
     * 商户今日手续费支出---商户需支付给平台的手续费，延续前例例：用户充值100，平台设置给商户2%，商户设置用户5%手续费
     * 则商户手续费支出为2
     */
    private BigDecimal dayRechargeChargeCardExpenditure;
    /**
     * 商户余额----昨日余额+今日充值-今日充值支出-今日商户手续费支出-商户开卡费支出=今日商户余额（今日数据实时更新）
     */
    private BigDecimal historyBalance;
    /**
     * 今日总支出--总支出---总支出为商户支出总额，即商户支付给平台方的开卡费以及手续费的总和
     */
    private BigDecimal dayChargeTotalExpenditure;
    /**
     * 总支出--总支出为商户支出总额，即商户支付给平台方的开卡费以及手续费的总和。
     */
    private BigDecimal totalChargeFeeExpenditure;
    /**
     * 总开卡费支出
     * 开卡费支出，即为商户单开卡的成本总和；
     * 假定平台对商户开卡费收取10美金，本条数据即为10u。
     */
    private BigDecimal totalOpenCardCharge;
    /**
     * 总手续费支出
     * 商户向平台支付的所有开卡手续费的总和；
     * 总手续费由平台方进行设定，向商户收取，且在商户端下面的用户进行充值的时候进行收取；
     * 本条数据仅统计商户需要支付的手续费成本。
     */
    private BigDecimal totalRechargeCharge;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",locale = "zh", timezone = "GMT+8")
    private Date createDate;
    /**
     * 卡注销支出
     */
    private BigDecimal cardCancellationExpenditure;


}
