package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 支付收款人信息实体类
 * <p>
 * 该类对应数据库表 `tb_payment_pay_payee`，用于存储收款人的相关信息。
 */
@Data
@TableName(value = "tb_payment_pay_payee")
public class PaymentPayee implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 配置id
     */
    private Integer payConfigId;

    private String payeeId;

    /**
     * 收款人类型（INDIVIDUAL 个人 / ENTERPRISE 企业）
     */
    private String subjectType;

    /**
     * 国家/地区编码
     */
    private String country;

    /**
     * 收款币种
     */
    private String currency;

    /**
     * 收款人名（个人类型必填）
     */
    private String firstName;

    /**
     * 收款人姓（个人类型必填）
     */
    private String lastName;

    /**
     * 公司名称（企业类型必填）
     */
    private String accountHolder;

    /**
     * 区号
     */
    private String nationCode;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 收款账户类型  电子钱包：E_WALLET  银行账户：BANK_ACCOUNT
     */
    private String accountType;

    /**
     * 地址
     */
    private String address;

    /**
     * 付款用途
     */
    private String paymentPurpose;

    private String twoLetterCode;

    private String englishNameCountry;

    private Integer status;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;


    /**
     * 城市名称
     */
    private String city;


    /**
     * 州编码，仅美国必填。可参考美国城市列表
     */
    private String state;

    /**
     * 邮编
     */
    private String postcode;
    /**
     * 收款账号
     */
    private String accountNo;
}