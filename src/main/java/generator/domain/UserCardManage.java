package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import lombok.Data;

/**
 * @TableName tb_user_card_manage
 */
@TableName(value = "tb_user_card_manage")
@Data
public class UserCardManage implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 卡片来源地
     */
    private String cardSource;

    /**
     * 卡片类别
     */
    private Integer cardCategory;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡归属
     */
    private CardSchemeEnum cardBelongTo;

    /**
     * 开卡费（成本）
     */
    private BigDecimal cardOpenFee;

    /**
     * 划转手续费%（成本）
     */
    private BigDecimal cardChargeRate;

    /**
     * 销卡费（成本）
     */
    private BigDecimal cardCancelFee;

    /**
     * 卡类
     */
    private String cardLei;

    /**
     * 应用场景
     */
    private String cardChangjing;

    /**
     * 发卡国家
     */
    private String cardSourceCountry;

    /**
     * 卡段编号
     */
    private String cardDuan;

    /**
     * 卡月费
     */
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    private BigDecimal cardChuzhiMax;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private Date updateTime;

    /**
     * 卡背图
     */
    private String cardImg;

    /**
     *
     */
    private CardModelEnum cardModel;

    /**
     *
     */
    private Integer cardStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserCardManage other = (UserCardManage) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getCardSource() == null ? other.getCardSource() == null : this.getCardSource().equals(other.getCardSource()))
                && (this.getCardCategory() == null ? other.getCardCategory() == null : this.getCardCategory().equals(other.getCardCategory()))
                && (this.getCardName() == null ? other.getCardName() == null : this.getCardName().equals(other.getCardName()))
                && (this.getCardBelongTo() == null ? other.getCardBelongTo() == null : this.getCardBelongTo().equals(other.getCardBelongTo()))
                && (this.getCardOpenFee() == null ? other.getCardOpenFee() == null : this.getCardOpenFee().equals(other.getCardOpenFee()))
                && (this.getCardChargeRate() == null ? other.getCardChargeRate() == null : this.getCardChargeRate().equals(other.getCardChargeRate()))
                && (this.getCardCancelFee() == null ? other.getCardCancelFee() == null : this.getCardCancelFee().equals(other.getCardCancelFee()))
                && (this.getCardLei() == null ? other.getCardLei() == null : this.getCardLei().equals(other.getCardLei()))
                && (this.getCardChangjing() == null ? other.getCardChangjing() == null : this.getCardChangjing().equals(other.getCardChangjing()))
                && (this.getCardSourceCountry() == null ? other.getCardSourceCountry() == null : this.getCardSourceCountry().equals(other.getCardSourceCountry()))
                && (this.getCardDuan() == null ? other.getCardDuan() == null : this.getCardDuan().equals(other.getCardDuan()))
                && (this.getCardMonthFee() == null ? other.getCardMonthFee() == null : this.getCardMonthFee().equals(other.getCardMonthFee()))
                && (this.getCardDanciXiaofeiMax() == null ? other.getCardDanciXiaofeiMax() == null : this.getCardDanciXiaofeiMax().equals(other.getCardDanciXiaofeiMax()))
                && (this.getCardMonthXiaofeiMax() == null ? other.getCardMonthXiaofeiMax() == null : this.getCardMonthXiaofeiMax().equals(other.getCardMonthXiaofeiMax()))
                && (this.getCardChuzhiMax() == null ? other.getCardChuzhiMax() == null : this.getCardChuzhiMax().equals(other.getCardChuzhiMax()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getCardImg() == null ? other.getCardImg() == null : this.getCardImg().equals(other.getCardImg()))
                && (this.getCardModel() == null ? other.getCardModel() == null : this.getCardModel().equals(other.getCardModel()))
                && (this.getCardStatus() == null ? other.getCardStatus() == null : this.getCardStatus().equals(other.getCardStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCardSource() == null) ? 0 : getCardSource().hashCode());
        result = prime * result + ((getCardCategory() == null) ? 0 : getCardCategory().hashCode());
        result = prime * result + ((getCardName() == null) ? 0 : getCardName().hashCode());
        result = prime * result + ((getCardBelongTo() == null) ? 0 : getCardBelongTo().hashCode());
        result = prime * result + ((getCardOpenFee() == null) ? 0 : getCardOpenFee().hashCode());
        result = prime * result + ((getCardChargeRate() == null) ? 0 : getCardChargeRate().hashCode());
        result = prime * result + ((getCardCancelFee() == null) ? 0 : getCardCancelFee().hashCode());
        result = prime * result + ((getCardLei() == null) ? 0 : getCardLei().hashCode());
        result = prime * result + ((getCardChangjing() == null) ? 0 : getCardChangjing().hashCode());
        result = prime * result + ((getCardSourceCountry() == null) ? 0 : getCardSourceCountry().hashCode());
        result = prime * result + ((getCardDuan() == null) ? 0 : getCardDuan().hashCode());
        result = prime * result + ((getCardMonthFee() == null) ? 0 : getCardMonthFee().hashCode());
        result = prime * result + ((getCardDanciXiaofeiMax() == null) ? 0 : getCardDanciXiaofeiMax().hashCode());
        result = prime * result + ((getCardMonthXiaofeiMax() == null) ? 0 : getCardMonthXiaofeiMax().hashCode());
        result = prime * result + ((getCardChuzhiMax() == null) ? 0 : getCardChuzhiMax().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCardImg() == null) ? 0 : getCardImg().hashCode());
        result = prime * result + ((getCardModel() == null) ? 0 : getCardModel().hashCode());
        result = prime * result + ((getCardStatus() == null) ? 0 : getCardStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", cardSource=").append(cardSource);
        sb.append(", cardCategory=").append(cardCategory);
        sb.append(", cardName=").append(cardName);
        sb.append(", cardBelongTo=").append(cardBelongTo);
        sb.append(", cardOpenFee=").append(cardOpenFee);
        sb.append(", cardChargeRate=").append(cardChargeRate);
        sb.append(", cardCancelFee=").append(cardCancelFee);
        sb.append(", cardLei=").append(cardLei);
        sb.append(", cardChangjing=").append(cardChangjing);
        sb.append(", cardSourceCountry=").append(cardSourceCountry);
        sb.append(", cardDuan=").append(cardDuan);
        sb.append(", cardMonthFee=").append(cardMonthFee);
        sb.append(", cardDanciXiaofeiMax=").append(cardDanciXiaofeiMax);
        sb.append(", cardMonthXiaofeiMax=").append(cardMonthXiaofeiMax);
        sb.append(", cardChuzhiMax=").append(cardChuzhiMax);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", cardImg=").append(cardImg);
        sb.append(", cardModel=").append(cardModel);
        sb.append(", cardStatus=").append(cardStatus);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}