package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_card_manage")
public class UserCardManage {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 卡片来源地
     */
    @Column(value = "card_source")
    private String cardSource;

    /**
     * 卡片类别
     */
    @Column(value = "card_category")
    private Integer cardCategory;

    /**
     * 卡片名称
     */
    @Column(value = "card_name")
    private String cardName;

    /**
     * 卡归属
     */
    @Column(value = "card_belong_to")
    private String cardBelongTo;

    /**
     * 开卡费（成本）
     */
    @Column(value = "card_open_fee")
    private BigDecimal cardOpenFee;

    /**
     * 划转手续费%（成本）
     */
    @Column(value = "card_charge_rate")
    private BigDecimal cardChargeRate;

    /**
     * 销卡费（成本）
     */
    @Column(value = "card_cancel_fee")
    private BigDecimal cardCancelFee;

    /**
     * 卡类
     */
    @Column(value = "card_lei")
    private String cardLei;

    /**
     * 应用场景
     */
    @Column(value = "card_changjing")
    private String cardChangjing;

    /**
     * 发卡国家
     */
    @Column(value = "card_source_country")
    private String cardSourceCountry;

    /**
     * 卡段编号
     */
    @Column(value = "card_duan")
    private String cardDuan;

    /**
     * 卡月费
     */
    @Column(value = "card_month_fee")
    private BigDecimal cardMonthFee;

    /**
     * 卡单次消费上限（USD）
     */
    @Column(value = "card_danci_xiaofei_max")
    private BigDecimal cardDanciXiaofeiMax;

    /**
     * 卡月消费上限（USD）
     */
    @Column(value = "card_month_xiaofei_max")
    private BigDecimal cardMonthXiaofeiMax;

    /**
     * 卡储值上限（USD）
     */
    @Column(value = "card_chuzhi_max")
    private BigDecimal cardChuzhiMax;

    @Column(value = "create_by")
    private String createBy;

    @Column(value = "create_time")
    private Timestamp createTime;

    @Column(value = "update_by")
    private String updateBy;

    @Column(value = "update_time")
    private Timestamp updateTime;

    /**
     * 卡背图
     */
    @Column(value = "card_img")
    private String cardImg;

    @Column(value = "card_model")
    private String cardModel;

    @Column(value = "card_status")
    private Integer cardStatus;


}
