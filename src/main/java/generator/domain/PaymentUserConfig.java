package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 汇率表
 * <p>
 * 该类对应数据库表 `tb_payment_user_config`，用于存储支付产品的相关配置信息。
 */
@Data
@TableName("tb_payment_user_config")
public class PaymentUserConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 0 禁用 ，1启用
     */
    private Integer status;

    /**
     * 渠道 ID
     */
    private Integer channelId;

    /**
     * 商户 ID
     */
    private Integer businessId;

    /**
     * '单笔手续费'（平台）
     */
    private BigDecimal platformFee;

    /**
     * '手续费率'（平台）
     */
    private BigDecimal platformFeeRate;

    /**
     * '商户手续费'
     */
    private BigDecimal businessFee;

    /**
     * '商户费率'
     */
    private BigDecimal businessFeeRate;


    /**
     * 渠道code
     */
    @TableField(exist = false)
    private String code;
    /**
     * 国家
     */
    @TableField(exist = false)
    private String country;
    /**
     * 货币
     */
    @TableField(exist = false)
    private String currency;

}
