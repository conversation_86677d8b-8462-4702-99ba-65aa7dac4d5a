package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_balance_log")
public class UserBalanceLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "order_id")
    private String orderId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    @Column(value = "before_money")
    private BigDecimal beforeMoney;

    @Column(value = "money")
    private BigDecimal money;

    @Column(value = "after_money")
    private BigDecimal afterMoney;

    /**
     * 1：增加 2：减少 0:金额不变动
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 1 划转进 2 划转出 3 消费扣减
     */
    @Column(value = "sub_type")
    private Integer subType;

    @Column(value = "note")
    private String note;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 创建日期
     */
    @Column(value = "create_date")
    private Date createDate;


}
