package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_message")
public class Message {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "title")
    private String title;

    @Column(value = "content")
    private String content;

    /**
     * 0 正常   1是禁用
     */
    @Column(value = "status")
    private Integer status;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 1：中文 2：英文
     */
    @Column(value = "lang")
    private Integer lang;

    /**
     * 排序
     */
    @Column(value = "sort")
    private Integer sort;


}
