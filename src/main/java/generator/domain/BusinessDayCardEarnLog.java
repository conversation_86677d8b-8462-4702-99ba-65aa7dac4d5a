package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收益列表
 */
@TableName(value = "tb_business_day_card_earn_log")
@Data
public class BusinessDayCardEarnLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 商户id
     */
    private Integer businessId;
    /**
     * 今日开卡费
     * 举例商户设置用户开卡费5U，今日开了100张卡。
     * 今日开卡费为500U
     */
    private BigDecimal dayOpenCardFee;
    /**
     * 今日开卡费收益
     * 举例商户设置用户开卡费5U，今日开了100张卡。
     * 今日开卡费为500U
     * 平台设置商户的开卡费为2U，开卡费支出为200U
     * 今日开卡费收益为300U
     */
    private BigDecimal dayOpenCardEarnings;
    /**
     * 今日手续费
     * /*举例商户设置用户充值手续费8%，今日充值1万
     * 今日手续费费为800U
     */
    private BigDecimal dayRechargeChargeFee;
    /**
     * 今日手续费收益
     * /*举例商户设置用户充值手续费8%，今日充值1万
     * 今日手续费费为800U
     * 平台设置给商户的充值手续费为2%
     * 今日手续费支出为200U
     * 今日手续费收益为600U
     */
    private BigDecimal dayRechargeChargeCardEarnings;
    /**
     * 今日收益 = 今日开卡费收益+今日手续费收益+今日销卡收益
     */
    private BigDecimal dayEarnings;
    /**
     * 历史总收益 每日收益叠加（只会增加不会减少）前一日总收益 + 今日总收益
     */
    private BigDecimal historyEarnings;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date statDate;
    /**
     * 销卡余额返回
     */
    private BigDecimal cardCancellationBalanceReturn;
    /**
     * 销卡费
     */
    private BigDecimal cancelCardFee;
    /**
     * 销卡收益
     */
    private BigDecimal cancelCardEarnings;
    /**
     * 卡模式
     */
    private String cardModel;
}
