package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_wallet")
public class UserWallet {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "user_id")
    private Integer userId;

    /**
     * 商户 ID
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 主账户钱包
     */
    @Column(value = "main_wallet")
    private BigDecimal mainWallet;

    /**
     * 储值卡钱包
     */
    @Column(value = "store_wallet")
    private BigDecimal storeWallet;

    /**
     * 共享卡钱包
     */
    @Column(value = "share_wallet")
    private BigDecimal shareWallet;

    /**
     * 实体卡钱包
     */
    @Column(value = "physical_wallet")
    private BigDecimal physicalWallet;

    /**
     * 代付钱包
     */
    @Column(value = "payment_wallet")
    private BigDecimal paymentWallet;

    /**
     * USDT 钱包
     */
    @Column(value = "usdt_wallet")
    private BigDecimal usdtWallet;

    /**
     * 代币钱包
     */
    @Column(value = "token_wallet")
    private BigDecimal tokenWallet;


}
