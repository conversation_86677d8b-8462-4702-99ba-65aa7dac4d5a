package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName tb_user_wallet
 */
@TableName(value ="tb_user_wallet")
@Data
public class UserWallet implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Integer userId;

    /**
     * 商户 ID
     */
    private Integer businessId;

    /**
     * 主账户钱包
     */
    private BigDecimal mainWallet;

    /**
     * 储值卡钱包
     */
    private BigDecimal storeWallet;

    /**
     * 共享卡钱包
     */
    private BigDecimal shareWallet;

    /**
     * 实体卡钱包
     */
    private BigDecimal physicalWallet;

    /**
     * 代付钱包
     */
    private BigDecimal paymentWallet;

    /**
     * USDT 钱包
     */
    private BigDecimal usdtWallet;

    /**
     * 代币钱包
     */
    private BigDecimal tokenWallet;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserWallet other = (UserWallet) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getMainWallet() == null ? other.getMainWallet() == null : this.getMainWallet().equals(other.getMainWallet()))
            && (this.getStoreWallet() == null ? other.getStoreWallet() == null : this.getStoreWallet().equals(other.getStoreWallet()))
            && (this.getShareWallet() == null ? other.getShareWallet() == null : this.getShareWallet().equals(other.getShareWallet()))
            && (this.getPhysicalWallet() == null ? other.getPhysicalWallet() == null : this.getPhysicalWallet().equals(other.getPhysicalWallet()))
            && (this.getPaymentWallet() == null ? other.getPaymentWallet() == null : this.getPaymentWallet().equals(other.getPaymentWallet()))
            && (this.getUsdtWallet() == null ? other.getUsdtWallet() == null : this.getUsdtWallet().equals(other.getUsdtWallet()))
            && (this.getTokenWallet() == null ? other.getTokenWallet() == null : this.getTokenWallet().equals(other.getTokenWallet()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getMainWallet() == null) ? 0 : getMainWallet().hashCode());
        result = prime * result + ((getStoreWallet() == null) ? 0 : getStoreWallet().hashCode());
        result = prime * result + ((getShareWallet() == null) ? 0 : getShareWallet().hashCode());
        result = prime * result + ((getPhysicalWallet() == null) ? 0 : getPhysicalWallet().hashCode());
        result = prime * result + ((getPaymentWallet() == null) ? 0 : getPaymentWallet().hashCode());
        result = prime * result + ((getUsdtWallet() == null) ? 0 : getUsdtWallet().hashCode());
        result = prime * result + ((getTokenWallet() == null) ? 0 : getTokenWallet().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", businessId=").append(businessId);
        sb.append(", mainWallet=").append(mainWallet);
        sb.append(", storeWallet=").append(storeWallet);
        sb.append(", shareWallet=").append(shareWallet);
        sb.append(", physicalWallet=").append(physicalWallet);
        sb.append(", paymentWallet=").append(paymentWallet);
        sb.append(", usdtWallet=").append(usdtWallet);
        sb.append(", tokenWallet=").append(tokenWallet);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}