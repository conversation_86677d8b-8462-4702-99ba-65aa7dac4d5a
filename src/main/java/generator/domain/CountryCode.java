package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_country_code")
public class CountryCode {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "country_en")
    private String countryEn;

    @Column(value = "country_zh_cn")
    private String countryZhCn;

    @Column(value = "country_zh_tw")
    private String countryZhTw;

    @Column(value = "phone_code")
    private String phoneCode;

    @Column(value = "short_code")
    private String shortCode;

    /**
     * 0：不支持的国家；1：支持的国家
     */
    @Column(value = "status")
    private Boolean status;


}
