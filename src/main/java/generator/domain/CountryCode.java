package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName tb_country_code
 */
@TableName(value = "tb_country_code")
@Data
public class CountryCode implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    private String countryEn;

    /**
     *
     */
    private String countryZhCn;

    /**
     *
     */
    private String countryZhTw;

    /**
     *
     */
    private String phoneCode;

    /**
     *
     */
    private String shortCode;

    /**
     *
     */
    private String twoWordsCode;

    /**
     * 0：不支持的国家；1：支持的国家
     */
    private Boolean status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CountryCode other = (CountryCode) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getCountryEn() == null ? other.getCountryEn() == null : this.getCountryEn().equals(other.getCountryEn()))
                && (this.getCountryZhCn() == null ? other.getCountryZhCn() == null : this.getCountryZhCn().equals(other.getCountryZhCn()))
                && (this.getCountryZhTw() == null ? other.getCountryZhTw() == null : this.getCountryZhTw().equals(other.getCountryZhTw()))
                && (this.getPhoneCode() == null ? other.getPhoneCode() == null : this.getPhoneCode().equals(other.getPhoneCode()))
                && (this.getShortCode() == null ? other.getShortCode() == null : this.getShortCode().equals(other.getShortCode()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCountryEn() == null) ? 0 : getCountryEn().hashCode());
        result = prime * result + ((getCountryZhCn() == null) ? 0 : getCountryZhCn().hashCode());
        result = prime * result + ((getCountryZhTw() == null) ? 0 : getCountryZhTw().hashCode());
        result = prime * result + ((getPhoneCode() == null) ? 0 : getPhoneCode().hashCode());
        result = prime * result + ((getShortCode() == null) ? 0 : getShortCode().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", countryEn=").append(countryEn);
        sb.append(", countryZhCn=").append(countryZhCn);
        sb.append(", countryZhTw=").append(countryZhTw);
        sb.append(", phoneCode=").append(phoneCode);
        sb.append(", shortCode=").append(shortCode);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}