package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value ="tb_business_day_fee_log")
@Data
public class BusinessDayFeeLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 商户id
     */
    private Integer businessId;

    private String walletType;

    /**
     * 今日用户充值（USD）该商户的用户每日充值至虚拟卡片中的金额的总和。
     */
    private BigDecimal dayRecharge;
    /**
     * 今日用户充值手续费（USD）该商户的用户每日充值至虚拟卡片中的金额的手续费的总和。
     */
    private BigDecimal dayRechargeCharge;
    /**
     * 今日用户开卡手续费（USD）该商户的用户每日open虚拟卡片中的开卡手续费的总和。
     */
    private BigDecimal dayOpenCardCharge;
    /**
     * '今日支出---根据平台设置给商户的费用总和（今日用户充值+今日手续费支出+今日开卡费支出+销卡支出）'
     */
    private BigDecimal dayBusinessTotalExpenditure;
    /**
     *'今日手续费支出根据平台设置给商户手续费，依会员充值金额进行数据采集
     * 举例：平台设置给商户1%手续费，商户设置给用户3%手续费。今日充值1万U今日手续费支出为 100U （商户需要给平台的充值费用成本）*'
     */
    private BigDecimal dayBusinessChargeExpenditure;
    /**
     *'今日手续费支出
     * 根据平台设置给商户手续费，依会员充值金额进行数据采集。\n
     * 举例：平台设置给商户1%手续费，商户设置给用户3%手续费。今日充值1万U日手续费支出为 100U （商户需要给平台的充值费用成本）*'
     */
    private BigDecimal dayBusinessOpenCardFeeExpenditure;
    /**
     * '当前商户账户中的余额，来源于商户的充值，提现或者该商户的用户开卡会减少余额。每日进行一次统计。'
     */
    private BigDecimal dayBusinessBalance;
    /**
     * '创建时间'
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createTime;
    /**
     * '创建日期'
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date CreateDate;
    /**
     * '商户充值'
     */
    private BigDecimal merchantDeposit;
    /**
     * '商户提现'
     */
    private BigDecimal merchantWithdrawal;
    /**
     * '卡注销余额返回'
     */
    private BigDecimal cardCancellationBalanceReturn;
    /**
     * 销卡费支出（给上游平台）
     */
    private BigDecimal cardCancellationFee;
}
