package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName tb_app_config
 */
@TableName(value = "tb_selenium_config")
@Data
public class SeleniumConfig implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    private String username;

    /**
     *
     */
    private String password;

    /**
     *
     */
    private String indexUrl;

    /**
     *
     */
    private String loginUrl;

    /**
     *
     */
    private String auth;

    /**
     * 是否启用 ，1：启用，2：禁用
     */
    private Integer state;

    /**
     * 标识
     */
    private String flag;


}