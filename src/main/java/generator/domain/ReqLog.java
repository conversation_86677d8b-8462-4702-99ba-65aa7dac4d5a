package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 回调记录 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_req_log")
public class ReqLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 渠道
     */
    @Column(value = "channel")
    private String channel;

    /**
     * 请求时间
     */
    @Column(value = "req_time")
    private Date reqTime;

    /**
     * 请求地址
     */
    @Column(value = "req_url")
    private String reqUrl;

    /**
     * 请求数据
     */
    @Column(value = "req_body")
    private String reqBody;

    /**
     * 请求头
     */
    @Column(value = "req_header")
    private String reqHeader;

    /**
     * 请求方法
     */
    @Column(value = "req_method")
    private String reqMethod;

    /**
     * 响应时间
     */
    @Column(value = "res_time")
    private Date resTime;

    /**
     * 响应数据
     */
    @Column(value = "res_body")
    private String resBody;

    /**
     * 响应头
     */
    @Column(value = "res_header")
    private String resHeader;

    /**
     * 状态 0 记录 1 成功
     */
    @Column(value = "status")
    private Integer status;


}
