package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 回调记录
 * @TableName tb_req_log
 */
@TableName(value ="tb_req_log")
@Data
public class ReqLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 请求时间
     */
    private Date reqTime;

    /**
     * 请求地址
     */
    private String reqUrl;

    /**
     * 请求数据
     */
    private String reqBody;

    /**
     * 请求头
     */
    private String reqHeader;

    /**
     * 请求方法
     */
    private String reqMethod;

    /**
     * 响应时间
     */
    private Date resTime;

    /**
     * 响应数据
     */
    private String resBody;

    /**
     * 响应头
     */
    private String resHeader;

    /**
     * 状态 0 记录 1 成功
     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ReqLog other = (ReqLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
            && (this.getReqTime() == null ? other.getReqTime() == null : this.getReqTime().equals(other.getReqTime()))
            && (this.getReqUrl() == null ? other.getReqUrl() == null : this.getReqUrl().equals(other.getReqUrl()))
            && (this.getReqBody() == null ? other.getReqBody() == null : this.getReqBody().equals(other.getReqBody()))
            && (this.getReqHeader() == null ? other.getReqHeader() == null : this.getReqHeader().equals(other.getReqHeader()))
            && (this.getReqMethod() == null ? other.getReqMethod() == null : this.getReqMethod().equals(other.getReqMethod()))
            && (this.getResTime() == null ? other.getResTime() == null : this.getResTime().equals(other.getResTime()))
            && (this.getResBody() == null ? other.getResBody() == null : this.getResBody().equals(other.getResBody()))
            && (this.getResHeader() == null ? other.getResHeader() == null : this.getResHeader().equals(other.getResHeader()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getReqTime() == null) ? 0 : getReqTime().hashCode());
        result = prime * result + ((getReqUrl() == null) ? 0 : getReqUrl().hashCode());
        result = prime * result + ((getReqBody() == null) ? 0 : getReqBody().hashCode());
        result = prime * result + ((getReqHeader() == null) ? 0 : getReqHeader().hashCode());
        result = prime * result + ((getReqMethod() == null) ? 0 : getReqMethod().hashCode());
        result = prime * result + ((getResTime() == null) ? 0 : getResTime().hashCode());
        result = prime * result + ((getResBody() == null) ? 0 : getResBody().hashCode());
        result = prime * result + ((getResHeader() == null) ? 0 : getResHeader().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", channel=").append(channel);
        sb.append(", reqTime=").append(reqTime);
        sb.append(", reqUrl=").append(reqUrl);
        sb.append(", reqBody=").append(reqBody);
        sb.append(", reqHeader=").append(reqHeader);
        sb.append(", reqMethod=").append(reqMethod);
        sb.append(", resTime=").append(resTime);
        sb.append(", resBody=").append(resBody);
        sb.append(", resHeader=").append(resHeader);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}