package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_payment_pay_payee")
public class PaymentPayPayee {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "payee_id")
    private String payeeId;

    /**
     * INDIVIDUAL 个人   / ENTERPRISE	企业
     */
    @Column(value = "subject_type")
    private String subjectType;

    /**
     * 国家、地区编码
     */
    @Column(value = "country")
    private String country;

    /**
     * 收款币种
     */
    @Column(value = "currency")
    private String currency;

    /**
     * 收款人名，类型为个人时必填
     */
    @Column(value = "first_name")
    private String firstName;

    /**
     * 收款人姓，类型为个人时必填
     */
    @Column(value = "last_name")
    private String lastName;

    /**
     * 公司名称，类型为公司时必填
     */
    @Column(value = "account_holder")
    private String accountHolder;

    /**
     * 区号
     */
    @Column(value = "nation_code")
    private String nationCode;

    /**
     * 手机号
     */
    @Column(value = "phone")
    private String phone;

    @Column(value = "address")
    private String address;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "pay_config_id")
    private Integer payConfigId;

    @Column(value = "business_id")
    private String businessId;

    /**
     * 付款用途
     */
    @Column(value = "payment_purpose")
    private String paymentPurpose;

    @Column(value = "two_letter_code")
    private String twoLetterCode;

    @Column(value = "english_name_country")
    private String englishNameCountry;

    @Column(value = "status")
    private Integer status;


}
