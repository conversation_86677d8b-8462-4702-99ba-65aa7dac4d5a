package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_user_wallet_log
 */
@TableName(value = "tb_user_wallet_log")
@Data
public class UserWalletLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户 ID
     */
    private Integer userId;

    /**
     * 商户 ID
     */
    private Integer businessId;

    /**
     * 操作类型:  1 划转进 2 划转出 3 交易扣减
     * 31开卡费 32划转充值 33充值手续费 34销卡返回 35 销卡手续费
     * 41开卡费收入 43充值手续费收入 45销卡费收入
     * 91 后台充值 92 后台扣减 93 提现驳回
     * 全部见 WalletTypeEnum
     */
    private Integer action;

    /**
     * 操作类型:  1 划转进 2 划转出 3 交易扣减
     * 31开卡费 32划转充值 33充值手续费 34销卡返回 35 销卡手续费
     * 41开卡费收入 43充值手续费收入 45销卡费收入
     * 91 后台充值 92 后台扣减 93 提现驳回
     * 全部见 WalletTypeEnum
     */
    @TableField(exist = false)
    private String actionStr;

    /**
     * 钱包类型
     * main_wallet 主账户钱包
     * store_wallet 储值卡钱包
     * share_wallet 共享卡钱包
     * physical_wallet 实体卡钱包
     * payment_wallet 代付钱包
     * usdt_wallet USDT 钱包
     * token_wallet代币钱包
     */
    private Object walletType;

    /**
     * 变动前金额
     */
    private BigDecimal changeBefore;

    /**
     * 变动金额
     */
    private BigDecimal changeMoney;

    /**
     * 变动后金额
     */
    private BigDecimal changeAfter;

    /**
     * 备注
     */
    private String note;

    /**
     * 关联 ID
     */
    private Integer targetId;

    /**
     * 流水号
     */
    private String orderId;


    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserWalletLog other = (UserWalletLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
                && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
                && (this.getAction() == null ? other.getAction() == null : this.getAction().equals(other.getAction()))
                && (this.getWalletType() == null ? other.getWalletType() == null : this.getWalletType().equals(other.getWalletType()))
                && (this.getChangeBefore() == null ? other.getChangeBefore() == null : this.getChangeBefore().equals(other.getChangeBefore()))
                && (this.getChangeMoney() == null ? other.getChangeMoney() == null : this.getChangeMoney().equals(other.getChangeMoney()))
                && (this.getChangeAfter() == null ? other.getChangeAfter() == null : this.getChangeAfter().equals(other.getChangeAfter()))
                && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
                && (this.getTargetId() == null ? other.getTargetId() == null : this.getTargetId().equals(other.getTargetId()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getAction() == null) ? 0 : getAction().hashCode());
        result = prime * result + ((getWalletType() == null) ? 0 : getWalletType().hashCode());
        result = prime * result + ((getChangeBefore() == null) ? 0 : getChangeBefore().hashCode());
        result = prime * result + ((getChangeMoney() == null) ? 0 : getChangeMoney().hashCode());
        result = prime * result + ((getChangeAfter() == null) ? 0 : getChangeAfter().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getTargetId() == null) ? 0 : getTargetId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", businessId=").append(businessId);
        sb.append(", action=").append(action);
        sb.append(", walletType=").append(walletType);
        sb.append(", changeBefore=").append(changeBefore);
        sb.append(", changeMoney=").append(changeMoney);
        sb.append(", changeAfter=").append(changeAfter);
        sb.append(", note=").append(note);
        sb.append(", targetId=").append(targetId);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}