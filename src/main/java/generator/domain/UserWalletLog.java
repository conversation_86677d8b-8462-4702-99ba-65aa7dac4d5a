package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_wallet_log")
public class UserWalletLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 用户 ID
     */
    @Column(value = "user_id")
    private Integer userId;

    /**
     * 商户 ID
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 操作类型:  1 划转进 2 划转出 3 交易扣减
     */
    @Column(value = "action")
    private Integer action;

    /**
     * 钱包类型
     */
    @Column(value = "wallet_type")
    private Object walletType;

    /**
     * 变动前金额
     */
    @Column(value = "change_before")
    private BigDecimal changeBefore;

    /**
     * 变动金额
     */
    @Column(value = "change_money")
    private BigDecimal changeMoney;

    /**
     * 变动后金额
     */
    @Column(value = "change_after")
    private BigDecimal changeAfter;

    /**
     * 备注
     */
    @Column(value = "note")
    private String note;

    /**
     * 关联 ID
     */
    @Column(value = "target_id")
    private Integer targetId;

    /**
     * 操作时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 流水号
     */
    @Column(value = "order_id")
    private String orderId;


}
