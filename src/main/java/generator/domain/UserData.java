package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_data")
public class UserData {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 所属商户
     */
    @Column(value = "user_id")
    private Integer userId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 是否平台商户  1 是 2 否
     */
    @Column(value = "platform_merchants")
    private Integer platformMerchants;

    /**
     * 姓名
     */
    @Column(value = "user_name")
    private String userName;

    /**
     * 国家/地区
     */
    @Column(value = "country")
    private String country;

    /**
     * 电话
     */
    @Column(value = "phone")
    private String phone;

    /**
     * 开卡成功的实体卡邮寄地址
     */
    @Column(value = "mailing_address")
    private String mailingAddress;

    /**
     * 证件号
     */
    @Column(value = "id_number")
    private Integer idNumber;

    /**
     * 证件图片
     */
    @Column(value = "id_image")
    private String idImage;

    /**
     * 0未审核/ 1审核通过/ 2 已驳回
     */
    @Column(value = "status")
    private String status;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;


}
