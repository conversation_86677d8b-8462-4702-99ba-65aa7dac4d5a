package generator.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_dev_config")
public class UserDevConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "user_id")
    private Integer userId;

    @Column(value = "business_id")
    private String businessId;

    @Column(value = "app_id")
    private String appId;

    @Column(value = "app_secret")
    private String appSecret;

    @Column(value = "cb_url")
    private String cbUrl;

    @Column(value = "ip_whitelist")
    private Object ipWhitelist;

    @Column(value = "enable_module")
    private Object enableModule;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;


}
