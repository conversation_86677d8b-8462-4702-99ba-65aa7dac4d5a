package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_money_log")
public class UserMoneyLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "order_id")
    private String orderId;

    /**
     * 1:BEP    2:ERC   3:TRC
     */
    @Column(value = "coin_type")
    private Integer coinType;

    /**
     * 商户id对应的tb_user表的id
     */
    @Column(value = "user_id")
    private Integer userId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    @Column(value = "before_money")
    private BigDecimal beforeMoney;

    @Column(value = "money")
    private BigDecimal money;

    @Column(value = "after_money")
    private BigDecimal afterMoney;

    /**
     * 变动类型 1：增加 2：减少 0:金额不变动
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 变动类型 1:充值 3：购买机器人  5:（分红钱包或者现金钱包）转入    9 赠送机器人 69：提现 1007：提现手续费 1009：提现驳回 20：购买机器人推荐直推奖励    25设置托管  26 取消托管  27后台资金变动
     * <p>
     * <p>
     * 1:充值 2：签到 :3：购买机器人 4：转出 5:（分红钱包或者现金钱包）转入 6：转出手续费 7：变现 :8：变现手续费   9 赠送机器人  20：购买机器人推荐直推奖励    25设置托管  26 取消托管  27后台资金变动   28托管金额变动  69：提现 1007：提现手续费 1009：提现驳回 新增： 30划转到银行卡 31划转手续费 32 开卡费 33 平台开卡手续费(无关商户) 34充值手续费返还 35 开卡返还 3 6 平台充值手续费(无关商户) 37销卡结算金额 38 销卡手续费扣除 40 划转到共享钱包
     */
    @Column(value = "sub_type")
    private Integer subType;

    @Column(value = "money_type")
    private String moneyType;

    @Column(value = "note")
    private String note;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 创建日期
     */
    @Column(value = "create_date")
    private Date createDate;

    /**
     * 1:提现待审核 2：提现审核通过 3：提现已驳回
     */
    @Column(value = "check_status")
    private Integer checkStatus;

    @Column(value = "check_admin")
    private String checkAdmin;

    @Column(value = "check_time")
    private Date checkTime;

    /**
     * 1:显示2：隐藏
     */
    @Column(value = "status")
    private Integer status;

    @Id(keyType = KeyType.Auto)
    private String transferHash;

    /**
     * 链类型，提币  1:BEP 2:ERC 3:TRC
     */
    @Column(value = "chain_type")
    private Integer chainType;

    /**
     * 提币地址1:ERC 2:BEP 3:TRC  (1:BEP 2:ERC 3:TRC)
     */
    @Column(value = "address")
    private String address;

    /**
     * 提现手续费（%）
     */
    @Column(value = "withdrawal_charge_rate")
    private BigDecimal withdrawalChargeRate;


}
