package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * @TableName tb_user_money_log
 */
@TableName(value = "tb_user_money_log")
@Data
public class UserMoneyLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    private String transferHash;

    /**
     *
     */
    private String orderId;

    /**
     * 1:BEP    2:ERC   3:TRC
     */
    private Integer coinType;

    /**
     * 商户id对应的tb_user表的id
     */
    private Integer userId;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     *
     */
    private BigDecimal beforeMoney;

    /**
     *
     */
    private BigDecimal money;

    /**
     *
     */
    private BigDecimal afterMoney;

    /**
     * 变动类型 1：增加 2：减少 0:金额不变动
     */
    private Integer type;

    /**
     * 变动类型 1:充值 3：购买机器人  5:（分红钱包或者现金钱包）转入    9 赠送机器人 69：提现 1007：提现手续费 1009：提现驳回 20：购买机器人推荐直推奖励    25设置托管  26 取消托管  27后台资金变动
     * <p>
     * <p>
     * 1:充值 2：签到 :3：购买机器人 4：转出 5:（分红钱包或者现金钱包）转入 6：转出手续费 7：变现 :8：变现手续费   9 赠送机器人  20：购买机器人推荐直推奖励    25设置托管  26 取消托管  27后台资金变动   28托管金额变动  69：提现 1007：提现手续费 1009：提现驳回 新增： 30划转到银行卡 31划转手续费 32 开卡费 33 平台开卡手续费(无关商户) 34充值手续费返还 35 开卡返还 3 6 平台充值手续费(无关商户) 37销卡结算金额 38 销卡手续费扣除 40 划转到共享钱包
     */
    private Integer subType;

    /**
     *
     */
    private String moneyType;

    /**
     *
     */
    private String note;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale = "zh", timezone = "GMT+8")
    private Date createDate;

    /**
     * 1:提现待审核 2：提现审核通过 3：提现已驳回
     */
    private Integer checkStatus;

    /**
     *
     */
    private String checkAdmin;

    /**
     *
     */
    private Date checkTime;

    /**
     * 1:显示2：隐藏
     */
    private Integer status;

    /**
     * 链类型，提币  1:BEP 2:ERC 3:TRC
     */
    private Integer chainType;

    /**
     * 提币地址1:ERC 2:BEP 3:TRC  (1:BEP 2:ERC 3:TRC)
     */
    private String address;

    /**
     * 提现手续费（%）
     */
    private BigDecimal withdrawalChargeRate;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserMoneyLog other = (UserMoneyLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getTransferHash() == null ? other.getTransferHash() == null : this.getTransferHash().equals(other.getTransferHash()))
                && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
                && (this.getCoinType() == null ? other.getCoinType() == null : this.getCoinType().equals(other.getCoinType()))
                && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
                && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
                && (this.getBeforeMoney() == null ? other.getBeforeMoney() == null : this.getBeforeMoney().equals(other.getBeforeMoney()))
                && (this.getMoney() == null ? other.getMoney() == null : this.getMoney().equals(other.getMoney()))
                && (this.getAfterMoney() == null ? other.getAfterMoney() == null : this.getAfterMoney().equals(other.getAfterMoney()))
                && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
                && (this.getSubType() == null ? other.getSubType() == null : this.getSubType().equals(other.getSubType()))
                && (this.getMoneyType() == null ? other.getMoneyType() == null : this.getMoneyType().equals(other.getMoneyType()))
                && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()))
                && (this.getCheckStatus() == null ? other.getCheckStatus() == null : this.getCheckStatus().equals(other.getCheckStatus()))
                && (this.getCheckAdmin() == null ? other.getCheckAdmin() == null : this.getCheckAdmin().equals(other.getCheckAdmin()))
                && (this.getCheckTime() == null ? other.getCheckTime() == null : this.getCheckTime().equals(other.getCheckTime()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getChainType() == null ? other.getChainType() == null : this.getChainType().equals(other.getChainType()))
                && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
                && (this.getWithdrawalChargeRate() == null ? other.getWithdrawalChargeRate() == null : this.getWithdrawalChargeRate().equals(other.getWithdrawalChargeRate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTransferHash() == null) ? 0 : getTransferHash().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getCoinType() == null) ? 0 : getCoinType().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getBeforeMoney() == null) ? 0 : getBeforeMoney().hashCode());
        result = prime * result + ((getMoney() == null) ? 0 : getMoney().hashCode());
        result = prime * result + ((getAfterMoney() == null) ? 0 : getAfterMoney().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getSubType() == null) ? 0 : getSubType().hashCode());
        result = prime * result + ((getMoneyType() == null) ? 0 : getMoneyType().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        result = prime * result + ((getCheckStatus() == null) ? 0 : getCheckStatus().hashCode());
        result = prime * result + ((getCheckAdmin() == null) ? 0 : getCheckAdmin().hashCode());
        result = prime * result + ((getCheckTime() == null) ? 0 : getCheckTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getChainType() == null) ? 0 : getChainType().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getWithdrawalChargeRate() == null) ? 0 : getWithdrawalChargeRate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", transferHash=").append(transferHash);
        sb.append(", orderId=").append(orderId);
        sb.append(", coinType=").append(coinType);
        sb.append(", userId=").append(userId);
        sb.append(", businessId=").append(businessId);
        sb.append(", beforeMoney=").append(beforeMoney);
        sb.append(", money=").append(money);
        sb.append(", afterMoney=").append(afterMoney);
        sb.append(", type=").append(type);
        sb.append(", subType=").append(subType);
        sb.append(", moneyType=").append(moneyType);
        sb.append(", note=").append(note);
        sb.append(", createTime=").append(createTime);
        sb.append(", createDate=").append(createDate);
        sb.append(", checkStatus=").append(checkStatus);
        sb.append(", checkAdmin=").append(checkAdmin);
        sb.append(", checkTime=").append(checkTime);
        sb.append(", status=").append(status);
        sb.append(", chainType=").append(chainType);
        sb.append(", address=").append(address);
        sb.append(", withdrawalChargeRate=").append(withdrawalChargeRate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}