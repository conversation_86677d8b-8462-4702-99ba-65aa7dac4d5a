package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_payment_payee_account")
public class PaymentPayeeAccount {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 收款人账户id
     */
    @Column(value = "account_id")
    private String accountId;

    /**
     * 收款人付款方式
     */
    @Column(value = "payment_method")
    private String paymentMethod;

    /**
     * 收款人账户编码
     */
    @Column(value = "account_no")
    private String accountNo;

    /**
     * 账户注册URL
     */
    @Column(value = "register_url")
    private String registerUrl;

    /**
     * PENDING	等待处理
     * ACTIVE	              正常
     * INACTIVE	停用
     */
    @Column(value = "status")
    private String status;

    /**
     * 收款人id
     */
    @Column(value = "payee_id")
    private String payeeId;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "business_id")
    private String businessId;


}
