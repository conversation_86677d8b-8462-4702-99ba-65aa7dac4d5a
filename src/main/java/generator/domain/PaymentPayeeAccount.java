package generator.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
/**
* 
* @TableName tb_payment_payee_account
*/
@Data
@TableName(value = "tb_payment_payee_account")
public class PaymentPayeeAccount implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
    * 收款人账户id
    */

    private String accountId;
    /**
    * 收款人付款方式
    */
    @NotBlank(message = "{Valid.paymentMethod}")
    private String paymentMethod;
    /**
    * 收款人账户编码
    */

    private String accountNo;
    /**
    * 账户注册URL
    */

    private String registerUrl;
    /**
    * PENDING	等待处理
    ACTIVE	              正常
    INACTIVE	停用
    */

    private String status;
    /**
    * 收款人id
    */

    private String payeeId;
    /**
    * 
    */

    private Date createTime;
    /**
    * 
    */

    private Integer businessId;
    /**
    * 收款人是否已有账号
    */

    private Integer haveAccount;
    /**
    * 收款人名字
    */
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String firstName;
    /**
    * 收款人中间名字

    */
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String middleName;
    /**
    * 收款人姓氏

    */
    @Pattern(regexp = "^[a-zA-Z\u0370-\u03FF\u1F00-\u1FFF]*$", message = "{Valid.accountHolder}")
    private String lastName;
    /**
    * "EMAIL" "PHONE" "CPF_CNPJ" "EVP"

    */

    private String walletAccountType;
    /**
    * 收款人识别号，11位或14位数字

    */

    private String documentId;
    /**
    * 手机号
    */

    private String phone;
    /**
    * 收款人地址
    */

    private String address;

}
