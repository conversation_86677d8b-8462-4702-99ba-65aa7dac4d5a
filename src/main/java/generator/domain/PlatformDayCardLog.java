package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_platform_day_card_log")
public class PlatformDayCardLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 当日平台总开卡的数量
     */
    @Column(value = "open_card_num")
    private Integer openCardNum;

    /**
     * 当日平台销卡的数量
     */
    @Column(value = "cancel_card_num")
    private Integer cancelCardNum;

    /**
     * 总开卡量（用昨天的总开卡量加上今天的开卡量，再减去今天销卡和过期的卡量。旨在统计，当天平台共计有多少张已激活可使用的卡片）
     */
    @Column(value = "have_card_num")
    private Integer haveCardNum;

    /**
     * 统计日期
     */
    @Column(value = "stat_date")
    private Date statDate;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;


}
