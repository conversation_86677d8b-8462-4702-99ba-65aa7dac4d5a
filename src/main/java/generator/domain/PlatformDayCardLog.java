package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_platform_day_card_log
 */
@TableName(value ="tb_platform_day_card_log")
@Data
public class PlatformDayCardLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 当日平台总开卡的数量
     */
    private Integer openCardNum;

    /**
     * 当日平台销卡的数量
     */
    private Integer cancelCardNum;

    /**
     * 总开卡量（用昨天的总开卡量加上今天的开卡量，再减去今天销卡和过期的卡量。旨在统计，当天平台共计有多少张已激活可使用的卡片）
     */
    private Integer haveCardNum;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PlatformDayCardLog other = (PlatformDayCardLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOpenCardNum() == null ? other.getOpenCardNum() == null : this.getOpenCardNum().equals(other.getOpenCardNum()))
            && (this.getCancelCardNum() == null ? other.getCancelCardNum() == null : this.getCancelCardNum().equals(other.getCancelCardNum()))
            && (this.getHaveCardNum() == null ? other.getHaveCardNum() == null : this.getHaveCardNum().equals(other.getHaveCardNum()))
            && (this.getStatDate() == null ? other.getStatDate() == null : this.getStatDate().equals(other.getStatDate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOpenCardNum() == null) ? 0 : getOpenCardNum().hashCode());
        result = prime * result + ((getCancelCardNum() == null) ? 0 : getCancelCardNum().hashCode());
        result = prime * result + ((getHaveCardNum() == null) ? 0 : getHaveCardNum().hashCode());
        result = prime * result + ((getStatDate() == null) ? 0 : getStatDate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", openCardNum=").append(openCardNum);
        sb.append(", cancelCardNum=").append(cancelCardNum);
        sb.append(", haveCardNum=").append(haveCardNum);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}