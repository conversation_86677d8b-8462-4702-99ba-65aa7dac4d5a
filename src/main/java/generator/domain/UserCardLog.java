package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_card_log")
public class UserCardLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户下的用户id
     */
    @Column(value = "child_user_id")
    private Integer childUserId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 消息通知类型【cardPay:卡消费通知、acctColl:收款入账通知、acctExchange:换汇结果通知、acctPay:付款相关通知、acctAuth:报备信息审核结果通知】另外自己加上  开卡费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  实际充值到账realCardRecharge  销卡结算金额cardCancel
     */
    @Column(value = "notice_type")
    private String noticeType;

    /**
     * 交易id
     */
    @Column(value = "order_id")
    private String orderId;

    /**
     * 预授权时间
     */
    @Column(value = "auth_time")
    private String authTime;

    /**
     * 币种
     */
    @Column(value = "trans_amount_currency")
    private String transAmountCurrency;

    /**
     * 交易金额
     */
    @Column(value = "trans_amount")
    private BigDecimal transAmount;

    @Column(value = "auth_amount_currency")
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    @Column(value = "auth_amount")
    private BigDecimal authAmount;

    /**
     * 结算金额
     */
    @Column(value = "settled_amount")
    private BigDecimal settledAmount;

    /**
     * 卡ID
     */
    @Column(value = "card_id")
    private String cardId;

    /**
     * 产品编码
     */
    @Column(value = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(value = "product_name")
    private String productName;

    /**
     * 脱敏卡号
     */
    @Column(value = "mask_card_number")
    private String maskCardNumber;

    /**
     * 卡模式 recharge 充值,share:共享卡 physical 实体卡
     */
    @Column(value = "card_model")
    private String cardModel;

    /**
     * 卡别名
     */
    @Column(value = "card_alias")
    private String cardAlias;

    /**
     * 商户名称
     */
    @Column(value = "merchant_name")
    private String merchantName;

    /**
     * 商户国家代码
     */
    @Column(value = "merchant_country_code")
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    @Column(value = "merchant_city")
    private String merchantCity;

    /**
     * 商户所在州或区
     */
    @Column(value = "merchant_state")
    private String merchantState;

    /**
     * 商户邮编
     */
    @Column(value = "merchant_zip_code")
    private String merchantZipCode;

    /**
     * 商户描述
     */
    @Column(value = "merchant_desc")
    private String merchantDesc;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    @Column(value = "status")
    private String status;

    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    @Column(value = "funds_direction")
    private String fundsDirection;

    /**
     * 交易类型【Consume:消费、ConsumeRefund:消费退款、ConsumeDispute:消费争议、DisputeRelease:争议释放、ConsumeReversal:消费冲正、ConsumeRefundReversal:消费退款冲正、AuthQuery:预授权查询、另外自己加上  开卡手续费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  销卡结算金额cardCancel】
     */
    @Column(value = "transaction_type")
    private String transactionType;

    /**
     * 失败原因
     */
    @Column(value = "failure_reason")
    private String failureReason;

    /**
     * 交易备注
     */
    @Column(value = "note")
    private String note;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 创建日期
     */
    @Column(value = "create_date")
    private Date createDate;

    /**
     * 可用额度
     */
    @Column(value = "available_credit")
    private BigDecimal availableCredit;


}
