package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qf.zpay.constants.CardModelEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_user_card_log
 */
@TableName(value = "tb_user_card_log")
@Data
public class UserCardLog implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商户下的用户id
     */
    private Integer childUserId;

    /**
     * 商户id
     */
    private Integer businessId;

    /**
     * 消息通知类型【cardPay:卡消费通知、acctColl:收款入账通知、acctExchange:换汇结果通知、acctPay:付款相关通知、acctAuth:报备信息审核结果通知】另外自己加上  开卡费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  实际充值到账realCardRecharge  销卡结算金额cardCancel
     */
    private String noticeType;

    /**
     * 交易id
     */
    private String orderId;

    /**
     * 预授权时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date authTime;

    /**
     * 币种
     */
    private String transAmountCurrency;

    /**
     * 交易金额
     */
    private BigDecimal transAmount;

    /**
     *
     */
    private String authAmountCurrency;

    /**
     * 预授权金额
     */
    private BigDecimal authAmount;

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 脱敏卡号
     */
    private String maskCardNumber;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;

    /**
     * 卡别名
     */
    private String cardAlias;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户国家代码
     */
    private String merchantCountryCode;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 商户所在州或区
     */
    private String merchantState;

    /**
     * 商户邮编
     */
    private String merchantZipCode;

    /**
     * 商户描述
     */
    private String merchantDesc;

    /**
     * 交易状态【AuthSuccessed:预授权成功、AuthFailure:预授权失败、Settled:已结算】
     */
    private String status;

    /**
     * 资金方向【Income:收入、Expenditure:支出、自己加的 Nochange不变化】
     */
    private String fundsDirection;

    /**
     * 交易类型【Consume:消费、ConsumeRefund:消费退款、ConsumeDispute:消费争议、DisputeRelease:争议释放、ConsumeReversal:消费冲正、ConsumeRefundReversal:消费退款冲正、AuthQuery:预授权查询、另外自己加上  开卡手续费cardOpenCharge 充值（划转）cardRecharge  充值手续费cardRechargeCharge  销卡结算金额cardCancel】
     */
    private String transactionType;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 交易备注
     */
    private String note;
    /**
     * 可用额度
     */
    private BigDecimal availableCredit;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserCardLog other = (UserCardLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getChildUserId() == null ? other.getChildUserId() == null : this.getChildUserId().equals(other.getChildUserId()))
                && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
                && (this.getNoticeType() == null ? other.getNoticeType() == null : this.getNoticeType().equals(other.getNoticeType()))
                && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
                && (this.getAuthTime() == null ? other.getAuthTime() == null : this.getAuthTime().equals(other.getAuthTime()))
                && (this.getTransAmountCurrency() == null ? other.getTransAmountCurrency() == null : this.getTransAmountCurrency().equals(other.getTransAmountCurrency()))
                && (this.getTransAmount() == null ? other.getTransAmount() == null : this.getTransAmount().equals(other.getTransAmount()))
                && (this.getAuthAmountCurrency() == null ? other.getAuthAmountCurrency() == null : this.getAuthAmountCurrency().equals(other.getAuthAmountCurrency()))
                && (this.getAuthAmount() == null ? other.getAuthAmount() == null : this.getAuthAmount().equals(other.getAuthAmount()))
                && (this.getSettledAmount() == null ? other.getSettledAmount() == null : this.getSettledAmount().equals(other.getSettledAmount()))
                && (this.getCardId() == null ? other.getCardId() == null : this.getCardId().equals(other.getCardId()))
                && (this.getProductCode() == null ? other.getProductCode() == null : this.getProductCode().equals(other.getProductCode()))
                && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
                && (this.getMaskCardNumber() == null ? other.getMaskCardNumber() == null : this.getMaskCardNumber().equals(other.getMaskCardNumber()))
                && (this.getCardModel() == null ? other.getCardModel() == null : this.getCardModel().equals(other.getCardModel()))
                && (this.getCardAlias() == null ? other.getCardAlias() == null : this.getCardAlias().equals(other.getCardAlias()))
                && (this.getMerchantName() == null ? other.getMerchantName() == null : this.getMerchantName().equals(other.getMerchantName()))
                && (this.getMerchantCountryCode() == null ? other.getMerchantCountryCode() == null : this.getMerchantCountryCode().equals(other.getMerchantCountryCode()))
                && (this.getMerchantCity() == null ? other.getMerchantCity() == null : this.getMerchantCity().equals(other.getMerchantCity()))
                && (this.getMerchantState() == null ? other.getMerchantState() == null : this.getMerchantState().equals(other.getMerchantState()))
                && (this.getMerchantZipCode() == null ? other.getMerchantZipCode() == null : this.getMerchantZipCode().equals(other.getMerchantZipCode()))
                && (this.getMerchantDesc() == null ? other.getMerchantDesc() == null : this.getMerchantDesc().equals(other.getMerchantDesc()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getFundsDirection() == null ? other.getFundsDirection() == null : this.getFundsDirection().equals(other.getFundsDirection()))
                && (this.getTransactionType() == null ? other.getTransactionType() == null : this.getTransactionType().equals(other.getTransactionType()))
                && (this.getFailureReason() == null ? other.getFailureReason() == null : this.getFailureReason().equals(other.getFailureReason()))
                && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getChildUserId() == null) ? 0 : getChildUserId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getNoticeType() == null) ? 0 : getNoticeType().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAuthTime() == null) ? 0 : getAuthTime().hashCode());
        result = prime * result + ((getTransAmountCurrency() == null) ? 0 : getTransAmountCurrency().hashCode());
        result = prime * result + ((getTransAmount() == null) ? 0 : getTransAmount().hashCode());
        result = prime * result + ((getAuthAmountCurrency() == null) ? 0 : getAuthAmountCurrency().hashCode());
        result = prime * result + ((getAuthAmount() == null) ? 0 : getAuthAmount().hashCode());
        result = prime * result + ((getSettledAmount() == null) ? 0 : getSettledAmount().hashCode());
        result = prime * result + ((getCardId() == null) ? 0 : getCardId().hashCode());
        result = prime * result + ((getProductCode() == null) ? 0 : getProductCode().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getMaskCardNumber() == null) ? 0 : getMaskCardNumber().hashCode());
        result = prime * result + ((getCardModel() == null) ? 0 : getCardModel().hashCode());
        result = prime * result + ((getCardAlias() == null) ? 0 : getCardAlias().hashCode());
        result = prime * result + ((getMerchantName() == null) ? 0 : getMerchantName().hashCode());
        result = prime * result + ((getMerchantCountryCode() == null) ? 0 : getMerchantCountryCode().hashCode());
        result = prime * result + ((getMerchantCity() == null) ? 0 : getMerchantCity().hashCode());
        result = prime * result + ((getMerchantState() == null) ? 0 : getMerchantState().hashCode());
        result = prime * result + ((getMerchantZipCode() == null) ? 0 : getMerchantZipCode().hashCode());
        result = prime * result + ((getMerchantDesc() == null) ? 0 : getMerchantDesc().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getFundsDirection() == null) ? 0 : getFundsDirection().hashCode());
        result = prime * result + ((getTransactionType() == null) ? 0 : getTransactionType().hashCode());
        result = prime * result + ((getFailureReason() == null) ? 0 : getFailureReason().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", childUserId=").append(childUserId);
        sb.append(", businessId=").append(businessId);
        sb.append(", noticeType=").append(noticeType);
        sb.append(", orderId=").append(orderId);
        sb.append(", authTime=").append(authTime);
        sb.append(", transAmountCurrency=").append(transAmountCurrency);
        sb.append(", transAmount=").append(transAmount);
        sb.append(", authAmountCurrency=").append(authAmountCurrency);
        sb.append(", authAmount=").append(authAmount);
        sb.append(", settledAmount=").append(settledAmount);
        sb.append(", cardId=").append(cardId);
        sb.append(", productCode=").append(productCode);
        sb.append(", productName=").append(productName);
        sb.append(", maskCardNumber=").append(maskCardNumber);
        sb.append(", cardModel=").append(cardModel);
        sb.append(", cardAlias=").append(cardAlias);
        sb.append(", merchantName=").append(merchantName);
        sb.append(", merchantCountryCode=").append(merchantCountryCode);
        sb.append(", merchantCity=").append(merchantCity);
        sb.append(", merchantState=").append(merchantState);
        sb.append(", merchantZipCode=").append(merchantZipCode);
        sb.append(", merchantDesc=").append(merchantDesc);
        sb.append(", status=").append(status);
        sb.append(", fundsDirection=").append(fundsDirection);
        sb.append(", transactionType=").append(transactionType);
        sb.append(", failureReason=").append(failureReason);
        sb.append(", note=").append(note);
        sb.append(", createTime=").append(createTime);
        sb.append(", createDate=").append(createDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}