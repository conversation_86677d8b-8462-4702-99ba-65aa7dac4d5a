package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_customer_service_info")
public class CustomerServiceInfo {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "name")
    private String name;

    @Column(value = "whats_app")
    private String whatsApp;

    @Column(value = "we_chat")
    private String weChat;

    @Column(value = "telegram")
    private String telegram;

    @Column(value = "create_time")
    private Date createTime;


}
