package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 支付配置信息实体类
 *
 * 该类对应数据库表 `tb_payment_pay_config`，用于存储支付产品的相关配置信息。
 */
@Data
@TableName("tb_payment_pay_config")
public class PaymentPayConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 1 国家二字码信息 ，2 电子钱包字段
     */
    private Integer status;

    /**
     * 国家/地区
     */
    private String countryRegion;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 支付产品
     */
    private String paymentProduct;

    /**
     * 当地市场地位
     */
    private String localMarketPosition;

    /**
     * 交易币种
     */
    private String transactionCurrency;

    /**
     * 结算币种
     */
    private String settlementCurrency;

    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    /**
     * 单笔手续费
     */
    private BigDecimal totalFee;

    /**
     * 结算周期（天）
     */
    private Integer settlementCycle;

    private Integer parentId;

    /**
     * 汇率信息
     */
    private String fx;

    private String englishNameCountry ;


    /**
     * 支付渠道
     */
    private String channel;

    private String twoLetterCode;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录最后更新时间
     */
    private Date updateTime;


}
