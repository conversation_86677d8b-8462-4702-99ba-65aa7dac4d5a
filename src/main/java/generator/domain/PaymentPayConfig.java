package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_payment_pay_config")
public class PaymentPayConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 二字码
     */
    @Column(value = "two_letter_code")
    private String twoLetterCode;

    /**
     * 国家/地区
     */
    @Column(value = "country_region")
    private String countryRegion;

    /**
     * 产品类型
     */
    @Column(value = "product_type")
    private String productType;

    /**
     * 支付产品
     */
    @Column(value = "payment_product")
    private String paymentProduct;

    /**
     * 当地市场地位
     */
    @Column(value = "local_market_position")
    private String localMarketPosition;

    /**
     * 交易币种
     */
    @Column(value = "transaction_currency")
    private String transactionCurrency;

    /**
     * 结算币种
     */
    @Column(value = "settlement_currency")
    private String settlementCurrency;

    /**
     * 单笔手续费
     */
    @Column(value = "total_fee")
    private BigDecimal totalFee;

    /**
     * 手续费率
     */
    @Column(value = "fee_rate")
    private BigDecimal feeRate;

    /**
     * 结算周期
     */
    @Column(value = "settlement_cycle")
    private Integer settlementCycle;

    /**
     * fx
     */
    @Column(value = "fx")
    private String fx;

    /**
     * 渠道
     */
    @Column(value = "channel")
    private String channel;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 1正常使用 2禁用
     */
    @Column(value = "status")
    private String status;

    @Column(value = "english_name_country")
    private String englishNameCountry;


}
