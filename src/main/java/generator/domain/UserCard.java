package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_card")
public class UserCard {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户id
     */
    @Column(value = "uid")
    private Integer uid;

    /**
     * 商户旗下的用户id
     */
    @Column(value = "uid_child")
    private Integer uidChild;

    /**
     * 可用金额
     */
    @Column(value = "amount")
    private BigDecimal amount;

    @Column(value = "virtual_amt")
    private BigDecimal virtualAmt;

    /**
     * 产品编码
     */
    @Column(value = "product_code")
    private String productCode;

    /**
     * 卡模式 recharge 充值,share:共享卡 physical 实体卡
     */
    @Column(value = "card_model")
    private String cardModel;

    /**
     * 开卡币种
     */
    @Column(value = "card_currency")
    private String cardCurrency;

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    @Column(value = "card_amt")
    private String cardAmt;

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    @Column(value = "card_total_amt")
    private String cardTotalAmt;

    /**
     * 订单任务编号
     */
    @Column(value = "order_no")
    private String orderNo;

    /**
     * 卡片ID
     */
    @Column(value = "card_id")
    private String cardId;

    /**
     * 有效期
     */
    @Column(value = "card_expiration_mmyy")
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    @Column(value = "card_cvv")
    private String cardCvv;

    /**
     * 卡号
     */
    @Column(value = "card_number")
    private String cardNumber;

    /**
     * 卡状态【Active:活跃,Blocked:锁定,Cancel:注销,Expired:过期】
     */
    @Column(value = "card_status")
    private String cardStatus;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 卡片申请时间
     */
    @Column(value = "apply_time")
    private Date applyTime;

    @Column(value = "status")
    private Integer status;

    /**
     * 实际开卡的时间
     */
    @Column(value = "open_card_time")
    private Date openCardTime;

    /**
     * 实际开卡的日期
     */
    @Column(value = "open_card_date")
    private Date openCardDate;

    /**
     * 注销卡片的时间
     */
    @Column(value = "cancel_card_time")
    private Date cancelCardTime;

    /**
     * 注销卡片的日期
     */
    @Column(value = "cancel_card_date")
    private Date cancelCardDate;

    /**
     * 上游渠道
     */
    @Column(value = "channel")
    private Object channel;

    @Column(value = "card_manage_id")
    private Integer cardManageId;

    @Column(value = "card_nickname")
    private String cardNickname;

    /**
     * 持卡人ID
     */
    @Column(value = "holder_id")
    private Integer holderId;

    /**
     * 卡组织
     */
    @Column(value = "card_scheme")
    private Object cardScheme;


}
