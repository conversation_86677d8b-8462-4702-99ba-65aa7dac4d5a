package generator.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qf.zpay.constants.CardChannelEnum;
import com.qf.zpay.constants.CardModelEnum;
import com.qf.zpay.constants.CardSchemeEnum;
import com.qf.zpay.constants.CardStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_user_card
 */
@TableName(value = "tb_user_card")
@Data
public class UserCard implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商户id
     */
    private Integer uid;

    /**
     * 商户旗下的用户id
     */
    private Integer uidChild;

    /**
     * 可用金额
     */
    private BigDecimal amount;

    /**
     *
     */
    private BigDecimal virtualAmt;

    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 卡昵称
     */
    private String cardNickname;

    /**
     * 卡模式
     */
    private CardModelEnum cardModel;

    /**
     * 开卡币种
     */
    private String cardCurrency;

    /**
     * 单笔限额（每次充值后在原来的基础上加上充值金额）
     */
    private BigDecimal cardAmt;

    /**
     * 单卡消费总额度【共享卡时选填,为0表示不限额】每次充值后在原来的基础上加上充值金额）
     */
    private BigDecimal cardTotalAmt;

    /**
     * 订单任务编号
     */
    private String orderNo;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 有效期
     */
    private String cardExpirationMmyy;

    /**
     * CVV
     */
    private String cardCvv;

    /**
     * 卡号
     */
    @ExcelProperty("cardNumber")
    private String cardNumber;

    /**
     * 卡状态
     */
    private CardStatusEnum cardStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 卡片申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 卡片申请处理状态 0
     */
    private Integer status;

    /**
     * 实际开卡的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardTime;

    /**
     * 实际开卡的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date openCardDate;

    /**
     * 注销卡片的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardTime;

    /**
     * 注销卡片的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelCardDate;

    /**
     * 上游渠道
     */
    private CardChannelEnum channel;

    /**
     *
     */
    private Integer cardManageId;

    /**
     * 持卡人信息
     */
    private Integer holderId;

    /**
     * 卡组织
     */
    private CardSchemeEnum cardScheme;

    /**
     * 联系信息 email or phone
     */
    private String contact;


    @TableField(exist = false)
    private BigDecimal upAmount;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserCard other = (UserCard) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getUid() == null ? other.getUid() == null : this.getUid().equals(other.getUid()))
                && (this.getUidChild() == null ? other.getUidChild() == null : this.getUidChild().equals(other.getUidChild()))
                && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
                && (this.getVirtualAmt() == null ? other.getVirtualAmt() == null : this.getVirtualAmt().equals(other.getVirtualAmt()))
                && (this.getProductCode() == null ? other.getProductCode() == null : this.getProductCode().equals(other.getProductCode()))
                && (this.getCardModel() == null ? other.getCardModel() == null : this.getCardModel().equals(other.getCardModel()))
                && (this.getCardCurrency() == null ? other.getCardCurrency() == null : this.getCardCurrency().equals(other.getCardCurrency()))
                && (this.getCardAmt() == null ? other.getCardAmt() == null : this.getCardAmt().equals(other.getCardAmt()))
                && (this.getCardTotalAmt() == null ? other.getCardTotalAmt() == null : this.getCardTotalAmt().equals(other.getCardTotalAmt()))
                && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
                && (this.getCardId() == null ? other.getCardId() == null : this.getCardId().equals(other.getCardId()))
                && (this.getCardExpirationMmyy() == null ? other.getCardExpirationMmyy() == null : this.getCardExpirationMmyy().equals(other.getCardExpirationMmyy()))
                && (this.getCardCvv() == null ? other.getCardCvv() == null : this.getCardCvv().equals(other.getCardCvv()))
                && (this.getCardNumber() == null ? other.getCardNumber() == null : this.getCardNumber().equals(other.getCardNumber()))
                && (this.getCardStatus() == null ? other.getCardStatus() == null : this.getCardStatus().equals(other.getCardStatus()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getApplyTime() == null ? other.getApplyTime() == null : this.getApplyTime().equals(other.getApplyTime()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getOpenCardTime() == null ? other.getOpenCardTime() == null : this.getOpenCardTime().equals(other.getOpenCardTime()))
                && (this.getOpenCardDate() == null ? other.getOpenCardDate() == null : this.getOpenCardDate().equals(other.getOpenCardDate()))
                && (this.getCancelCardTime() == null ? other.getCancelCardTime() == null : this.getCancelCardTime().equals(other.getCancelCardTime()))
                && (this.getCancelCardDate() == null ? other.getCancelCardDate() == null : this.getCancelCardDate().equals(other.getCancelCardDate()))
                && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
                && (this.getCardManageId() == null ? other.getCardManageId() == null : this.getCardManageId().equals(other.getCardManageId()))
                && (this.getHolderId() == null ? other.getHolderId() == null : this.getHolderId().equals(other.getHolderId()))
                && (this.getCardScheme() == null ? other.getCardScheme() == null : this.getCardScheme().equals(other.getCardScheme()))
                && (this.getContact() == null ? other.getContact() == null : this.getContact().equals(other.getContact()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUid() == null) ? 0 : getUid().hashCode());
        result = prime * result + ((getUidChild() == null) ? 0 : getUidChild().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getVirtualAmt() == null) ? 0 : getVirtualAmt().hashCode());
        result = prime * result + ((getProductCode() == null) ? 0 : getProductCode().hashCode());
        result = prime * result + ((getCardModel() == null) ? 0 : getCardModel().hashCode());
        result = prime * result + ((getCardCurrency() == null) ? 0 : getCardCurrency().hashCode());
        result = prime * result + ((getCardAmt() == null) ? 0 : getCardAmt().hashCode());
        result = prime * result + ((getCardTotalAmt() == null) ? 0 : getCardTotalAmt().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getCardId() == null) ? 0 : getCardId().hashCode());
        result = prime * result + ((getCardExpirationMmyy() == null) ? 0 : getCardExpirationMmyy().hashCode());
        result = prime * result + ((getCardCvv() == null) ? 0 : getCardCvv().hashCode());
        result = prime * result + ((getCardNumber() == null) ? 0 : getCardNumber().hashCode());
        result = prime * result + ((getCardStatus() == null) ? 0 : getCardStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getApplyTime() == null) ? 0 : getApplyTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getOpenCardTime() == null) ? 0 : getOpenCardTime().hashCode());
        result = prime * result + ((getOpenCardDate() == null) ? 0 : getOpenCardDate().hashCode());
        result = prime * result + ((getCancelCardTime() == null) ? 0 : getCancelCardTime().hashCode());
        result = prime * result + ((getCancelCardDate() == null) ? 0 : getCancelCardDate().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getCardManageId() == null) ? 0 : getCardManageId().hashCode());
        result = prime * result + ((getHolderId() == null) ? 0 : getHolderId().hashCode());
        result = prime * result + ((getCardScheme() == null) ? 0 : getCardScheme().hashCode());
        result = prime * result + ((getContact() == null) ? 0 : getContact().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", uid=").append(uid);
        sb.append(", uidChild=").append(uidChild);
        sb.append(", amount=").append(amount);
        sb.append(", virtualAmt=").append(virtualAmt);
        sb.append(", productCode=").append(productCode);
        sb.append(", cardModel=").append(cardModel);
        sb.append(", cardCurrency=").append(cardCurrency);
        sb.append(", cardAmt=").append(cardAmt);
        sb.append(", cardTotalAmt=").append(cardTotalAmt);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", cardId=").append(cardId);
        sb.append(", cardExpirationMmyy=").append(cardExpirationMmyy);
        sb.append(", cardCvv=").append(cardCvv);
        sb.append(", cardNumber=").append(cardNumber);
        sb.append(", cardStatus=").append(cardStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", applyTime=").append(applyTime);
        sb.append(", status=").append(status);
        sb.append(", openCardTime=").append(openCardTime);
        sb.append(", openCardDate=").append(openCardDate);
        sb.append(", cancelCardTime=").append(cancelCardTime);
        sb.append(", cancelCardDate=").append(cancelCardDate);
        sb.append(", channel=").append(channel);
        sb.append(", cardManageId=").append(cardManageId);
        sb.append(", holderId=").append(holderId);
        sb.append(", cardScheme=").append(cardScheme);
        sb.append(", contact=").append(contact);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}