package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_platform_day_money_log")
public class PlatformDayMoneyLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 今日充值 --- 所有商户当日在平台执行充值操作的金额
     */
    @Column(value = "total_recharge")
    private BigDecimal totalRecharge;

    /**
     * 今日提现 --- 所有商户当日在平台执行提现操作的金额
     */
    @Column(value = "total_withdraw")
    private BigDecimal totalWithdraw;

    /**
     * 今日盈利 --- 平台从商户处赚取的所有利润总和，包括开卡费以及手续费。比如，上游给平台的开卡费为1usd一张，平台给商户开卡费3usd一张，那么开卡费的收益即为2usd。手续费同理。手续费收益与开卡费收益相加的历史数据，即为总盈利。
     */
    @Column(value = "total_earn")
    private BigDecimal totalEarn;

    /**
     * 平台TVT--平台账户余额---平台总的资金沉淀量，包括平台所有的商户充值进来尚未消费掉的资金。
     */
    @Column(value = "total_balance")
    private BigDecimal totalBalance;

    /**
     * 统计日期
     */
    @Column(value = "stat_date")
    private Date statDate;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;


}
