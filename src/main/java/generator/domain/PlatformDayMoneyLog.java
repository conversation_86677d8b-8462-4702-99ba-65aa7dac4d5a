package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_platform_day_money_log
 */
@TableName(value ="tb_platform_day_money_log")
@Data
public class PlatformDayMoneyLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 今日充值 --- 所有商户当日在平台执行充值操作的金额
     */
    private BigDecimal totalRecharge;

    /**
     * 今日提现 --- 所有商户当日在平台执行提现操作的金额
     */
    private BigDecimal totalWithdraw;

    /**
     * 今日盈利 --- 平台从商户处赚取的所有利润总和，包括开卡费以及手续费。比如，上游给平台的开卡费为1usd一张，平台给商户开卡费3usd一张，那么开卡费的收益即为2usd。手续费同理。手续费收益与开卡费收益相加的历史数据，即为总盈利。
     */
    private BigDecimal totalEarn;

    /**
     * 平台TVT--平台账户余额---平台总的资金沉淀量，包括平台所有的商户充值进来尚未消费掉的资金。
     */
    private BigDecimal totalBalance;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PlatformDayMoneyLog other = (PlatformDayMoneyLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTotalRecharge() == null ? other.getTotalRecharge() == null : this.getTotalRecharge().equals(other.getTotalRecharge()))
            && (this.getTotalWithdraw() == null ? other.getTotalWithdraw() == null : this.getTotalWithdraw().equals(other.getTotalWithdraw()))
            && (this.getTotalEarn() == null ? other.getTotalEarn() == null : this.getTotalEarn().equals(other.getTotalEarn()))
            && (this.getTotalBalance() == null ? other.getTotalBalance() == null : this.getTotalBalance().equals(other.getTotalBalance()))
            && (this.getStatDate() == null ? other.getStatDate() == null : this.getStatDate().equals(other.getStatDate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTotalRecharge() == null) ? 0 : getTotalRecharge().hashCode());
        result = prime * result + ((getTotalWithdraw() == null) ? 0 : getTotalWithdraw().hashCode());
        result = prime * result + ((getTotalEarn() == null) ? 0 : getTotalEarn().hashCode());
        result = prime * result + ((getTotalBalance() == null) ? 0 : getTotalBalance().hashCode());
        result = prime * result + ((getStatDate() == null) ? 0 : getStatDate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", totalRecharge=").append(totalRecharge);
        sb.append(", totalWithdraw=").append(totalWithdraw);
        sb.append(", totalEarn=").append(totalEarn);
        sb.append(", totalBalance=").append(totalBalance);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}