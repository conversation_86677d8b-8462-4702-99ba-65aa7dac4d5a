package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 支付渠道配置
 * <p>
 * 该类对应数据库表 `tb_payment_channel`，用于存储支付产品的相关配置信息。
 */
@Data
@TableName("tb_payment_channel")
public class PaymentChannel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 0 禁用 ，1启用
     */
    private Integer state;

    /**
     * 编码
     */
    private String code;

    /**
     * 国家
     */
    private String country;

    /**
     * 货币
     */
    private String currency;

    /**
     * 时间
     */
    private Date dateTime;


}
