package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代付流水
 *
 * @TableName tb_business_day_payment_flow_log
 */
@Data
@TableName(value = "tb_business_day_payment_flow_log")
public class BusinessDayPaymentFlowLog implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 商户id
     */
    @TableField(value = "business_id")
    private Integer businessId;
    /**
     * 今日支出
     */
    @TableField(value = "day_expend")
    private BigDecimal dayExpend;
    /**
     * 今日收益
     */
    @TableField(value = "day_earnings")
    private BigDecimal dayEarnings;
    /**
     * 用户总到账金额
     */
    @TableField(value = "day_user_arrival_amount")
    private BigDecimal dayUserArrivalAmount;
    /**
     * 今日成功数
     */
    @TableField(value = "day_succeed_sum")
    private Integer daySucceedSum;
    /**
     * 历史总支出
     */
    @TableField(value = "history_expend")
    private BigDecimal historyExpend;
    /**
     * 历史总收益
     */
    @TableField(value = "history_earnings")
    private BigDecimal historyEarnings;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date statDate;
}