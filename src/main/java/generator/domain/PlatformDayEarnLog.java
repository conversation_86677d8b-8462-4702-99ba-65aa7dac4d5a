package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_platform_day_earn_log")
public class PlatformDayEarnLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 今日开卡费（USD）平台设置给商户的开卡费（今日总开卡费）
     */
    @Column(value = "open_card_fee_total")
    private BigDecimal openCardFeeTotal;

    /**
     * 今日开卡费收益(USD）平台设置给商户的开卡费（今日总开卡费）（减去-）上游渠道设置给平台的开卡费（支出）
     举例：上游渠道开卡费1U
     平台设置商户开卡费8U
     今日开卡费收益为8-1=7
     */
    @Column(value = "open_card_fee_earn")
    private BigDecimal openCardFeeEarn;

    /**
     * 今日充值手续费（USD）平台设置给商户的充值手续费（今日总手续费）
     */
    @Column(value = "recharge_fee_total")
    private BigDecimal rechargeFeeTotal;

    /**
     * 今日充值手续费收益（USD） 平台设置给商户的充值手续费（今日总充值手续费）（减去-）上游渠道设置给平台的充值手续费（支出）
     举例：上游渠道设置平台充值手续费0.5%
     平台设置商户充值手续费3%
     今日充值1万U
     今日充值手续费收益为 250U
     (3-0.5)% x 1万
     */
    @Column(value = "recharge_fee_earn")
    private BigDecimal rechargeFeeEarn;

    /**
     * 总销卡费
     */
    @Column(value = "cancel_card_fee_total")
    private BigDecimal cancelCardFeeTotal;

    /**
     * 销卡费收益
     */
    @Column(value = "cancel_card_fee_earn")
    private BigDecimal cancelCardFeeEarn;

    /**
     * 今日总收益
     */
    @Column(value = "day_earn")
    private BigDecimal dayEarn;

    /**
     * 历史总收益
     */
    @Column(value = "history_earn")
    private BigDecimal historyEarn;

    /**
     * 统计日期
     */
    @Column(value = "stat_date")
    private Date statDate;

    /**
     * 创建时间
     */
    @Column(value = "create_time")
    private Date createTime;


}
