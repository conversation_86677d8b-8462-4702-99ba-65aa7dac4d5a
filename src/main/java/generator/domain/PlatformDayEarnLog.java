package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName tb_platform_day_earn_log
 */
@TableName(value ="tb_platform_day_earn_log")
@Data
public class PlatformDayEarnLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 今日开卡费（USD）平台设置给商户的开卡费（今日总开卡费）
     */
    private BigDecimal openCardFeeTotal;

    /**
     * 今日开卡费收益(USD）平台设置给商户的开卡费（今日总开卡费）（减去-）上游渠道设置给平台的开卡费（支出）
举例：上游渠道开卡费1U
平台设置商户开卡费8U
今日开卡费收益为8-1=7
     */
    private BigDecimal openCardFeeEarn;

    /**
     * 今日充值手续费（USD）平台设置给商户的充值手续费（今日总手续费）
     */
    private BigDecimal rechargeFeeTotal;

    /**
     * 今日充值手续费收益（USD） 平台设置给商户的充值手续费（今日总充值手续费）（减去-）上游渠道设置给平台的充值手续费（支出）
举例：上游渠道设置平台充值手续费0.5%
平台设置商户充值手续费3%
今日充值1万U
今日充值手续费收益为 250U
(3-0.5)% x 1万
     */
    private BigDecimal rechargeFeeEarn;

    /**
     * 总销卡费
     */
    private BigDecimal cancelCardFeeTotal;

    /**
     * 销卡费收益
     */
    private BigDecimal cancelCardFeeEarn;

    /**
     * 今日总收益
     */
    private BigDecimal dayEarn;

    /**
     * 历史总收益
     */
    private BigDecimal historyEarn;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PlatformDayEarnLog other = (PlatformDayEarnLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOpenCardFeeTotal() == null ? other.getOpenCardFeeTotal() == null : this.getOpenCardFeeTotal().equals(other.getOpenCardFeeTotal()))
            && (this.getOpenCardFeeEarn() == null ? other.getOpenCardFeeEarn() == null : this.getOpenCardFeeEarn().equals(other.getOpenCardFeeEarn()))
            && (this.getRechargeFeeTotal() == null ? other.getRechargeFeeTotal() == null : this.getRechargeFeeTotal().equals(other.getRechargeFeeTotal()))
            && (this.getRechargeFeeEarn() == null ? other.getRechargeFeeEarn() == null : this.getRechargeFeeEarn().equals(other.getRechargeFeeEarn()))
            && (this.getCancelCardFeeTotal() == null ? other.getCancelCardFeeTotal() == null : this.getCancelCardFeeTotal().equals(other.getCancelCardFeeTotal()))
            && (this.getCancelCardFeeEarn() == null ? other.getCancelCardFeeEarn() == null : this.getCancelCardFeeEarn().equals(other.getCancelCardFeeEarn()))
            && (this.getDayEarn() == null ? other.getDayEarn() == null : this.getDayEarn().equals(other.getDayEarn()))
            && (this.getHistoryEarn() == null ? other.getHistoryEarn() == null : this.getHistoryEarn().equals(other.getHistoryEarn()))
            && (this.getStatDate() == null ? other.getStatDate() == null : this.getStatDate().equals(other.getStatDate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOpenCardFeeTotal() == null) ? 0 : getOpenCardFeeTotal().hashCode());
        result = prime * result + ((getOpenCardFeeEarn() == null) ? 0 : getOpenCardFeeEarn().hashCode());
        result = prime * result + ((getRechargeFeeTotal() == null) ? 0 : getRechargeFeeTotal().hashCode());
        result = prime * result + ((getRechargeFeeEarn() == null) ? 0 : getRechargeFeeEarn().hashCode());
        result = prime * result + ((getCancelCardFeeTotal() == null) ? 0 : getCancelCardFeeTotal().hashCode());
        result = prime * result + ((getCancelCardFeeEarn() == null) ? 0 : getCancelCardFeeEarn().hashCode());
        result = prime * result + ((getDayEarn() == null) ? 0 : getDayEarn().hashCode());
        result = prime * result + ((getHistoryEarn() == null) ? 0 : getHistoryEarn().hashCode());
        result = prime * result + ((getStatDate() == null) ? 0 : getStatDate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", openCardFeeTotal=").append(openCardFeeTotal);
        sb.append(", openCardFeeEarn=").append(openCardFeeEarn);
        sb.append(", rechargeFeeTotal=").append(rechargeFeeTotal);
        sb.append(", rechargeFeeEarn=").append(rechargeFeeEarn);
        sb.append(", cancelCardFeeTotal=").append(cancelCardFeeTotal);
        sb.append(", cancelCardFeeEarn=").append(cancelCardFeeEarn);
        sb.append(", dayEarn=").append(dayEarn);
        sb.append(", historyEarn=").append(historyEarn);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}