package generator.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName tb_mailing_address
 */
@Data
@TableName(value = "tb_mailing_address")
public class MailingAddress implements Serializable {
    /**
     * -- GETTER --
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * kyc id
     * -- GETTER --
     * kyc id
     */
    @TableField(value = "holder_id")
    private Integer holderId;

    /**
     * 卡id
     * -- GETTER --
     * 卡id
     */
    @TableField(value = "card_id")
    private String cardId;

    /**
     * 城市
     */
    @TableField(value = "city")
    private String city;
    /**
     * 省
     */
    @TableField(value = "state")
    private String state;
    /**
     * 街
     */
    @TableField(value = "street")
    private String street;
    /**
     * 邮编
     * -- GETTER --
     * 邮编
     */
    @TableField(value = "postcode")
    private String postcode;

    /**
     * 详细地址
     * -- GETTER --
     * 详细地址
     */
    @TableField(value = "address")
    private String address;


    @TableField(value = "nation_code")
    private String nationCode;

    /**
     * 国家
     * -- GETTER --
     * 国家
     */
    @TableField(value = "country")
    private String country;
    /**
     * 电话
     */
    @TableField(value = "phone")
    private String phone;
    /**
     * 用户
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}