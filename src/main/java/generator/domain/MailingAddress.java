package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_mailing_address")
public class MailingAddress {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * kyc id
     */
    @Column(value = "holder_id")
    private Integer holderId;

    /**
     * 卡id
     */
    @Column(value = "card_id")
    private String cardId;

    /**
     * 邮编
     */
    @Column(value = "postcode")
    private String postcode;

    /**
     * 详细地址
     */
    @Column(value = "address")
    private String address;

    /**
     * 区号
     */
    @Column(value = "nation_code")
    private String nationCode;

    /**
     * 国家
     */
    @Column(value = "country")
    private String country;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 电话
     */
    @Column(value = "phone")
    private String phone;

    /**
     * 用户
     */
    @Column(value = "user_name")
    private String userName;


}
