package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_business_day_in_out_log")
public class BusinessDayInOutLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "business_id")
    private Integer businessId;

    @Column(value = "in")
    private BigDecimal in;

    @Column(value = "out")
    private BigDecimal out;

    @Column(value = "in_fee")
    private BigDecimal inFee;

    @Column(value = "out_fee")
    private BigDecimal outFee;

    @Column(value = "stat_date")
    private Date statDate;


}
