package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Value;

import java.math.BigDecimal;

@Data
@TableName(value = "tb_user_payment_config")
public class UserPaymentConfig {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer paymentConfigId;
    private BigDecimal totalFee;
    private BigDecimal feeRate;
    private Integer businessId;

}
