package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_payment_config")
public class UserPaymentConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "payment_config_id")
    private Integer paymentConfigId;

    @Column(value = "total_fee")
    private BigDecimal totalFee;

    /**
     * 手续费率
     */
    @Column(value = "fee_rate")
    private BigDecimal feeRate;

    @Column(value = "business_id")
    private String businessId;


}
