package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName tb_user_data
 */
@Data
@TableName(value = "tb_card_holder")
public class CardHolder implements Serializable {
    /**
     * -- GETTER --
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 所属商户
     */
    private Integer userId;

    /**
     * 商户ID
     */
    private Integer businessId;

    /**
     * 英文名字/拼音
     */
    @NotBlank(message = "{Valid.englishName}")
    private String englishName;

    /**
     * 中文名字
     */
    @NotBlank(message = "{Valid.chineseName}")
    private String chineseName;

    /**
     * 出生日期
     */
    @NotBlank(message = "{Valid.birthday}")
    private String birthday;

    /**
     * 邮箱
     */
    @NotBlank(message = "{Valid.email}")
    private String email;

    /**
     * 电话
     */
    @NotBlank(message = "{Valid.phone}")
    private String phone;

    /**
     * 证件类型
     */
    @NotBlank(message = "{Valid.documentsType}")
    private String documentsType;

    /**
     * 证件号
     */
    @NotBlank(message = "{Valid.idNumber}")
    private String idNumber;

    /**
     * 证件有效期开始日期
     */
    @NotBlank(message = "{Valid.dateOfIssue}")
    private String dateOfIssue;

    /**
     * 详细地址
     */
//    @NotBlank(message = "{Valid.mailingAddress}")
    private String mailingAddress;

    /**
     * 证件有效期结束日期
     */
    @NotBlank(message = "{Valid.dateOfIssue}")
    private String dateOfExpiry;


    //    @NotNull(message = "{Valid.status}")
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 国籍
     */
    @NotBlank(message = "{Valid.nationality}")
    private String nationality;

    /**
     * 区号
     */
    @NotBlank(message = "{Valid.nationCode}")
    private String nationCode;

    /**
     * 性别
     */
    @NotBlank(message = "{Valid.sex}")
    private String sex;

    /**
     * 职业
     */
    @NotBlank(message = "{Valid.profession}")
    private String profession;

    /**
     * 职位
     */
    @NotBlank(message = "{Valid.posts}")
    private String posts;

    /**
     * 开户目的
     */
    @NotBlank(message = "{Valid.purpose}")
    private String purpose;

    /**
     * 证件图片
     */
    @NotBlank(message = "{Valid.certificateImage}")
    private String certificateImage;

    @NotBlank(message = "{Valid.signatureImage}")
    private String signatureImage;
    /**
     * 状态: 1=實名登記/3=實名認證
     */
    private Integer realNameStatus;


    private static final long serialVersionUID = 1L;

    /**
     * 上游id
     */
    private String upuid;

}