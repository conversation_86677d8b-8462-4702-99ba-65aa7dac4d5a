package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_card_holder")
public class CardHolder {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 所属商户
     */
    @Column(value = "user_id")
    private Integer userId;

    /**
     * 商户id
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 英文名字/拼音
     */
    @Column(value = "english_name")
    private String englishName;

    /**
     * 中文名字
     */
    @Column(value = "chinese_name")
    private String chineseName;

    /**
     * 出生日期
     */
    @Column(value = "birthday")
    private String birthday;

    /**
     * 邮箱
     */
    @Column(value = "email")
    private String email;

    /**
     * 电话
     */
    @Column(value = "phone")
    private String phone;

    /**
     * 证件类型
     */
    @Column(value = "documents_type")
    private String documentsType;

    /**
     * 证件号
     */
    @Column(value = "id_number")
    private String idNumber;

    /**
     * 证件有效期开始日期
     */
    @Column(value = "date_of_Issue")
    private String dateOfIssue;

    /**
     * 详细地址
     */
    @Column(value = "mailing_address")
    private String mailingAddress;

    /**
     * 证件有效期结束日期
     */
    @Column(value = "date_of_expiry")
    private String dateOfExpiry;

    /**
     * 0未审核/ 1审核通过/ 2 已驳回
     */
    @Column(value = "status")
    private Integer status;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 国籍
     */
    @Column(value = "nationality")
    private String nationality;

    /**
     * 区号
     */
    @Column(value = "nation_code")
    private String nationCode;

    /**
     * 性别：M 男/F 女
     */
    @Column(value = "sex")
    private Object sex;

    /**
     * 职业
     */
    @Column(value = "profession")
    private String profession;

    /**
     * 职位
     */
    @Column(value = "posts")
    private String posts;

    /**
     * 开户目的
     */
    @Column(value = "purpose")
    private String purpose;

    /**
     * 实名状态
     */
    @Column(value = "real_name_status")
    private Integer realNameStatus;

    /**
     * 证件照片
     */
    @Column(value = "certificate_image")
    private String certificateImage;

    /**
     * 签名图片
     */
    @Column(value = "signature_image")
    private String signatureImage;


}
