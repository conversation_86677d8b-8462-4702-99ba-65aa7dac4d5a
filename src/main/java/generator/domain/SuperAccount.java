package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_super_account")
public class SuperAccount {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    @Column(value = "email")
    private String email;

    @Column(value = "password")
    private String password;

    @Column(value = "code")
    private String code;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "expire_time")
    private Date expireTime;

    @Column(value = "created_by")
    private String createdBy;


}
