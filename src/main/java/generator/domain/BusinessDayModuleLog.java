package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Object;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_business_day_module_log")
public class BusinessDayModuleLog {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户 ID
     */
    @Column(value = "business_id")
    private String businessId;

    /**
     * 统计日期
     */
    @Column(value = "stat_date")
    private Date statDate;

    /**
     * 统计模块
     */
    @Column(value = "wallet_type")
    private Object walletType;

    /**
     * 划入
     */
    @Column(value = "transfer_in")
    private BigDecimal transferIn;

    /**
     * 划出
     */
    @Column(value = "transfer_out")
    private BigDecimal transferOut;

    /**
     * 消费
     */
    @Column(value = "consume")
    private BigDecimal consume;

    /**
     * 支出
     */
    @Column(value = "disburse")
    private BigDecimal disburse;

    /**
     * 商户收入
     */
    @Column(value = "business_earn")
    private BigDecimal businessEarn;

    /**
     * 平台收入
     */
    @Column(value = "platform_earn")
    private BigDecimal platformEarn;


}
