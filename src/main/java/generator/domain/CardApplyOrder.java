package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_card_apply_order")
public class CardApplyOrder {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 商户 ID
     */
    @Column(value = "business_id")
    private String businessId;

    @Column(value = "order_id")
    private String orderId;

    @Column(value = "card_id")
    private String cardId;

    @Column(value = "card_number")
    private String cardNumber;

    /**
     * 1 开卡 2 充值 3 冻结 4 解冻 5 挂失 6 解挂 5 换卡 6 销卡
     */
    @Column(value = "action")
    private Integer action;

    /**
     * 金额
     */
    @Column(value = "amount")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @Column(value = "fee")
    private BigDecimal fee;

    /**
     * 0 待处理 1 处理中 2 驳回请求 3 处理成功 4 处理失败
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @Column(value = "note")
    private String note;

    /**
     * 渠道
     */
    @Column(value = "channel")
    private String channel;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;


}
