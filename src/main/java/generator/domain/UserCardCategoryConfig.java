package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qf.zpay.constants.CardChannelEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName tb_user_card_category_config
 */
@TableName(value = "tb_user_card_category_config")
@Data
public class UserCardCategoryConfig implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 卡来源渠道
     */
    private String source;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支持的产品模式【Standard:标准卡,ShareBalance:共享卡】
     */
    private String cardModel;

    /**
     * 支持的结算币种
     */
    private String cardCurrency;

    /**
     *
     */
    private String cardScheme;

    /**
     *
     */
    private CardChannelEnum channel;


    /**
     * 状态：0不可用，1可用
     */
    private Integer state;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserCardCategoryConfig other = (UserCardCategoryConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
                && (this.getProductCode() == null ? other.getProductCode() == null : this.getProductCode().equals(other.getProductCode()))
                && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
                && (this.getCardModel() == null ? other.getCardModel() == null : this.getCardModel().equals(other.getCardModel()))
                && (this.getCardCurrency() == null ? other.getCardCurrency() == null : this.getCardCurrency().equals(other.getCardCurrency()))
                && (this.getCardScheme() == null ? other.getCardScheme() == null : this.getCardScheme().equals(other.getCardScheme()))
                && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getProductCode() == null) ? 0 : getProductCode().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getCardModel() == null) ? 0 : getCardModel().hashCode());
        result = prime * result + ((getCardCurrency() == null) ? 0 : getCardCurrency().hashCode());
        result = prime * result + ((getCardScheme() == null) ? 0 : getCardScheme().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", source=").append(source);
        sb.append(", productCode=").append(productCode);
        sb.append(", productName=").append(productName);
        sb.append(", cardModel=").append(cardModel);
        sb.append(", cardCurrency=").append(cardCurrency);
        sb.append(", cardScheme=").append(cardScheme);
        sb.append(", channel=").append(channel);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}