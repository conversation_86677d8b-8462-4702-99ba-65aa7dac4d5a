package generator.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.lang.Object;
import java.lang.String;
import java.lang.Integer;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Table(value = "tb_user_card_category_config")
public class UserCardCategoryConfig {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 卡来源渠道
     */
    @Column(value = "source")
    private String source;

    /**
     * 产品编码
     */
    @Column(value = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(value = "product_name")
    private String productName;

    /**
     * 支持的产品模式【Standard:标准卡,ShareBalance:共享卡】
     */
    @Column(value = "card_model")
    private String cardModel;

    /**
     * 支持的结算币种
     */
    @Column(value = "card_currency")
    private String cardCurrency;

    @Column(value = "card_scheme")
    private String cardScheme;

    @Column(value = "channel")
    private Object channel;


}
