<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>仪表盘</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/babel-standalone"></script>
    <script src="https://unpkg.com/dayjs"></script>
    <script src="https://unpkg.com/axios"></script>
    <!--    <script src="https://unpkg.com/antd"></script>-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/antd/5.23.3/antd.min.js"></script>
    <script src="https://unpkg.com/@ant-design/icons"></script>
</head>
<body>
<div id="root"></div>
<script type="text/babel">
    const {useEffect, useState} = React;
    const {Button, Flex, Rate, Card, Col, Row, Statistic} = antd;

    const {LikeOutlined} = '@ant-design/icons';


    function App() {
        const [card, setCard] = useState({fund: {}, num: {}});
        const [fund, setFund] = useState({});
        const [tvl, setTvl] = useState({});


        useEffect(async () => {
            const res = await axios.get('/stat/dashboard');
            const stat = res.data.data;
            setCard({fund: stat.cardFund, num: stat.cardNum});
            setFund(stat.fund);
            setTvl(stat.tvl);
            // console.error(card);
        }, []);

        return (
            <div>
                <Card title="充提统计">
                    <Row gutter={8}>
                        <Col span={6}>
                            <Statistic title="今日充值" value={fund.todayDeposit}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="今日提现" value={fund.todayWithdraw}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="今日提现手续费收入" value={fund.todayWithdrawFee}/>
                        </Col>
                    </Row>
                    <Row gutter={8} style={{marginTop: 16}}>
                        <Col span={6}>
                            <Statistic title="历史充值" value={fund.historyDeposit}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="历史提现" value={fund.historyWithdraw}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="历史提现手续费收入" value={fund.historyWithdrawFee}/>
                        </Col>
                    </Row>
                </Card>
                <Card title="平台资金" style={{marginTop: 16}}>
                    <Row gutter={8}>
                        <Col span={6}>
                            <Statistic title="主钱包" value={tvl.mainTvl}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="共享卡钱包" value={tvl.shareTvl}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="储值卡钱包" value={tvl.storeTvl}/>
                        </Col>
                    </Row>
                    <Row gutter={8} style={{marginTop: 16}}>
                        <Col span={6}>
                            <Statistic title="实体卡钱包" value={tvl.physicalTvl}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="代付钱包" value={tvl.paymentTvl}/>
                        </Col>
                        <Col span={6}>
                            <Statistic title="USDT钱包" value={tvl.usdtTvl}/>
                        </Col>
                    </Row>
                </Card>
                <Card title="卡片业务" style={{marginTop: 16}}>
                    <Card type="inner" size={"small"} title="今日数据">
                        <Row gutter={8}>
                            <Col span={6}>
                                <Statistic title="今日开卡" value={card.fund.openCardNum}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日充值" value={card.fund.rechargeNum}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日销卡" value={card.fund.cancelCardNum}/>
                            </Col>
                        </Row>
                    </Card>
                    <Card type="inner" size={"small"} style={{marginTop: 16}} title="历史数据">
                        <Row gutter={8}>
                            <Col span={6}>
                                <Statistic title="储值卡" value={card.num.rechargeCardNum}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="共享卡" value={card.num.shareCardNum}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="实体卡" value={card.num.physicalCardNum}/>
                            </Col>
                        </Row>
                        <Row gutter={8} style={{marginTop: 16}}>
                            <Col span={6}>
                                <Statistic title="活跃卡片数量" value={card.num.activeCardNum}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="全部卡片数量" value={card.num.totalCardNum}/>
                            </Col>
                        </Row>
                        <Row gutter={8} style={{marginTop: 16}}>
                            <Col span={6}>
                                <Statistic title="活跃卡片余额" value={card.num.activeCardAmount}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="全部卡片余额" value={card.num.totalCardAmount}/>
                            </Col>
                        </Row>
                    </Card>
                    <Card type="inner" size={"small"} style={{marginTop: 16}} title="金融统计">
                        <Row gutter={8}>
                            <Col span={6}>
                                <Statistic title="今日开卡费成本" value={card.fund.openCardFeeCost}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日开卡费" value={card.fund.openCardFeeBusiness}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日开卡费收入" value={card.fund.openCardFeePlatformEarn}/>
                            </Col>
                        </Row>
                        <Row gutter={8} style={{marginTop: 16}}>
                            <Col span={6}>
                                <Statistic title="今日充值费成本" value={card.fund.rechargeFeeCost}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日充值费" value={card.fund.rechargeFeeBusiness}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日充值费收入" value={card.fund.rechargeFeePlatformEarn}/>
                            </Col>
                        </Row>
                        <Row gutter={8} style={{marginTop: 16}}>
                            <Col span={6}>
                                <Statistic title="今日销卡费成本" value={card.fund.cancelCardFeeCost}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日销卡费" value={card.fund.cancelCardFeeBusiness}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日销卡费收入" value={card.fund.cancelCardFeePlatformEarn}/>
                            </Col>
                        </Row>
                        <Row gutter={8} style={{marginTop: 16}}>
                            <Col span={6}>
                                <Statistic title="今日成本费" value={card.fund.platformDayDisburse}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="今日收入" value={card.fund.platformDayEarn}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="历史成本费" value={card.fund.platformHistoryDisburse}/>
                            </Col>
                            <Col span={6}>
                                <Statistic title="历史收入" value={card.fund.platformHistoryEarn}/>
                            </Col>
                        </Row>
                    </Card>
                </Card>
                <Card title="代付业务" style={{marginTop: 16}}>
                </Card>
            </div>
        );
    }

    const container = document.getElementById("root");
    const root = ReactDOM.createRoot(container);
    root.render(<App/>);
</script>
</body>
</html>