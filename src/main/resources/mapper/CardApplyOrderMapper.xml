<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CardApplyOrderMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CardApplyOrder">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="order_id" jdbcType="CHAR" property="orderId"/>
        <result column="card_id" jdbcType="VARCHAR" property="cardId"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="action" jdbcType="OTHER" property="action"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="status" jdbcType="OTHER" property="status"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `business_id`, `order_id`, `card_id`, `card_number`, `action`, `amount`, `fee`, `status`, `note`,
        `channel`, `create_time`, `update_time`
    </sql>

</mapper>