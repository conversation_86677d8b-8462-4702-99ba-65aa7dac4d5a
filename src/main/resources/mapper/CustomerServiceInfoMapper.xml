<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CustomerServiceInfoMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CustomerServiceInfo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="whats_app" jdbcType="VARCHAR" property="whatsApp"/>
        <result column="we_chat" jdbcType="VARCHAR" property="weChat"/>
        <result column="telegram" jdbcType="VARCHAR" property="telegram"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `name`, `whats_app`, `we_chat`, `telegram`, `create_time`
    </sql>

</mapper>