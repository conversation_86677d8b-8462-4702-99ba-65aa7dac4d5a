<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PaymentPayConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PaymentPayConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="two_letter_code" jdbcType="VARCHAR" property="twoLetterCode"/>
        <result column="country_region" jdbcType="VARCHAR" property="countryRegion"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="payment_product" jdbcType="VARCHAR" property="paymentProduct"/>
        <result column="local_market_position" jdbcType="CLOB" property="localMarketPosition"/>
        <result column="transaction_currency" jdbcType="VARCHAR" property="transactionCurrency"/>
        <result column="settlement_currency" jdbcType="VARCHAR" property="settlementCurrency"/>
        <result column="total_fee" jdbcType="DECIMAL" property="totalFee"/>
        <result column="fee_rate" jdbcType="DECIMAL" property="feeRate"/>
        <result column="settlement_cycle" jdbcType="INTEGER" property="settlementCycle"/>
        <result column="fx" jdbcType="VARCHAR" property="fx"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="english_name_country" jdbcType="VARCHAR" property="englishNameCountry"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `two_letter_code`, `country_region`, `product_type`, `payment_product`, `local_market_position`,
        `transaction_currency`, `settlement_currency`, `total_fee`, `fee_rate`, `settlement_cycle`, `fx`, `channel`,
        `create_time`, `update_time`, `status`, `english_name_country`
    </sql>

</mapper>