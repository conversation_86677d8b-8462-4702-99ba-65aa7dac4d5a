<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.AppConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.AppConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `key`, `value`, `remark`, `create_at`, `update_time`
    </sql>

</mapper>