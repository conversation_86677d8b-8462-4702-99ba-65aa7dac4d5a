<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.MailingAddressMapper">
    <resultMap id="BaseResultMap" type="generator.domain.MailingAddress">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="holder_id" jdbcType="INTEGER" property="holderId"/>
        <result column="card_id" jdbcType="VARCHAR" property="cardId"/>
        <result column="postcode" jdbcType="VARCHAR" property="postcode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="nation_code" jdbcType="VARCHAR" property="nationCode"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `holder_id`, `card_id`, `postcode`, `address`, `nation_code`, `country`, `create_time`, `update_time`,
        `phone`, `user_name`
    </sql>

</mapper>