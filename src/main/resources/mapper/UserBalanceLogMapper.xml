<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserBalanceLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserBalanceLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="before_money" jdbcType="DECIMAL" property="beforeMoney"/>
        <result column="money" jdbcType="DECIMAL" property="money"/>
        <result column="after_money" jdbcType="DECIMAL" property="afterMoney"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="sub_type" jdbcType="INTEGER" property="subType"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `order_id`, `business_id`, `before_money`, `money`, `after_money`, `type`, `sub_type`, `note`,
        `create_time`, `create_date`
    </sql>

</mapper>