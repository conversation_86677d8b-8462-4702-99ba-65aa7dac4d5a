<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="generator.domain.User">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="salt" jdbcType="VARCHAR" property="salt"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="pay_password" jdbcType="VARCHAR" property="payPassword"/>
        <result column="money" jdbcType="DECIMAL" property="money"/>
        <result column="share_balance" jdbcType="DECIMAL" property="shareBalance"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="business_name" jdbcType="VARCHAR" property="businessName"/>
        <result column="business_no" jdbcType="VARCHAR" property="businessNo"/>
        <result column="business_status" jdbcType="TINYINT" property="businessStatus"/>
        <result column="withdraw_rate" jdbcType="DECIMAL" property="withdrawRate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="last_login_ip" jdbcType="VARCHAR" property="lastLoginIp"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`
        , `email`, `mobile`, `salt`, `password`, `pay_password`, `money`, `share_balance`, `status`, `business_id`,
        `business_name`, `business_no`, `business_status`, `withdraw_rate`, `create_time`, `update_time`,
        `last_login_time`, `last_login_ip`
    </sql>

</mapper>