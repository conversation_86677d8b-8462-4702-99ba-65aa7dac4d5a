<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PaymentPayOrderMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PaymentPayOrder">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="payee_id" jdbcType="INTEGER" property="payeeId"/>
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="amount_received" jdbcType="DECIMAL" property="amountReceived"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="total_fee" jdbcType="DECIMAL" property="totalFee"/>
        <result column="payment_rate" jdbcType="DECIMAL" property="paymentRate"/>
        <result column="cost_total_money" jdbcType="DECIMAL" property="costTotalMoney"/>
        <result column="cost_fee" jdbcType="DECIMAL" property="costFee"/>
        <result column="payer" jdbcType="VARCHAR" property="payer"/>
        <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="failure_reason" jdbcType="VARCHAR" property="failureReason"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime"/>
        <result column="quote_id" jdbcType="VARCHAR" property="quoteId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="upstream_order_id" jdbcType="VARCHAR" property="upstreamOrderId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="cost_payment_rate" jdbcType="DECIMAL" property="costPaymentRate"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `order_id`, `payee_id`, `payment_method`, `amount_received`, `payment_amount`, `fee`, `total_fee`,
        `payment_rate`, `cost_total_money`, `cost_fee`, `payer`, `payment_purpose`, `status`, `failure_reason`,
        `create_time`, `payment_time`, `completion_time`, `quote_id`, `business_id`, `upstream_order_id`, `remark`,
        `cost_payment_rate`
    </sql>

</mapper>