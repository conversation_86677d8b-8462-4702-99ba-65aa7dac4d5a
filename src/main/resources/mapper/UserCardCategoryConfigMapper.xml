<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserCardCategoryConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserCardCategoryConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="card_model" jdbcType="VARCHAR" property="cardModel"/>
        <result column="card_currency" jdbcType="VARCHAR" property="cardCurrency"/>
        <result column="card_scheme" jdbcType="VARCHAR" property="cardScheme"/>
        <result column="channel" jdbcType="OTHER" property="channel"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `source`, `product_code`, `product_name`, `card_model`, `card_currency`, `card_scheme`, `channel`
    </sql>

</mapper>