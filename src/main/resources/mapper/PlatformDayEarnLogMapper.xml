<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PlatformDayEarnLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PlatformDayEarnLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="open_card_fee_total" jdbcType="DECIMAL" property="openCardFeeTotal"/>
        <result column="open_card_fee_earn" jdbcType="DECIMAL" property="openCardFeeEarn"/>
        <result column="recharge_fee_total" jdbcType="DECIMAL" property="rechargeFeeTotal"/>
        <result column="recharge_fee_earn" jdbcType="DECIMAL" property="rechargeFeeEarn"/>
        <result column="cancel_card_fee_total" jdbcType="DECIMAL" property="cancelCardFeeTotal"/>
        <result column="cancel_card_fee_earn" jdbcType="DECIMAL" property="cancelCardFeeEarn"/>
        <result column="day_earn" jdbcType="DECIMAL" property="dayEarn"/>
        <result column="history_earn" jdbcType="DECIMAL" property="historyEarn"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `open_card_fee_total`, `open_card_fee_earn`, `recharge_fee_total`, `recharge_fee_earn`,
        `cancel_card_fee_total`, `cancel_card_fee_earn`, `day_earn`, `history_earn`, `stat_date`, `create_time`
    </sql>

</mapper>