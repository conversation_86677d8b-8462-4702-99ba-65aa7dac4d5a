<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserCardMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserCard">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uid" jdbcType="INTEGER" property="uid"/>
        <result column="uid_child" jdbcType="INTEGER" property="uidChild"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="virtual_amt" jdbcType="DECIMAL" property="virtualAmt"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="card_model" jdbcType="VARCHAR" property="cardModel"/>
        <result column="card_currency" jdbcType="VARCHAR" property="cardCurrency"/>
        <result column="card_amt" jdbcType="VARCHAR" property="cardAmt"/>
        <result column="card_total_amt" jdbcType="VARCHAR" property="cardTotalAmt"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="card_id" jdbcType="VARCHAR" property="cardId"/>
        <result column="card_expiration_mmyy" jdbcType="VARCHAR" property="cardExpirationMmyy"/>
        <result column="card_cvv" jdbcType="VARCHAR" property="cardCvv"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="card_status" jdbcType="VARCHAR" property="cardStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="open_card_time" jdbcType="TIMESTAMP" property="openCardTime"/>
        <result column="open_card_date" jdbcType="DATE" property="openCardDate"/>
        <result column="cancel_card_time" jdbcType="TIMESTAMP" property="cancelCardTime"/>
        <result column="cancel_card_date" jdbcType="DATE" property="cancelCardDate"/>
        <result column="channel" jdbcType="OTHER" property="channel"/>
        <result column="card_manage_id" jdbcType="INTEGER" property="cardManageId"/>
        <result column="card_nickname" jdbcType="VARCHAR" property="cardNickname"/>
        <result column="holder_id" jdbcType="OTHER" property="holderId"/>
        <result column="card_scheme" jdbcType="OTHER" property="cardScheme"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `uid`, `uid_child`, `amount`, `virtual_amt`, `product_code`, `card_model`, `card_currency`, `card_amt`,
        `card_total_amt`, `order_no`, `card_id`, `card_expiration_mmyy`, `card_cvv`, `card_number`, `card_status`,
        `create_time`, `update_time`, `apply_time`, `status`, `open_card_time`, `open_card_date`, `cancel_card_time`,
        `cancel_card_date`, `channel`, `card_manage_id`, `card_nickname`, `holder_id`, `card_scheme`
    </sql>

</mapper>