<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.RechargeMapper">
    <resultMap id="BaseResultMap" type="generator.domain.Recharge">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="coin_type" jdbcType="TINYINT" property="coinType"/>
        <result column="from_address" jdbcType="CHAR" property="fromAddress"/>
        <result column="tron_private_key" jdbcType="CHAR" property="tronPrivateKey"/>
        <result column="is_status" jdbcType="OTHER" property="isStatus"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="hash" jdbcType="VARCHAR" property="hash"/>
        <result column="charge_hash" jdbcType="VARCHAR" property="chargeHash"/>
        <result column="deal_hash" jdbcType="VARCHAR" property="dealHash"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_error" jdbcType="INTEGER" property="isError"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `coin_type`, `from_address`, `tron_private_key`, `is_status`, `amount`, `hash`, `charge_hash`,
        `deal_hash`, `create_time`, `update_time`, `is_error`
    </sql>

</mapper>