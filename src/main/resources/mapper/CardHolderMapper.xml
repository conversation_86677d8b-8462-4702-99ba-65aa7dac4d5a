<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CardHolderMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CardHolder">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="english_name" jdbcType="VARCHAR" property="englishName"/>
        <result column="chinese_name" jdbcType="VARCHAR" property="chineseName"/>
        <result column="birthday" jdbcType="VARCHAR" property="birthday"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="documents_type" jdbcType="VARCHAR" property="documentsType"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="date_of_Issue" jdbcType="VARCHAR" property="dateOfIssue"/>
        <result column="mailing_address" jdbcType="VARCHAR" property="mailingAddress"/>
        <result column="date_of_expiry" jdbcType="VARCHAR" property="dateOfExpiry"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="nationality" jdbcType="VARCHAR" property="nationality"/>
        <result column="nation_code" jdbcType="VARCHAR" property="nationCode"/>
        <result column="sex" jdbcType="OTHER" property="sex"/>
        <result column="profession" jdbcType="VARCHAR" property="profession"/>
        <result column="posts" jdbcType="VARCHAR" property="posts"/>
        <result column="purpose" jdbcType="VARCHAR" property="purpose"/>
        <result column="real_name_status" jdbcType="INTEGER" property="realNameStatus"/>
        <result column="certificate_image" jdbcType="VARCHAR" property="certificateImage"/>
        <result column="signature_image" jdbcType="VARCHAR" property="signatureImage"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `business_id`, `english_name`, `chinese_name`, `birthday`, `email`, `phone`, `documents_type`,
        `id_number`, `date_of_Issue`, `mailing_address`, `date_of_expiry`, `status`, `create_time`, `update_time`,
        `nationality`, `nation_code`, `sex`, `profession`, `posts`, `purpose`, `real_name_status`, `certificate_image`,
        `signature_image`
    </sql>

</mapper>