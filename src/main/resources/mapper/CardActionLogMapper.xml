<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CardActionLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CardActionLog">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="type" jdbcType="OTHER" property="type"/>
        <result column="money" jdbcType="DECIMAL" property="money"/>
        <result column="card_id" jdbcType="VARCHAR" property="cardId"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="cost_fee" jdbcType="DECIMAL" property="costFee"/>
        <result column="cost_rate" jdbcType="DECIMAL" property="costRate"/>
        <result column="platform_fee" jdbcType="DECIMAL" property="platformFee"/>
        <result column="platform_rate" jdbcType="DECIMAL" property="platformRate"/>
        <result column="business_fee" jdbcType="DECIMAL" property="businessFee"/>
        <result column="business_rate" jdbcType="DECIMAL" property="businessRate"/>
        <result column="platform_earn" jdbcType="DECIMAL" property="platformEarn"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="business_earn" jdbcType="DECIMAL" property="businessEarn"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="card_model" jdbcType="VARCHAR" property="cardModel"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `type`, `money`, `card_id`, `business_id`, `cost_fee`, `cost_rate`, `platform_fee`, `platform_rate`,
        `business_fee`, `business_rate`, `platform_earn`, `order_id`, `business_earn`, `create_time`, `create_date`,
        `status`, `note`, `card_model`
    </sql>

</mapper>