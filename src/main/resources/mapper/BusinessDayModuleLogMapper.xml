<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.BusinessDayModuleLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.BusinessDayModuleLog">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="wallet_type" jdbcType="OTHER" property="walletType"/>
        <result column="transfer_in" jdbcType="DECIMAL" property="transferIn"/>
        <result column="transfer_out" jdbcType="DECIMAL" property="transferOut"/>
        <result column="consume" jdbcType="DECIMAL" property="consume"/>
        <result column="disburse" jdbcType="DECIMAL" property="disburse"/>
        <result column="business_earn" jdbcType="DECIMAL" property="businessEarn"/>
        <result column="platform_earn" jdbcType="DECIMAL" property="platformEarn"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `business_id`, `stat_date`, `wallet_type`, `transfer_in`, `transfer_out`, `consume`, `disburse`,
        `business_earn`, `platform_earn`
    </sql>

</mapper>