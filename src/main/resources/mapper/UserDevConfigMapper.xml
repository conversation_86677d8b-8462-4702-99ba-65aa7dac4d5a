<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserDevConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserDevConfig">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="user_id" jdbcType="OTHER" property="userId"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="app_id" jdbcType="CHAR" property="appId"/>
        <result column="app_secret" jdbcType="CHAR" property="appSecret"/>
        <result column="cb_url" jdbcType="VARCHAR" property="cbUrl"/>
        <result column="ip_whitelist" jdbcType="OTHER" property="ipWhitelist"/>
        <result column="enable_module" jdbcType="OTHER" property="enableModule"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `business_id`, `app_id`, `app_secret`, `cb_url`, `ip_whitelist`, `enable_module`,
        `create_time`, `update_time`
    </sql>

</mapper>