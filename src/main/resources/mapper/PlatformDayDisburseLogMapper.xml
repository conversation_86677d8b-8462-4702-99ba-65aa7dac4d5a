<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PlatformDayDisburseLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PlatformDayDisburseLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="open_card_fee" jdbcType="DECIMAL" property="openCardFee"/>
        <result column="recharge_fee" jdbcType="DECIMAL" property="rechargeFee"/>
        <result column="cancel_card_fee" jdbcType="DECIMAL" property="cancelCardFee"/>
        <result column="day_disburse" jdbcType="DECIMAL" property="dayDisburse"/>
        <result column="history_disburse" jdbcType="DECIMAL" property="historyDisburse"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `open_card_fee`, `recharge_fee`, `cancel_card_fee`, `day_disburse`, `history_disburse`, `stat_date`,
        `create_time`
    </sql>

</mapper>