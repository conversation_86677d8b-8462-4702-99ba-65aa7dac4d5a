<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PaymentPayPayeeMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PaymentPayPayee">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="payee_id" jdbcType="VARCHAR" property="payeeId"/>
        <result column="subject_type" jdbcType="VARCHAR" property="subjectType"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
        <result column="account_holder" jdbcType="VARCHAR" property="accountHolder"/>
        <result column="nation_code" jdbcType="VARCHAR" property="nationCode"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="pay_config_id" jdbcType="INTEGER" property="payConfigId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose"/>
        <result column="two_letter_code" jdbcType="VARCHAR" property="twoLetterCode"/>
        <result column="english_name_country" jdbcType="VARCHAR" property="englishNameCountry"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `payee_id`, `subject_type`, `country`, `currency`, `first_name`, `last_name`, `account_holder`,
        `nation_code`, `phone`, `address`, `create_time`, `pay_config_id`, `business_id`, `payment_purpose`,
        `two_letter_code`, `english_name_country`, `status`
    </sql>

</mapper>