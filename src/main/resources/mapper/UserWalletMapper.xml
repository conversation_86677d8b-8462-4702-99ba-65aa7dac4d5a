<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserWalletMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserWallet">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="user_id" jdbcType="OTHER" property="userId"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="main_wallet" jdbcType="DECIMAL" property="mainWallet"/>
        <result column="store_wallet" jdbcType="DECIMAL" property="storeWallet"/>
        <result column="share_wallet" jdbcType="DECIMAL" property="shareWallet"/>
        <result column="physical_wallet" jdbcType="DECIMAL" property="physicalWallet"/>
        <result column="payment_wallet" jdbcType="DECIMAL" property="paymentWallet"/>
        <result column="usdt_wallet" jdbcType="DECIMAL" property="usdtWallet"/>
        <result column="token_wallet" jdbcType="DECIMAL" property="tokenWallet"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `business_id`, `main_wallet`, `store_wallet`, `share_wallet`, `physical_wallet`,
        `payment_wallet`, `usdt_wallet`, `token_wallet`
    </sql>

</mapper>