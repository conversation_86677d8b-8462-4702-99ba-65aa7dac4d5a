<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.SuperAccountMapper">
    <resultMap id="BaseResultMap" type="generator.domain.SuperAccount">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="password" jdbcType="CHAR" property="password"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `email`, `password`, `code`, `create_time`, `expire_time`, `created_by`
    </sql>

</mapper>