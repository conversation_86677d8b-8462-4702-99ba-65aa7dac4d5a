<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserCardLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserCardLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="child_user_id" jdbcType="INTEGER" property="childUserId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="notice_type" jdbcType="VARCHAR" property="noticeType"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="auth_time" jdbcType="VARCHAR" property="authTime"/>
        <result column="trans_amount_currency" jdbcType="VARCHAR" property="transAmountCurrency"/>
        <result column="trans_amount" jdbcType="DECIMAL" property="transAmount"/>
        <result column="auth_amount_currency" jdbcType="VARCHAR" property="authAmountCurrency"/>
        <result column="auth_amount" jdbcType="DECIMAL" property="authAmount"/>
        <result column="settled_amount" jdbcType="DECIMAL" property="settledAmount"/>
        <result column="card_id" jdbcType="VARCHAR" property="cardId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="mask_card_number" jdbcType="VARCHAR" property="maskCardNumber"/>
        <result column="card_model" jdbcType="VARCHAR" property="cardModel"/>
        <result column="card_alias" jdbcType="VARCHAR" property="cardAlias"/>
        <result column="merchant_name" jdbcType="VARCHAR" property="merchantName"/>
        <result column="merchant_country_code" jdbcType="VARCHAR" property="merchantCountryCode"/>
        <result column="merchant_city" jdbcType="VARCHAR" property="merchantCity"/>
        <result column="merchant_state" jdbcType="VARCHAR" property="merchantState"/>
        <result column="merchant_zip_code" jdbcType="VARCHAR" property="merchantZipCode"/>
        <result column="merchant_desc" jdbcType="VARCHAR" property="merchantDesc"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="funds_direction" jdbcType="VARCHAR" property="fundsDirection"/>
        <result column="transaction_type" jdbcType="VARCHAR" property="transactionType"/>
        <result column="failure_reason" jdbcType="VARCHAR" property="failureReason"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="available_credit" jdbcType="DECIMAL" property="availableCredit"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `child_user_id`, `business_id`, `notice_type`, `order_id`, `auth_time`, `trans_amount_currency`,
        `trans_amount`, `auth_amount_currency`, `auth_amount`, `settled_amount`, `card_id`, `product_code`,
        `product_name`, `mask_card_number`, `card_model`, `card_alias`, `merchant_name`, `merchant_country_code`,
        `merchant_city`, `merchant_state`, `merchant_zip_code`, `merchant_desc`, `status`, `funds_direction`,
        `transaction_type`, `failure_reason`, `note`, `create_time`, `create_date`, `available_credit`
    </sql>

</mapper>