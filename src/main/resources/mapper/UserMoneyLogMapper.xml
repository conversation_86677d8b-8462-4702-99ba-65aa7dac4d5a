<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserMoneyLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserMoneyLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="transfer_hash" jdbcType="VARCHAR" property="transferHash"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="coin_type" jdbcType="INTEGER" property="coinType"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="before_money" jdbcType="DECIMAL" property="beforeMoney"/>
        <result column="money" jdbcType="DECIMAL" property="money"/>
        <result column="after_money" jdbcType="DECIMAL" property="afterMoney"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="sub_type" jdbcType="INTEGER" property="subType"/>
        <result column="money_type" jdbcType="VARCHAR" property="moneyType"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="check_status" jdbcType="INTEGER" property="checkStatus"/>
        <result column="check_admin" jdbcType="VARCHAR" property="checkAdmin"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="chain_type" jdbcType="INTEGER" property="chainType"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="withdrawal_charge_rate" jdbcType="DECIMAL" property="withdrawalChargeRate"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`
        , `order_id`, `coin_type`, `user_id`, `business_id`, `before_money`, `money`, `after_money`, `type`,
        `sub_type`, `money_type`, `note`, `create_time`, `create_date`, `check_status`, `check_admin`, `check_time`,
        `status`, `transfer_hash`, `chain_type`, `address`, `withdrawal_charge_rate`
    </sql>

</mapper>