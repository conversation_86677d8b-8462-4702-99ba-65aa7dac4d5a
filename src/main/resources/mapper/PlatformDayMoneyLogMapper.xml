<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PlatformDayMoneyLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PlatformDayMoneyLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="total_recharge" jdbcType="DECIMAL" property="totalRecharge"/>
        <result column="total_withdraw" jdbcType="DECIMAL" property="totalWithdraw"/>
        <result column="total_earn" jdbcType="DECIMAL" property="totalEarn"/>
        <result column="total_balance" jdbcType="DECIMAL" property="totalBalance"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `total_recharge`, `total_withdraw`, `total_earn`, `total_balance`, `stat_date`, `create_time`
    </sql>

</mapper>