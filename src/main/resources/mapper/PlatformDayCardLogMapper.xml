<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PlatformDayCardLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PlatformDayCardLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="open_card_num" jdbcType="INTEGER" property="openCardNum"/>
        <result column="cancel_card_num" jdbcType="INTEGER" property="cancelCardNum"/>
        <result column="have_card_num" jdbcType="INTEGER" property="haveCardNum"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `open_card_num`, `cancel_card_num`, `have_card_num`, `stat_date`, `create_time`
    </sql>

</mapper>