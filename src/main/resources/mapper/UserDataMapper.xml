<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserDataMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="platform_merchants" jdbcType="INTEGER" property="platformMerchants"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="mailing_address" jdbcType="VARCHAR" property="mailingAddress"/>
        <result column="id_number" jdbcType="INTEGER" property="idNumber"/>
        <result column="id_image" jdbcType="VARCHAR" property="idImage"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `business_id`, `platform_merchants`, `user_name`, `country`, `phone`, `mailing_address`,
        `id_number`, `id_image`, `status`, `create_time`, `update_time`
    </sql>

</mapper>