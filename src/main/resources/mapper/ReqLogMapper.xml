<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.ReqLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.ReqLog">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="req_time" jdbcType="TIMESTAMP" property="reqTime"/>
        <result column="req_url" jdbcType="VARCHAR" property="reqUrl"/>
        <result column="req_body" jdbcType="CLOB" property="reqBody"/>
        <result column="req_header" jdbcType="CLOB" property="reqHeader"/>
        <result column="req_method" jdbcType="VARCHAR" property="reqMethod"/>
        <result column="res_time" jdbcType="TIMESTAMP" property="resTime"/>
        <result column="res_body" jdbcType="CLOB" property="resBody"/>
        <result column="res_header" jdbcType="CLOB" property="resHeader"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `channel`, `req_time`, `req_url`, `req_body`, `req_header`, `req_method`, `res_time`, `res_body`,
        `res_header`, `status`
    </sql>

</mapper>