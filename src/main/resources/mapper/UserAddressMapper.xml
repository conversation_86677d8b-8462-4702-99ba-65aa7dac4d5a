<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserAddressMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserAddress">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="erc_address" jdbcType="VARCHAR" property="ercAddress"/>
        <result column="erc_prikey" jdbcType="VARCHAR" property="ercPrikey"/>
        <result column="erc_word" jdbcType="VARCHAR" property="ercWord"/>
        <result column="bep_address" jdbcType="VARCHAR" property="bepAddress"/>
        <result column="bep_prikey" jdbcType="VARCHAR" property="bepPrikey"/>
        <result column="bep_word" jdbcType="VARCHAR" property="bepWord"/>
        <result column="trc_address" jdbcType="VARCHAR" property="trcAddress"/>
        <result column="trc_prikey" jdbcType="VARCHAR" property="trcPrikey"/>
        <result column="trc_word" jdbcType="VARCHAR" property="trcWord"/>
        <result column="trc_hexAddress" jdbcType="VARCHAR" property="trcHexAddress"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `erc_address`, `erc_prikey`, `erc_word`, `bep_address`, `bep_prikey`, `bep_word`,
        `trc_address`, `trc_prikey`, `trc_word`, `trc_hexAddress`, `create_at`
    </sql>

</mapper>