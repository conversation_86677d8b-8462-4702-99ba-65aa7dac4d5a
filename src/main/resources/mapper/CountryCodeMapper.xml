<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CountryCodeMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CountryCode">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="country_en" jdbcType="VARCHAR" property="countryEn"/>
        <result column="country_zh_cn" jdbcType="VARCHAR" property="countryZhCn"/>
        <result column="country_zh_tw" jdbcType="VARCHAR" property="countryZhTw"/>
        <result column="phone_code" jdbcType="VARCHAR" property="phoneCode"/>
        <result column="short_code" jdbcType="VARCHAR" property="shortCode"/>
        <result column="status" jdbcType="BIT" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `country_en`, `country_zh_cn`, `country_zh_tw`, `phone_code`, `short_code`, `status`
    </sql>

</mapper>