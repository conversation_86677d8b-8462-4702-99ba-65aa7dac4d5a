<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.BusinessDayInOutLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.BusinessDayInOutLog">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="in" jdbcType="DECIMAL" property="in"/>
        <result column="out" jdbcType="DECIMAL" property="out"/>
        <result column="in_fee" jdbcType="DECIMAL" property="inFee"/>
        <result column="out_fee" jdbcType="DECIMAL" property="outFee"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `business_id`, `in`, `out`, `in_fee`, `out_fee`, `stat_date`
    </sql>

</mapper>