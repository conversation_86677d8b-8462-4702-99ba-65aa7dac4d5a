<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserPaymentConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserPaymentConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="payment_config_id" jdbcType="INTEGER" property="paymentConfigId"/>
        <result column="total_fee" jdbcType="DECIMAL" property="totalFee"/>
        <result column="fee_rate" jdbcType="DECIMAL" property="feeRate"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `payment_config_id`, `total_fee`, `fee_rate`, `business_id`
    </sql>

</mapper>