<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.PaymentPayeeAccountMapper">
    <resultMap id="BaseResultMap" type="generator.domain.PaymentPayeeAccount">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_id" jdbcType="VARCHAR" property="accountId"/>
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="account_no" jdbcType="VARCHAR" property="accountNo"/>
        <result column="register_url" jdbcType="VARCHAR" property="registerUrl"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="payee_id" jdbcType="VARCHAR" property="payeeId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `account_id`, `payment_method`, `account_no`, `register_url`, `status`, `payee_id`, `create_time`,
        `business_id`
    </sql>

</mapper>