<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserWalletLogMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserWalletLog">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="user_id" jdbcType="OTHER" property="userId"/>
        <result column="business_id" jdbcType="OTHER" property="businessId"/>
        <result column="action" jdbcType="OTHER" property="action"/>
        <result column="wallet_type" jdbcType="OTHER" property="walletType"/>
        <result column="change_before" jdbcType="DECIMAL" property="changeBefore"/>
        <result column="change_money" jdbcType="DECIMAL" property="changeMoney"/>
        <result column="change_after" jdbcType="DECIMAL" property="changeAfter"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="target_id" jdbcType="OTHER" property="targetId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `user_id`, `business_id`, `action`, `wallet_type`, `change_before`, `change_money`, `change_after`,
        `note`, `target_id`, `create_time`, `order_id`
    </sql>

</mapper>