<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserCardFeeConfigMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserCardFeeConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="card_manage_id" jdbcType="INTEGER" property="cardManageId"/>
        <result column="card_bin" jdbcType="VARCHAR" property="cardBin"/>
        <result column="is_able" jdbcType="INTEGER" property="isAble"/>
        <result column="cost_open_card_fee" jdbcType="DECIMAL" property="costOpenCardFee"/>
        <result column="cost_charge_rate" jdbcType="DECIMAL" property="costChargeRate"/>
        <result column="cost_cancel_card_fee" jdbcType="DECIMAL" property="costCancelCardFee"/>
        <result column="platform_open_card_fee" jdbcType="DECIMAL" property="platformOpenCardFee"/>
        <result column="platform_charge_rate" jdbcType="DECIMAL" property="platformChargeRate"/>
        <result column="platform_cancel_card_fee" jdbcType="DECIMAL" property="platformCancelCardFee"/>
        <result column="business_open_card_fee" jdbcType="DECIMAL" property="businessOpenCardFee"/>
        <result column="business_charge_rate" jdbcType="DECIMAL" property="businessChargeRate"/>
        <result column="business_cancel_card_fee" jdbcType="DECIMAL" property="businessCancelCardFee"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="number_of_cards_that_can_be_opened" jdbcType="INTEGER" property="numberOfCardsThatCanBeOpened"/>
        <result column="first_recharge" jdbcType="DECIMAL" property="firstRecharge"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `business_id`, `card_manage_id`, `card_bin`, `is_able`, `cost_open_card_fee`, `cost_charge_rate`,
        `cost_cancel_card_fee`, `platform_open_card_fee`, `platform_charge_rate`, `platform_cancel_card_fee`,
        `business_open_card_fee`, `business_charge_rate`, `business_cancel_card_fee`, `create_time`, `update_time`,
        `create_by`, `update_by`, `number_of_cards_that_can_be_opened`, `first_recharge`
    </sql>

</mapper>