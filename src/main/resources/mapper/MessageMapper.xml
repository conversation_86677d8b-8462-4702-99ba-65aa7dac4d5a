<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.MessageMapper">
    <resultMap id="BaseResultMap" type="generator.domain.Message">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="CLOB" property="content"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="lang" jdbcType="INTEGER" property="lang"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `title`, `content`, `status`, `create_time`, `update_time`, `lang`, `sort`
    </sql>

</mapper>