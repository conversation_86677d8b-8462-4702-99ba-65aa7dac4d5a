<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.UserCardManageMapper">
    <resultMap id="BaseResultMap" type="generator.domain.UserCardManage">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="card_source" jdbcType="VARCHAR" property="cardSource"/>
        <result column="card_category" jdbcType="INTEGER" property="cardCategory"/>
        <result column="card_name" jdbcType="VARCHAR" property="cardName"/>
        <result column="card_belong_to" jdbcType="VARCHAR" property="cardBelongTo"/>
        <result column="card_open_fee" jdbcType="DECIMAL" property="cardOpenFee"/>
        <result column="card_charge_rate" jdbcType="DECIMAL" property="cardChargeRate"/>
        <result column="card_cancel_fee" jdbcType="DECIMAL" property="cardCancelFee"/>
        <result column="card_lei" jdbcType="VARCHAR" property="cardLei"/>
        <result column="card_changjing" jdbcType="CLOB" property="cardChangjing"/>
        <result column="card_source_country" jdbcType="VARCHAR" property="cardSourceCountry"/>
        <result column="card_duan" jdbcType="VARCHAR" property="cardDuan"/>
        <result column="card_month_fee" jdbcType="DECIMAL" property="cardMonthFee"/>
        <result column="card_danci_xiaofei_max" jdbcType="DECIMAL" property="cardDanciXiaofeiMax"/>
        <result column="card_month_xiaofei_max" jdbcType="DECIMAL" property="cardMonthXiaofeiMax"/>
        <result column="card_chuzhi_max" jdbcType="DECIMAL" property="cardChuzhiMax"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="card_img" jdbcType="VARCHAR" property="cardImg"/>
        <result column="card_model" jdbcType="OTHER" property="cardModel"/>
        <result column="card_status" jdbcType="OTHER" property="cardStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `card_source`, `card_category`, `card_name`, `card_belong_to`, `card_open_fee`, `card_charge_rate`,
        `card_cancel_fee`, `card_lei`, `card_changjing`, `card_source_country`, `card_duan`, `card_month_fee`,
        `card_danci_xiaofei_max`, `card_month_xiaofei_max`, `card_chuzhi_max`, `create_by`, `create_time`, `update_by`,
        `update_time`, `card_img`, `card_model`, `card_status`
    </sql>

</mapper>