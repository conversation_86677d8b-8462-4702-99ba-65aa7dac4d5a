<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CoinInOutMapper">
    <resultMap id="BaseResultMap" type="generator.domain.CoinInOut">
        <id column="id" jdbcType="OTHER" property="id"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="trans_type" jdbcType="OTHER" property="transType"/>
        <result column="coin_type" jdbcType="OTHER" property="coinType"/>
        <result column="from_address" jdbcType="VARCHAR" property="fromAddress"/>
        <result column="to_address" jdbcType="VARCHAR" property="toAddress"/>
        <result column="hash" jdbcType="VARCHAR" property="hash"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="status" jdbcType="OTHER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `business_id`, `trans_type`, `coin_type`, `from_address`, `to_address`, `hash`, `amount`, `fee`, `status`,
        `create_time`, `update_time`
    </sql>

</mapper>